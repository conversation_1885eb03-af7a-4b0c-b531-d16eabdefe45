Couldn't read values in CFPrefsPlistSource<0x300ad7280> (Domain: group.com.bop.bopmaps.onesignal, User: kCFPreferencesAnyUser, ByHost: Yes, Container: (null), Contents Need Refresh: Yes): Using kCFPreferencesAnyUser with a container is only allowed for System Containers, detaching from cfprefsd
FlutterView implements focusItemsInRect: - caching for linear focus movement is limited as long as this view is on screen.
flutter: The Dart VM service is listening on http://127.0.0.1:49914/oU3OqdKva1U=/
applicationQueuePlayer _establishConnectionIfNeeded timeout [ping did not pong]
nw_connection_copy_connected_local_endpoint_block_invoke [C3] Client called nw_connection_copy_connected_local_endpoint on unconnected nw_connection
nw_connection_copy_connected_remote_endpoint_block_invoke [C3] Client called nw_connection_copy_connected_remote_endpoint on unconnected nw_connection
nw_connection_copy_protocol_metadata_internal_block_invoke [C3] Client called nw_connection_copy_protocol_metadata_internal on unconnected nw_connection
quic_conn_process_inbound [C1.1.1.1:2] [-0179764aa8421fdb2f78284a594204554f6dd202] unable to parse packet
flutter: Environment variables loaded successfully
flutter: 📍 Starting location services pre-initialization...
flutter: 📍 Location services enabled: true
flutter: 📍 Initial location permission status: LocationPermission.whileInUse
flutter: 🗺️ LocationManager: Initializing singleton instance
flutter: 🗺️ LocationManager: Checking initial location status
flutter: 📍 LocationManager initialized: 616345308
flutter: 📍 Attempting initial location fix (will timeout after 2 seconds)...
flutter: 🗺️ LocationManager: Initial permission check result: LocationPermission.whileInUse
flutter: 🗺️ LocationManager: Status changed from LocationStatus.notDetermined to LocationStatus.available
flutter: 🗺️ LocationManager: Have permission, requesting current location
flutter: 🗺️ LocationManager: Requesting current location (request #1)
flutter: 🗺️ LocationManager: Status changed from LocationStatus.available to LocationStatus.determining
flutter: 🗺️ LocationManager: Location request started at T+4ms since app startup
flutter: 📍 STARTUP LOCATION: Lat: 36.**************, Lng: -86.**************
flutter: 🚀 App initialization complete, launching UI...
flutter: 🔐 Auth state: token=false, user=false, isAuthenticated=false
flutter: 🔐 Auth state: token=false, user=false, isAuthenticated=false
flutter: 🔔 Initializing OneSignal with App ID: ************************************ for user: 79
VERBOSE: setAppId called with appId: ************************************!
VERBOSE: setLaunchOptions() called with launchOptions: (null)!
VERBOSE: launchOptions is set and appId of ************************************ is set, initializing OneSignal...
VERBOSE: In app message click listener added successfully
VERBOSE: In app message lifecycle listener added successfully
VERBOSE: OneSignal.User login called with externalId: 79
VERBOSE: OneSignalUserManager.createNewUser: not creating new user due to logging into the same user.)
VERBOSE: Notification click listener added successfully
VERBOSE: requestPermission Called
VERBOSE: Firing registerForRemoteNotifications
flutter: 🔐 Auth state: token=true, user=true, isAuthenticated=true
flutter: 👤 Current user: mothusom68 (<EMAIL>)
flutter: 🎯 Display name computed: mothusom68
flutter: 🔑 Token starts with: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************.ppRGTJE5co8Kk65n-k-URqeeAyeuTW36IPXdbC3cuyU
flutter: 🔐 Auth state: token=true, user=true, isAuthenticated=true
flutter: 👤 Current user: mothusom68 (<EMAIL>)
flutter: 🎯 Display name computed: mothusom68
flutter: 🔑 Token starts with: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************.ppRGTJE5co8Kk65n-k-URqeeAyeuTW36IPXdbC3cuyU
flutter: 🗺️ Navigating to map screen
flutter: 🔐 Auth state: token=true, user=true, isAuthenticated=true
flutter: 👤 Current user: mothusom68 (<EMAIL>)
flutter: 🎯 Display name computed: mothusom68
flutter: 🔑 Token starts with: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************.ppRGTJE5co8Kk65n-k-URqeeAyeuTW36IPXdbC3cuyU
flutter: 🔐 Auth check for map route - isAuthenticated: true
flutter: 🚀 MapScreen: Time to init: 3ms
flutter: 🗺️ LocationManager: Starting foreground location tracking
flutter: 🗺️ LocationManager: Requesting location permission
flutter: MapProvider: Periodic refresh timer disabled - using SmartLocationFetchManager instead
flutter: 🚀 MapScreen: Requesting location permission
flutter: 🗺️ LocationManager: Requesting location permission
flutter: 🎵 [SpotifyProvider] Initializing...
flutter: 🍎 Initializing Apple Music provider...
flutter: 🍎 Initializing Apple Music service...
flutter: 🎵 [MapScreen] Build - currentlyPlayingPinId: null
flutter: 🎵 [MapScreen] Build - hasCurrentlyPlayingPin: false
flutter: 🎵 [MapScreen] _buildCaptionPopup called
flutter: 🎵 [MapScreen] - currentlyPlayingPinId: null
flutter: 🎵 [MapScreen] - currentlyPlayingPinData: null
flutter: 🎵 [MapScreen] ❌ No currently playing pin or pin data
flutter: NavigationHelper: Getting current tab...
flutter: NavigationHelper: Checking route - name: /map, looking for: /profile, match: false
flutter: NavigationHelper: Checking route - name: /map, looking for: /explore, match: false
flutter: NavigationHelper: Checking route - name: /map, looking for: /friends, match: false
flutter: NavigationHelper: Checking route - name: /map, looking for: /challenges, match: false
flutter: NavigationHelper: Checking route - name: /map, looking for: /map, match: true
flutter: NavigationHelper: Current tab is MAP (explicit)
flutter: 🌧️ Setting weather type to: rain
flutter: 🌧️ Weather visual effects initialized
flutter: 🌧️ Checking location and updating weather...
flutter: 🌍 Location monitoring started
flutter: 📍 Layer State Machine initialized
flutter: 🟡 [MAP-INIT] === REGISTERING CALLBACKS ===
flutter: 🟡 [MAP-INIT] MapProvider via Provider.of: Instance of 'MapProvider'
flutter: 🟡 [MAP-INIT] MapProvider hashCode: 826651983
flutter: 🟡 [MAP-INIT] Local _mapProvider: null
flutter: 🟡 [MAP-INIT] Local _mapProvider hashCode: null
flutter: MapProvider: Force refresh callback registered
flutter: MapProvider: Optimistic pin callback registered
flutter: 🟡 [MAP-INIT] ✅ Callbacks registered on Provider.of instance!
flutter: 🟡 [MAP-INIT] === CALLBACK REGISTRATION COMPLETE ===
flutter: 📍 Pin cache cleared (force: true, size was: 0)
flutter: 📍 Skipping pin bounce animation during initial load
flutter: 🔄 VisualEffectsManager: Screen visibility changed to true
flutter: 🔄 Previous state - visible: true, foreground: true
flutter: 🔄 Screen became visible and app in foreground - resuming animations
flutter: 🔄 VisualEffectsManager: Resuming animation timers...
flutter: 🔄 Current map style: MapStyle.minimal
flutter: 🔄 Weather enabled: true
flutter: 🔄 Screen visible: true
flutter: 🔄 App in foreground: true
flutter: 🔄 Restarting weather effects timer...
flutter: 🌧️ Setting weather type to: rain
flutter: Map Screen: didPush - screen is now visible
flutter: 🌧️ Initial screen size update: 390.0x844.0
flutter: 📍 MapProvider update ignored - map not ready
flutter: 👤 MapScreen: User profile not loaded, initializing...
flutter: 🚀 Initializing UserProvider...
flutter: 🎨 Loaded saved map style: MapStyle.neon
flutter: 🎭 Loaded saved effects state - weather: true
flutter: 🎯 Getting rank for level 1
flutter: 📊 Selected rank: Basement Bopper (required level: 1)
flutter: ✅ Loaded initial cached data: Level 1, XP 25
flutter: 🎯 Getting rank for level 1
flutter: 📊 Selected rank: Basement Bopper (required level: 1)
flutter: ✅ Loaded initial cached data: Level 1, XP 25
flutter:
flutter: 📦 Loading cached user progress:
flutter: - Cache age: 6 minutes
flutter: - Cached Level: 1
flutter: - Cached XP: 25
flutter: - Cached Progress: 5.0
flutter: 🎯 Getting rank for level 1
flutter: 📊 Selected rank: Basement Bopper (required level: 1)
flutter: ✅ Using cached user progress (age: 6m)
flutter: 🔐 Auth state: token=true, user=true, isAuthenticated=true
flutter: 👤 Current user: mothusom68 (<EMAIL>)
flutter: 🎯 Display name computed: mothusom68
flutter: 🔑 Token starts with: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************.ppRGTJE5co8Kk65n-k-URqeeAyeuTW36IPXdbC3cuyU
flutter: 🤖 MapScreen: Initializing Global AI Provider for authenticated user...
flutter: 🔐 Auth state: token=true, user=true, isAuthenticated=true
flutter: 👤 Current user: mothusom68 (<EMAIL>)
flutter: 🎯 Display name computed: mothusom68
flutter: 🔑 Token starts with: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************.ppRGTJE5co8Kk65n-k-URqeeAyeuTW36IPXdbC3cuyU
flutter: 🤖 Initializing Global AI Provider...
flutter: ⚙️ [LastFM] Exploration configured: depth=1, artists/level=4, decay=0.7
flutter: 🎧 [AIRecommendationService] Started playback monitoring
flutter: 👤 User profile not found, fetching now...
flutter: 🚀 [UserProvider] ===== STARTING fetchFullProfile =====
flutter: 🔍 Fetching full user profile from /api/users/me/
flutter: 🎵 Loaded 0 genres from user profile
flutter: 🎵 Profile genres: {}
flutter: 🎵 FINAL: 0 total genres from user profile
flutter: 📍 [AIRecommendationService] Context set: category=artistBased, genre=None
flutter: ⏱️ [Performance] Starting _loadArtistBasedRecommendationsOnly for suggested songs
flutter: 🎤 [Artist Recs] Getting current user artist recommendations...
flutter: 🏆 [SpotifyService] Getting top tracks (limit: 5)
flutter: ✅ Global AI Provider initialized successfully
flutter: 📍 MapProvider update ignored - map not ready
flutter: 💾 Loaded cached header data: mothusom68
flutter: 🔄 Fetching fresh data from network...
flutter: 📚 Loading all category achievements...
flutter: ✅ MapScreen: Global AI Provider initialized successfully
flutter: 💾 Loaded cached full profile: mothusom68
flutter: 🔍 Fetching user header data from /api/users/profile_header/
VERBOSE: oneSignalDidRegisterForRemoteNotifications:deviceToken:
INFO: Device Registered with Apple: 9479e4bb779b8918947baf5df1ff0348dae2c69168e0cd7f2279ec5d44b35c7c
VERBOSE: ForegroundLifecycleListener added successfully
VERBOSE: updateNotificationTypes called: 15
VERBOSE: Firing registerForRemoteNotifications
VERBOSE: startedRegister: 1
VERBOSE: getNotificationTypes:mSubscriptionStatus: -1
VERBOSE: network request (OSRequestGetInAppMessages) with URL https://api.onesignal.com/apps/************************************/subscriptions/************************************/iams and headers: {
    "OneSignal-RYW-Token" = 10000000011142951436;
    "OneSignal-Session-Duration" = 773;
}
VERBOSE: network response (OSRequestGetInAppMessages) with URL https://api.onesignal.com/apps/************************************/subscriptions/************************************/iams: {
    headers =     {
        "Access-Control-Allow-Origin" = "*";
        "Alt-Svc" = "h3=\":443\"; ma=86400";
        "Content-Length" = 22;
        "Content-Type" = "application/json; charset=utf-8";
        Date = "Sat, 02 Aug 2025 02:30:30 GMT";
        Priority = "u=3,i=?0";
        Server = cloudflare;
        "Strict-Transport-Security" = "max-age=15552000; includeSubDomains";
        Via = "1.1 google";
        "access-control-allow-headers" = "SDK-Version,Content-Type,Origin,Authorization,OneSignal-Subscription-Id";
        "cf-cache-status" = DYNAMIC;
        "cf-ray" = "968a2756da2a56c8-IAD";
        "server-timing" = cfExtPri;
        traceparent = "00-4d2beb8767abd82de625ed177f372855-d1b7fc0ed9a768f6-00";
    };
    httpStatusCode = 200;
    "in_app_messages" =     (
    );
}
MLNMapView WARNING UIViewController.automaticallyAdjustsScrollViewInsets is deprecated use MLNMapView.automaticallyAdjustContentInset instead.
VERBOSE: getInAppMessagesFromServer success
VERBOSE: updateInAppMessagesFromServer
VERBOSE: resetRedisplayMessagesBySession with redisplayedInAppMessages: {
}
VERBOSE: Evaluating in app messages
flutter: ⚡ Resuming lightning effects...
flutter: 🔄 All animation timers resumed successfully
flutter: 🚀 Parallel pin feature isolate initialized successfully
flutter: 🚀 Cluster isolate initialized successfully
flutter: 🔔 OneSignal notification permission: true
flutter: 🔔 OneSignal user info updated:
flutter:    Push Subscription ID: ************************************
flutter:    Push Token: 9479e4bb779b8918947baf5df1ff0348dae2c69168e0cd7f2279ec5d44b35c7c
flutter: ⚠️ OneSignal player registration skipped - no API service
flutter: ✅ OneSignal initialized successfully for user: 79
flutter: 🔔 OneSignal tags added: {user_id: 79, username: mothusom68, email: <EMAIL>, is_verified: false, has_spotify: false, has_apple_music: false, has_soundcloud: false}
flutter: ✅ OneSignal external user ID set (direct): 79
flutter: 📍 SmartFetch: Location permission granted - will refresh on next location update
VERBOSE: oneSignalDidRegisterForRemoteNotifications:deviceToken:
INFO: Device Registered with Apple: 9479e4bb779b8918947baf5df1ff0348dae2c69168e0cd7f2279ec5d44b35c7c
VERBOSE: OneSignal.User addTags called with: ["email": "<EMAIL>", "is_verified": "false", "username": "mothusom68", "has_apple_music": "false", "has_spotify": "false", "user_id": "79", "has_soundcloud": "false"]
VERBOSE: OSOperationRepo enqueueDelta: <OSDelta OS_UPDATE_PROPERTIES_DELTA with property: tags value: ["email": "<EMAIL>", "is_verified": "false", "username": "mothusom68", "has_apple_music": "false", "has_spotify": "false", "user_id": "79", "has_soundcloud": "false"]>
NSPredicate: Use of 'mgl_does:have:' as an NSExpression function is forbidden.
flutter: ❌ [SpotifyService] Error getting top tracks: Exception: Not connected to Spotify
flutter: 🎤 [Artist Recs] Got 0 favorite artists from top tracks: []
flutter: 🎤 [Artist Recs] Falling back to user top artists: []
flutter: ❌ [Artist Recs] No artists available for recommendations
flutter: ⚠️ No artist-based tracks found, using fallback
flutter: 🎭 Loading mood-based recommendations for: Happy
flutter: 🎭 Getting mood recommendations for: happy
flutter: 🎵 Searching for mood playlists: "happy mood playlist"
flutter: 🔍 [SpotifyService] Searching for playlists: "happy mood playlist" (limit: 25, offset: 0)
flutter: Loading completed summary using optimized endpoint
flutter:
flutter: 🚀 === API REQUEST ===
flutter: 📤 GET https://api.bopmaps.com/api/gamification/user-achievements/completed_summary/
flutter: 📋 Headers: {Content-Type: application/json, Accept: application/json, Authorization: Bearer [HIDDEN]}
flutter: ⏰ Timestamp: 2025-08-01T21:30:30.490960
flutter:
flutter: Loading achievements for category: location using optimized endpoints
flutter:
flutter: 🚀 === API REQUEST ===
flutter: 📤 GET https://api.bopmaps.com/api/gamification/user-achievements/completed_by_category/?category=location
flutter: 📋 Headers: {Content-Type: application/json, Accept: application/json, Authorization: Bearer [HIDDEN]}
flutter: ⏰ Timestamp: 2025-08-01T21:30:30.491882
flutter:
flutter:
flutter: 🚀 === API REQUEST ===
flutter: 📤 GET https://api.bopmaps.com/api/gamification/user-achievements/in_progress_by_category/?category=location
flutter: 📋 Headers: {Content-Type: application/json, Accept: application/json, Authorization: Bearer [HIDDEN]}
flutter: ⏰ Timestamp: 2025-08-01T21:30:30.492319
flutter:
flutter:
flutter: 🚀 === API REQUEST ===
flutter: 📤 GET https://api.bopmaps.com/api/gamification/achievements/rank_badge_data/
flutter: 📋 Headers: {Content-Type: application/json, Accept: application/json, Authorization: Bearer [HIDDEN]}
flutter: ⏰ Timestamp: 2025-08-01T21:30:30.493041
flutter:
flutter: Loading achievements for category: social using optimized endpoints
flutter:
flutter: 🚀 === API REQUEST ===
flutter: 📤 GET https://api.bopmaps.com/api/gamification/user-achievements/completed_by_category/?category=social
flutter: 📋 Headers: {Content-Type: application/json, Accept: application/json, Authorization: Bearer [HIDDEN]}
flutter: ⏰ Timestamp: 2025-08-01T21:30:30.493575
flutter:
flutter:
flutter: 🚀 === API REQUEST ===
flutter: 📤 GET https://api.bopmaps.com/api/gamification/user-achievements/in_progress_by_category/?category=social
flutter: 📋 Headers: {Content-Type: application/json, Accept: application/json, Authorization: Bearer [HIDDEN]}
flutter: ⏰ Timestamp: 2025-08-01T21:30:30.493950
flutter:
flutter:
flutter: 🚀 === API REQUEST ===
flutter: 📤 GET https://api.bopmaps.com/api/gamification/user-achievements/
flutter: 📋 Headers: {Content-Type: application/json, Accept: application/json, Authorization: Bearer [HIDDEN]}
flutter: ⏰ Timestamp: 2025-08-01T21:30:30.494364
flutter:
flutter: Loading achievements for category: genre using optimized endpoints
flutter:
flutter: 🚀 === API REQUEST ===
flutter: 📤 GET https://api.bopmaps.com/api/gamification/user-achievements/completed_by_category/?category=genre
flutter: 📋 Headers: {Content-Type: application/json, Accept: application/json, Authorization: Bearer [HIDDEN]}
flutter: ⏰ Timestamp: 2025-08-01T21:30:30.494999
flutter:
flutter:
flutter: 🚀 === API REQUEST ===
flutter: 📤 GET https://api.bopmaps.com/api/gamification/user-achievements/in_progress_by_category/?category=genre
flutter: 📋 Headers: {Content-Type: application/json, Accept: application/json, Authorization: Bearer [HIDDEN]}
flutter: ⏰ Timestamp: 2025-08-01T21:30:30.495259
flutter:
flutter: Loading achievements for category: artist using optimized endpoints
flutter:
flutter: 🚀 === API REQUEST ===
flutter: 📤 GET https://api.bopmaps.com/api/gamification/user-achievements/completed_by_category/?category=artist
flutter: 📋 Headers: {Content-Type: application/json, Accept: application/json, Authorization: Bearer [HIDDEN]}
flutter: ⏰ Timestamp: 2025-08-01T21:30:30.495757
flutter:
flutter:
flutter: 🚀 === API REQUEST ===
flutter: 📤 GET https://api.bopmaps.com/api/gamification/user-achievements/in_progress_by_category/?category=artist
flutter: 📋 Headers: {Content-Type: application/json, Accept: application/json, Authorization: Bearer [HIDDEN]}
flutter: ⏰ Timestamp: 2025-08-01T21:30:30.495977
flutter:
flutter: 🌧️ Found last known position: 36.**************, -86.************** (0.0 minutes old)
flutter: 🌧️ Using recent last known position
flutter: 🌧️ Got position: 36.1467, -86.8080
flutter: 🌧️ Updating weather for location: 36.1467,-86.8080
flutter: 🌧️ Fetching weather data for: 36.1467,-86.8080
flutter: 🌧️ Making weather API request to: http://api.weatherapi.com/v1/current.json?key=41051434ead448cdba5191912252206&q=36.1467,-86.8080&aqi=no
flutter: 🗺️ Map created successfully
flutter: Map style loaded successfully
flutter: 🧹 Cleaned up all style-specific layers and sources
flutter: 🔌 WebSocket: Disconnected
flutter: 🔐 Auth state: token=true, user=true, isAuthenticated=true
flutter: 👤 Current user: mothusom68 (<EMAIL>)
flutter: 🎯 Display name computed: mothusom68
flutter: 🔑 Token starts with: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************.ppRGTJE5co8Kk65n-k-URqeeAyeuTW36IPXdbC3cuyU
flutter: 🔐 Auth state: token=true, user=true, isAuthenticated=true
flutter: 👤 Current user: mothusom68 (<EMAIL>)
flutter: 🎯 Display name computed: mothusom68
flutter: 🔑 Token starts with: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************.ppRGTJE5co8Kk65n-k-URqeeAyeuTW36IPXdbC3cuyU
flutter: 🎵 [MapScreen] Build - currentlyPlayingPinId: null
flutter: 🎵 [MapScreen] Build - hasCurrentlyPlayingPin: false
flutter: 🎵 [MapScreen] _buildCaptionPopup called
flutter: 🎵 [MapScreen] - currentlyPlayingPinId: null
flutter: 🎵 [MapScreen] - currentlyPlayingPinData: null
flutter: 🎵 [MapScreen] ❌ No currently playing pin or pin data
flutter: NavigationHelper: Getting current tab...
flutter: NavigationHelper: Checking route - name: /map, looking for: /profile, match: false
flutter: NavigationHelper: Checking route - name: /map, looking for: /explore, match: false
flutter: NavigationHelper: Checking route - name: /map, looking for: /friends, match: false
flutter: NavigationHelper: Checking route - name: /map, looking for: /challenges, match: false
flutter: NavigationHelper: Checking route - name: /map, looking for: /map, match: true
flutter: NavigationHelper: Current tab is MAP (explicit)
flutter: 🎯 Getting rank for level 1
flutter: 📊 Selected rank: Basement Bopper (required level: 1)
flutter: ✅ Loaded initial cached data: Level 1, XP 25
flutter:
flutter: 📦 Loading cached user progress:
flutter: - Cache age: 6 minutes
flutter: - Cached Level: 1
flutter: - Cached XP: 25
flutter: - Cached Progress: 5.0
flutter: 🎯 Getting rank for level 1
flutter: 📊 Selected rank: Basement Bopper (required level: 1)
flutter: ✅ Using cached user progress (age: 6m)
flutter: 🔄 Fetching fresh data from network...
flutter: 📚 Loading all category achievements...
flutter: ✅ Openmaptiles source already exists
flutter: ✅ Openmaptiles source already exists
flutter: Customizing map style for Snapchat look...
flutter: 🛣️ Added realistic road surfaces
flutter: 🔄 [STYLE-CHANGE] Re-registering callbacks after style change...
flutter: MapProvider: Optimistic pin callback registered
flutter: MapProvider: Force refresh callback registered
flutter: 🔄 [STYLE-CHANGE] ✅ Callbacks re-registered successfully!
flutter: 🔄 [STYLE-CHANGE] MapProvider hashCode: 826651983
flutter: 📍 Pin cache cleared (force: true, size was: 0)
flutter: ✅ Map fully initialized and ready
flutter: 🗺️ LocationManager: Permission request result: LocationPermission.whileInUse
flutter: 🗺️ LocationManager: Status changed from LocationStatus.determining to LocationStatus.available
flutter: 📍 MapProvider update ignored - map not ready
flutter: 🗺️ LocationManager: Requesting current location (request #2)
flutter: 🗺️ LocationManager: Status changed from LocationStatus.available to LocationStatus.determining
flutter: 🗺️ LocationManager: Location request started at T+1161ms since app startup
flutter: 📍 MapProvider update ignored - map not ready
flutter: 📍 MapProvider update ignored - map not ready
flutter: 📍 MapProvider update ignored - map not ready
flutter: 🗺️ LocationManager: Permission request result: LocationPermission.whileInUse
flutter: 🗺️ LocationManager: Status changed from LocationStatus.determining to LocationStatus.available
flutter: 📍 MapProvider update ignored - map not ready
flutter: 📍 MapProvider update ignored - map not ready
flutter: 🎵 [SpotifyProvider] Authentication check result: false
flutter: ❌ [SpotifyProvider] No valid tokens found, user needs to authenticate
flutter: ✅ [SpotifyProvider] Initialization completed, connected: false
flutter: 🎵 [MapScreen] _onMusicSourceChanged() called
flutter: 🎵 [MapScreen] Early return - mounted: true, hasPin: false
NSPredicate: Use of 'MLN_IF' as an NSExpression function is forbidden.
NSPredicate: Use of 'mgl_does:have:' as an NSExpression function is forbidden.
flutter: 📍 Received position update: lat: 36.**************, lng: -86.**************, accuracy: 10.476811082671006m, speed: 0.0m/s
flutter: 🧭 Compass stream initialized
flutter: No existing park layer to remove
flutter: Successfully added custom parks layer
flutter: Successfully added grass layer
flutter: Successfully added forest layer
flutter:
flutter: 🚀 === API REQUEST ===
flutter: 📤 GET https://api.bopmaps.com/api/gamification/achievements/rank_badge_data/
flutter: 📋 Headers: {Content-Type: application/json, Accept: application/json, Authorization: Bearer [HIDDEN]}
flutter: ⏰ Timestamp: 2025-08-01T21:30:30.620487
flutter:
flutter: Loading completed summary using optimized endpoint
flutter:
flutter: 🚀 === API REQUEST ===
flutter: 📤 GET https://api.bopmaps.com/api/gamification/user-achievements/completed_summary/
flutter: 📋 Headers: {Content-Type: application/json, Accept: application/json, Authorization: Bearer [HIDDEN]}
flutter: ⏰ Timestamp: 2025-08-01T21:30:30.621339
flutter:
flutter: Loading achievements for category: genre using optimized endpoints
flutter:
flutter: 🚀 === API REQUEST ===
flutter: 📤 GET https://api.bopmaps.com/api/gamification/user-achievements/completed_by_category/?category=genre
flutter: 📋 Headers: {Content-Type: application/json, Accept: application/json, Authorization: Bearer [HIDDEN]}
flutter: ⏰ Timestamp: 2025-08-01T21:30:30.622497
flutter:
flutter:
flutter: 🚀 === API REQUEST ===
flutter: 📤 GET https://api.bopmaps.com/api/gamification/user-achievements/in_progress_by_category/?category=genre
flutter: 📋 Headers: {Content-Type: application/json, Accept: application/json, Authorization: Bearer [HIDDEN]}
flutter: ⏰ Timestamp: 2025-08-01T21:30:30.623170
flutter:
flutter:
flutter: 🚀 === API REQUEST ===
flutter: 📤 GET https://api.bopmaps.com/api/gamification/user-achievements/
flutter: 📋 Headers: {Content-Type: application/json, Accept: application/json, Authorization: Bearer [HIDDEN]}
flutter: ⏰ Timestamp: 2025-08-01T21:30:30.624494
flutter:
flutter: Loading achievements for category: artist using optimized endpoints
flutter:
flutter: 🚀 === API REQUEST ===
flutter: 📤 GET https://api.bopmaps.com/api/gamification/user-achievements/completed_by_category/?category=artist
flutter: 📋 Headers: {Content-Type: application/json, Accept: application/json, Authorization: Bearer [HIDDEN]}
flutter: ⏰ Timestamp: 2025-08-01T21:30:30.625889
flutter:
flutter:
flutter: 🚀 === API REQUEST ===
flutter: 📤 GET https://api.bopmaps.com/api/gamification/user-achievements/in_progress_by_category/?category=artist
flutter: 📋 Headers: {Content-Type: application/json, Accept: application/json, Authorization: Bearer [HIDDEN]}
flutter: ⏰ Timestamp: 2025-08-01T21:30:30.626234
flutter:
flutter: Successfully added custom water layer
flutter: Successfully added water outline
flutter: Loading achievements for category: location using optimized endpoints
flutter:
flutter: 🚀 === API REQUEST ===
flutter: 📤 GET https://api.bopmaps.com/api/gamification/user-achievements/completed_by_category/?category=location
flutter: 📋 Headers: {Content-Type: application/json, Accept: application/json, Authorization: Bearer [HIDDEN]}
flutter: ⏰ Timestamp: 2025-08-01T21:30:30.627721
flutter:
flutter:
flutter: 🚀 === API REQUEST ===
flutter: 📤 GET https://api.bopmaps.com/api/gamification/user-achievements/in_progress_by_category/?category=location
flutter: 📋 Headers: {Content-Type: application/json, Accept: application/json, Authorization: Bearer [HIDDEN]}
flutter: ⏰ Timestamp: 2025-08-01T21:30:30.628140
flutter:
flutter: Loading achievements for category: social using optimized endpoints
flutter:
flutter: 🚀 === API REQUEST ===
flutter: 📤 GET https://api.bopmaps.com/api/gamification/user-achievements/completed_by_category/?category=social
flutter: 📋 Headers: {Content-Type: application/json, Accept: application/json, Authorization: Bearer [HIDDEN]}
flutter: ⏰ Timestamp: 2025-08-01T21:30:30.628584
flutter:
flutter:
flutter: 🚀 === API REQUEST ===
flutter: 📤 GET https://api.bopmaps.com/api/gamification/user-achievements/in_progress_by_category/?category=social
flutter: 📋 Headers: {Content-Type: application/json, Accept: application/json, Authorization: Bearer [HIDDEN]}
flutter: ⏰ Timestamp: 2025-08-01T21:30:30.629128
flutter:
flutter: 🗺️ Map created successfully
flutter: Map style loaded successfully
flutter: 🧹 Cleaned up all style-specific layers and sources
flutter: 🎵 [MapScreen] Build - currentlyPlayingPinId: null
flutter: 🎵 [MapScreen] Build - hasCurrentlyPlayingPin: false
flutter: 🎵 [MapScreen] _buildCaptionPopup called
flutter: 🎵 [MapScreen] - currentlyPlayingPinId: null
flutter: 🎵 [MapScreen] - currentlyPlayingPinData: null
flutter: 🎵 [MapScreen] ❌ No currently playing pin or pin data
flutter: NavigationHelper: Getting current tab...
flutter: NavigationHelper: Checking route - name: /map, looking for: /profile, match: false
flutter: NavigationHelper: Checking route - name: /map, looking for: /explore, match: false
flutter: NavigationHelper: Checking route - name: /map, looking for: /friends, match: false
flutter: NavigationHelper: Checking route - name: /map, looking for: /challenges, match: false
flutter: NavigationHelper: Checking route - name: /map, looking for: /map, match: true
flutter: NavigationHelper: Current tab is MAP (explicit)
NSPredicate: Use of 'mgl_does:have:' as an NSExpression function is forbidden.
SentryFlutterReplayScreenshotProvider received null result. Cannot capture a replay screenshot.
flutter: ✅ Openmaptiles source already exists
flutter: ✅ Openmaptiles source already exists
flutter: Customizing map style for Snapchat look...
flutter: 🛣️ Added realistic road surfaces
flutter: 🔄 [STYLE-CHANGE] Re-registering callbacks after style change...
flutter: MapProvider: Optimistic pin callback registered
flutter: MapProvider: Force refresh callback registered
flutter: 🔄 [STYLE-CHANGE] ✅ Callbacks re-registered successfully!
flutter: 🔄 [STYLE-CHANGE] MapProvider hashCode: 826651983
flutter: 📍 Pin cache cleared (force: true, size was: 0)
flutter: ✅ Map fully initialized and ready
NSPredicate: Use of 'MLN_IF' as an NSExpression function is forbidden.
flutter: Successfully added Snapchat-style tree icon
flutter: ❌ [SpotifyService] No access token found in either storage location
flutter: 🎨 Enhanced user avatar added successfully
flutter: No existing park layer to remove
flutter: Successfully added custom parks layer
flutter: Successfully added grass layer
flutter: Successfully added forest layer
flutter: Successfully added custom water layer
flutter: Successfully added water outline
NSPredicate: Use of 'mgl_does:have:' as an NSExpression function is forbidden.
flutter: Successfully added Snapchat-style tree icon
NSPredicate: Use of 'mgl_does:have:' as an NSExpression function is forbidden.
flutter: 🗺️ Symbol settings configured
flutter: 🗺️ Symbol tap listener registered
flutter: 🗺️ Camera position listener added
flutter: 🎨 Enhanced user avatar added successfully
flutter: 🗺️ Initial zoom set to: 16.5
flutter: 📍 User avatar image already added, skipping
flutter: ✅ Created new user avatar symbol
flutter: 📍 User avatar image already added, skipping
flutter: ❌ [SpotifyService] No access token found in either storage location
flutter: 🔄 [SpotifyService] Created new HTTP client
flutter: ✅ Loaded stored Apple Music authentication
flutter: ✅ Apple Music service initialized. Authenticated: true
flutter: 🍎 [AppleMusicPlayerService] Apple Music service injected
flutter: 🍎 Initializing Apple Music player service...
flutter: 🍎 Initializing Apple Music service...
flutter: 📍 _updateMapPins called - updating from cache only (no API calls)
flutter: 📍 No cached pins available for display
flutter: Successfully added tree symbols to parks
flutter: Successfully added tree symbols to landcover
flutter: 📍 _updateMapPins called - updating from cache only (no API calls)
flutter: 📍 No cached pins available for display
flutter: Error adding tree symbols: PlatformException(layerAlreadyExists, Layer already exists, Layer with id park-trees already exists., null)
flutter: 🌧️ Weather API response received successfully
flutter: 🌧️ Weather API Response: {location: {name: Nashville, region: Tennessee, country: United States of America, lat: 36.166, lon: -86.784, tz_id: America/Chicago, localtime_epoch: 1754101838, localtime: 2025-08-01 21:30}, current: {last_updated_epoch: 1754101800, last_updated: 2025-08-01 21:30, temp_c: 24.4, temp_f: 75.9, is_day: 0, condition: {text: Overcast, icon: //cdn.weatherapi.com/weather/64x64/night/122.png, code: 1009}, wind_mph: 8.1, wind_kph: 13.0, wind_degree: 32, wind_dir: NNE, pressure_mb: 1023.0, pressure_in: 30.2, precip_mm: 0.0, precip_in: 0.0, humidity: 69, cloud: 100, feelslike_c: 26.4, feelslike_f: 79.5, windchill_c: 23.4, windchill_f: 74.2, heatindex_c: 25.4, heatindex_f: 77.8, dewpoint_c: 19.6, dewpoint_f: 67.2, vis_km: 16.0, vis_miles: 9.0, uv: 0.0, gust_mph: 11.9, gust_kph: 19.2}}
flutter: 🌧️ Weather details: text="overcast", code=1009, precip=0.0mm, isDay=false
flutter: 🌧️ Wind: 13.0kph at 32° -> offset(22.049250500067075, 13.777900870063327)
flutter: 🌧️ ═══ WEATHER DETAILS ═══
flutter: 🌧️ Location: Nashville, Tennessee, United States of America
flutter: 🌧️ Temperature: 24.4°C (75.9°F)
flutter: 🌧️ Condition: Overcast (code: 1009)
flutter: 🌧️ Precipitation: 0.0mm
flutter: 🌧️ Wind: 13.0kph at 32° (NNE)
flutter: 🌧️ Humidity: 69%
flutter: 🌧️ Cloud Cover: 100%
flutter: 🌧️ Visibility: 16.0km
flutter: 🌧️ UV Index: 0.0
flutter: 🌧️ Is Day: No
flutter: 🌧️ ═══════════════════════
flutter: 🌧️ Parsed Weather Type: clouds
flutter: 🌧️ Wind Vector: (22.0, 13.8)
flutter: 🌧️ Setting weather type to: clouds
flutter: 🎨 Applying neon effects...
flutter: 🏢 Applying neon building colors...
flutter: 🏢 Removed all building layers to prevent z-fighting
flutter: 🎨 Neon effects applied successfully
NSPredicate: Use of 'MLN_IF' as an NSExpression function is forbidden.
NSPredicate: Use of 'MLN_IF' as an NSExpression function is forbidden.
NSPredicate: Use of 'MLN_IF' as an NSExpression function is forbidden.
flutter: 🗺️ LocationManager: Requesting current location (request #3)
flutter: 🗺️ LocationManager: Status changed from LocationStatus.available to LocationStatus.determining
flutter: 🗺️ LocationManager: Location request started at T+1665ms since app startup
flutter: 🏢 Successfully added neon 3D buildings
flutter: 🎵 [MapScreen] Build - currentlyPlayingPinId: null
flutter: 🎵 [MapScreen] Build - hasCurrentlyPlayingPin: false
flutter: 🎵 [MapScreen] _buildCaptionPopup called
flutter: 🎵 [MapScreen] - currentlyPlayingPinId: null
flutter: 🎵 [MapScreen] - currentlyPlayingPinData: null
flutter: 🎵 [MapScreen] ❌ No currently playing pin or pin data
flutter: NavigationHelper: Getting current tab...
flutter: NavigationHelper: Checking route - name: /map, looking for: /profile, match: false
flutter: NavigationHelper: Checking route - name: /map, looking for: /explore, match: false
flutter: NavigationHelper: Checking route - name: /map, looking for: /friends, match: false
flutter: NavigationHelper: Checking route - name: /map, looking for: /challenges, match: false
flutter: NavigationHelper: Checking route - name: /map, looking for: /map, match: true
flutter: NavigationHelper: Current tab is MAP (explicit)
flutter: ✅ Loaded stored Apple Music authentication
flutter: ✅ Apple Music service initialized. Authenticated: true
flutter: 🎵 [QueueManager] Initializing...
flutter: ✅ [QueueManager] Initialized successfully
flutter: ✅ Apple Music player service initialized
flutter: 🎵 [QueueManager] Initializing...
flutter: ✅ [QueueManager] Initialized successfully
flutter: 🎵 [MapScreen] _onMusicSourceChanged() called
flutter: 🎵 [MapScreen] Early return - mounted: true, hasPin: false
flutter: ✅ Loaded Apple Music user profile
flutter: 🎵 [MapScreen] _onMusicSourceChanged() called
flutter: 🎵 [MapScreen] Early return - mounted: true, hasPin: false
flutter: 🎵 [MapScreen] _onMusicSourceChanged() called
flutter: 🎵 [MapScreen] Early return - mounted: true, hasPin: false
flutter: 🎵 [MapScreen] _onMusicSourceChanged() called
flutter: 🎵 [MapScreen] Early return - mounted: true, hasPin: false
flutter: 🎨 Applying neon effects...
flutter: 🏢 Applying neon building colors...
flutter: 🏢 Removed all building layers to prevent z-fighting
flutter: 🎨 Neon effects applied successfully
NSPredicate: Use of 'MLN_IF' as an NSExpression function is forbidden.
NSPredicate: Use of 'MLN_IF' as an NSExpression function is forbidden.
NSPredicate: Use of 'MLN_IF' as an NSExpression function is forbidden.
flutter: 🏢 Successfully added neon 3D buildings
flutter: 📍 Received position update: lat: 36.**************, lng: -86.**************, accuracy: 10.476811082671006m, speed: 0.0m/s
flutter: 📍 User avatar image already added, skipping
flutter: 📍 SmartFetch: Handling location update at 36.**************, -86.**************
flutter: 📍 SmartFetch: Initial load required
flutter: 📍 SmartFetch: Immediate fetch required
flutter: 📍 SmartFetch: Started fetching pins
flutter: 🔍 Starting scanning animation
flutter: 📍 SmartFetch: Starting API call for filter PinFilterType.all
flutter: 🔍 Fetching nearby pins at lat: 36.**************, lng: -86.**************, radius: 5000.0
flutter: 🗺️ LocationManager: Location acquired in 627ms: lat: 36.**************, lng: -86.**************, accuracy: 10.476811082671006m, speed: 0.0m/s, altitude: 159.99554590022845m
flutter: 🗺️ LocationManager: Initial position set: lat: 36.**************, lng: -86.**************, accuracy: 10.476811082671006m
flutter: 🗺️ LocationManager: Status changed from LocationStatus.determining to LocationStatus.available
flutter: 🗺️ LocationManager: Location success rate: 33.3% (1/3)
flutter: 🚀 MapScreen: Location permission requested
flutter: 🚀 MapScreen: Pin refreshing disabled - handled by SmartLocationFetchManager
flutter: 🚀 MapScreen: Full initialization completed in: 1172ms
flutter: MapProvider: Position update received - pin refresh handled by SmartLocationFetchManager
flutter: 🎵 [MapScreen] Build - currentlyPlayingPinId: null
flutter: 🎵 [MapScreen] Build - hasCurrentlyPlayingPin: false
flutter: 🎵 [MapScreen] _buildCaptionPopup called
flutter: 🎵 [MapScreen] - currentlyPlayingPinId: null
flutter: 🎵 [MapScreen] - currentlyPlayingPinData: null
flutter: 🎵 [MapScreen] ❌ No currently playing pin or pin data
flutter: NavigationHelper: Getting current tab...
flutter: NavigationHelper: Checking route - name: /map, looking for: /profile, match: false
flutter: NavigationHelper: Checking route - name: /map, looking for: /explore, match: false
flutter: NavigationHelper: Checking route - name: /map, looking for: /friends, match: false
flutter: NavigationHelper: Checking route - name: /map, looking for: /challenges, match: false
flutter: NavigationHelper: Checking route - name: /map, looking for: /map, match: true
flutter: NavigationHelper: Current tab is MAP (explicit)
flutter: 🏢 Auto-tilting map to reveal 3D buildings (zoom: 16.5)
flutter: 🔍 Added scanning layers on top of all other layers
flutter:
flutter: 📥 === API RESPONSE ===
flutter: 🎯 GET https://api.bopmaps.com/api/gamification/user-achievements/in_progress_by_category/?category=artist
flutter: 📊 Status Code: 200
flutter: 📋 Response Headers: {connection: keep-alive, transfer-encoding: chunked, date: Sat, 02 Aug 2025 02:30:31 GMT, content-encoding: gzip, vary: Accept, origin, Cookie, strict-transport-security: max-age=********; includeSubDomains; preload, referrer-policy: same-origin, report-to: {"group":"cf-nel","max_age":604800,"endpoints":[{"url":"https://a.nel.cloudflare.com/report/v4?s=a6n4oeFRgoGFL6%2FjGKymZ1MSnu8O3BNsDhPzsKDU7L2xQvfVpzsQxSPi9Q7G368LUM71BzY8Xly8a3jy%2BtZs%2F7eVUMGW0EK5yX8tdU1oCQ%3D%3D"}]}, cf-cache-status: DYNAMIC, content-type: application/json, cross-origin-opener-policy: same-origin, server: cloudflare, alt-svc: h3=":443"; ma=86400, nel: {"report_to":"cf-nel","success_fraction":0.0,"max_age":604800}, cf-ray: 968a275bb99ba9c4-IAD, x-frame-options: DENY, x-content-type-options: nosniff, allow: GET, HEAD, OPTIONS}
flutter: 📦 Response Body: []
flutter: ⏰ Timestamp: 2025-08-01T21:30:31.296449
flutter: ✅ Request successful
flutter: ========================
flutter:
flutter:
flutter: 📥 === API RESPONSE ===
flutter: 🎯 GET https://api.bopmaps.com/api/gamification/user-achievements/completed_by_category/?category=location
flutter: 📊 Status Code: 200
flutter: 📋 Response Headers: {connection: keep-alive, transfer-encoding: chunked, date: Sat, 02 Aug 2025 02:30:31 GMT, content-encoding: gzip, vary: Accept, origin, Cookie, strict-transport-security: max-age=********; includeSubDomains; preload, referrer-policy: same-origin, report-to: {"group":"cf-nel","max_age":604800,"endpoints":[{"url":"https://a.nel.cloudflare.com/report/v4?s=3mH%2B%2BDvwhe8TTicrgYQZ%2Fir%2FRqMrBvCbnXn8uPWxI6OtG2ewrpz8%2BrXW01CTokaiA52ue5dO64j8pWdPDFLHohSwFIYAIl26HKd9Zvv4kw%3D%3D"}]}, cf-cache-status: DYNAMIC, content-type: application/json, cross-origin-opener-policy: same-origin, server: cloudflare, alt-svc: h3=":443"; ma=86400, nel: {"report_to":"cf-nel","success_fraction":0.0,"max_age":604800}, cf-ray: 968a275bbd3be647-IAD, x-frame-options: DENY, x-content-type-options: nosniff, allow: GET, HEAD, OPTIONS}
flutter: 📦 Response Body: []
flutter: ⏰ Timestamp: 2025-08-01T21:30:31.364934
flutter: ✅ Request successful
flutter: ========================
flutter:
flutter: 🏷️ Adding all labels on top...
flutter: 🏷️ All labels added on top
NSPredicate: Use of 'MLN_IF' as an NSExpression function is forbidden.
NSPredicate: Use of 'mgl_does:have:' as an NSExpression function is forbidden.
NSPredicate: Use of 'mgl_does:have:' as an NSExpression function is forbidden.
NSPredicate: Use of 'mgl_attributed:' as an NSExpression function is forbidden.
NSPredicate: Use of 'mgl_does:have:' as an NSExpression function is forbidden.
NSPredicate: Use of 'mgl_does:have:' as an NSExpression function is forbidden.
NSPredicate: Use of 'mgl_does:have:' as an NSExpression function is forbidden.
NSPredicate: Use of 'mgl_does:have:' as an NSExpression function is forbidden.
flutter: 🏢 Added building icons
flutter: 🏷️ Added enhanced building labels
flutter: ✅ Added POI icon: poi-restaurant
flutter:
flutter: 📥 === API RESPONSE ===
flutter: 🎯 GET https://api.bopmaps.com/api/gamification/user-achievements/completed_summary/
flutter: 📊 Status Code: 200
flutter: 📋 Response Headers: {connection: keep-alive, transfer-encoding: chunked, date: Sat, 02 Aug 2025 02:30:31 GMT, content-encoding: gzip, vary: Accept, origin, Cookie, strict-transport-security: max-age=********; includeSubDomains; preload, referrer-policy: same-origin, report-to: {"group":"cf-nel","max_age":604800,"endpoints":[{"url":"https://a.nel.cloudflare.com/report/v4?s=TQNobEKq8DPoQ%2FucZS43elHbG%2FkMMPsMfxkQqq7IxtPQtMFnJQr1AWrO52lv3qrLaTAjFUe9p9mflIGBgbUHWPl0b9tz%2BmU2z%2FlLdQI%3D"}]}, cf-cache-status: DYNAMIC, content-type: application/json, cross-origin-opener-policy: same-origin, server: cloudflare, alt-svc: h3=":443"; ma=86400, nel: {"report_to":"cf-nel","success_fraction":0.0,"max_age":604800}, cf-ray: 968a275beec8ae0c-IAD, x-frame-options: DENY, x-content-type-options: nosniff, allow: GET, HEAD, OPTIONS}
flutter: 📦 Response Body: {"location":{"count":0,"recent":[]},"artist":{"count":0,"recent":[]},"genre":{"count":0,"recent":[]},"social":{"count":1,"recent":[{"id":1,"name":"First React","xp_reward":25,"tier":"T0","completed_at":"2025-08-01T23:48:39.183728+00:00"}]},"total_completed":1,"total_xp":25}
flutter: ⏰ Timestamp: 2025-08-01T21:30:31.422968
flutter: ✅ Request successful
flutter: ========================
flutter:
flutter: ✅ Loaded completed summary: 1 total completed, 25 total XP
flutter: ✅ Loaded completion stats from optimized endpoint
flutter: ✅ Added POI icon: poi-cafe
flutter: ✅ Added POI icon: poi-hotel
flutter:
flutter: 📥 === API RESPONSE ===
flutter: 🎯 GET https://api.bopmaps.com/api/gamification/user-achievements/in_progress_by_category/?category=genre
flutter: 📊 Status Code: 200
flutter: 📋 Response Headers: {connection: keep-alive, transfer-encoding: chunked, date: Sat, 02 Aug 2025 02:30:31 GMT, content-encoding: gzip, vary: Accept, origin, Cookie, strict-transport-security: max-age=********; includeSubDomains; preload, referrer-policy: same-origin, report-to: {"group":"cf-nel","max_age":604800,"endpoints":[{"url":"https://a.nel.cloudflare.com/report/v4?s=dMx9R%2BbLjVqqkIh%2Btfkr5VJnBp0Ai1bj2bKICDubYTVDRUofbaUf0R7J2bsHLtCRTBUC7VZOFevpC3LS00Faxh38CYmc3N8Oa5aAgzyofg%3D%3D"}]}, cf-cache-status: DYNAMIC, content-type: application/json, cross-origin-opener-policy: same-origin, server: cloudflare, alt-svc: h3=":443"; ma=86400, nel: {"report_to":"cf-nel","success_fraction":0.0,"max_age":604800}, cf-ray: 968a275bdfe9f272-IAD, x-frame-options: DENY, x-content-type-options: nosniff, allow: GET, HEAD, OPTIONS}
flutter: 📦 Response Body: []
flutter: ⏰ Timestamp: 2025-08-01T21:30:31.467787
flutter: ✅ Request successful
flutter: ========================
flutter:
flutter: 🔍 [SpotifyService] Playlist search response keys: [playlists]
flutter: ✓ [SpotifyService] Got 25 playlist results for "happy mood playlist"
flutter: 📚 Found 15 playlists for mood "happy mood playlist"
flutter: 🎵 [SpotifyService] Getting playlist tracks (playlist: 0IAG5sPikOCo5nvyKJjCYo, limit: 80, offset: 40)
flutter: 🌍 [SpotifyService] Adding market parameter for direct Spotify API
flutter:
flutter: 📥 === API RESPONSE ===
flutter: 🎯 GET https://api.bopmaps.com/api/gamification/user-achievements/in_progress_by_category/?category=social
flutter: 📊 Status Code: 200
flutter: 📋 Response Headers: {connection: keep-alive, transfer-encoding: chunked, date: Sat, 02 Aug 2025 02:30:31 GMT, content-encoding: gzip, vary: Accept, origin, Cookie, strict-transport-security: max-age=********; includeSubDomains; preload, referrer-policy: same-origin, report-to: {"group":"cf-nel","max_age":604800,"endpoints":[{"url":"https://a.nel.cloudflare.com/report/v4?s=t5Y0hfe6L9DEITMP1sQ9BvnEHBh2vMtuEvZHiVDti6flfcAew6gnJQUaQElQEjnKuTwU1B4XjkW0EXqnW0HvegS%2F831L%2BcLdkBveWW2eYQ%3D%3D"}]}, cf-cache-status: DYNAMIC, content-type: application/json, cross-origin-opener-policy: same-origin, server: cloudflare, alt-svc: h3=":443"; ma=86400, nel: {"report_to":"cf-nel","success_fraction":0.0,"max_age":604800}, cf-ray: 968a275bdf1dc3b1-IAD, x-frame-options: DENY, x-content-type-options: nosniff, allow: GET, HEAD, OPTIONS}
flutter: 📦 Response Body: [{"id":21,"name":"Silent Impact","description":"A Legend-rank user upvotes your pin","xp_reward":350,"tier":"SECRET","icon_name":null,"category":"social","progress":{"current_count":0,"required_count":1,"progress_percentage":0.0},"last_updated":"2025-08-01T23:48:39.819729+00:00"},{"id":20,"name":"Viral Pin","description":"One pin hits 75 upvotes","xp_reward":1000,"tier":"T7","icon_name":null,"category":"social","progress":{"current_count":0,"required_count":1,"progress_percentage":0.0},"last_updated":"2025-08-01T23:48:39.781955+00:00"},{"id":19,"name":"Social Icon","description":"500 upvotes received + 100 unique supporters","xp_reward":800,"tier":"T7","icon_name":null,"category":"social","progress":{"current_count":0,"required_count":500,"progress_percentage":0.0},"last_updated":"2025-08-01T23:48:39.750770+00:00"},{"id":18,"name":"Comment Legend","description":"Write 100 comments","xp_reward":650,"tier":"T6","icon_name":null,"category":"social","progress":{"current_count":0,"required_count":1,"progress_percentage":0.0},"last_updated":"2025-08-01T23:48:39.720252+00:00"},{"id":17,"name":"Multi-Hit Maker","description":"3 pins with ≥ 30 upvotes each","xp_reward":650,"tier":"T6","icon_name":null,"category":"social","progress":{"current_count":0,"required_count":1,"progress_percentage":0.0},"last_updated":"2025-08-01T23:48:39.688730+00:00"},{"id":16,"name":"Super Hype","description":"Give 750 upvotes total","xp_reward":600,"tier":"T6","icon_name":null,"category":"social","progress":{"current_count":1,"required_count":750,"progress_percentage":0.1},"last_updated":"2025-08-01T23:48:39.657774+00:00"},{"id":15,"name":"Ripple Effect","description":"50 unique users upvoted your pins","xp_reward":450,"tier":"T5","icon_name":null,"category":"social","progress":{"current_count":0,"required_count":1,"progress_percentage":0.0},"last_updated":"2025-08-01T23:48:39.624506+00:00"},{"id":14,"name":"Hot Pin","description":"Get 40 upvotes on a single pin","xp_reward":400,"tier":"T5","icon_name":null,"category":"social","progress":{"current_count":0,"required_count":1,"progress_percentage":0.0},"last_updated":"2025-08-01T23:48:39.593792+00:00"},{"id":13,"name":"Weekly Warrior","description":"Complete 3 different weekly challenges","xp_reward":300,"tier":"T4","icon_name":null,"category":"social","progress":{"current_count":0,"required_count":1,"progress_percentage":0.0},"last_updated":"2025-08-01T23:48:39.562259+00:00"},{"id":12,"name":"Consistency King","description":"Give ≥ 2 upvotes 15 consecutive days","xp_reward":300,"tier":"T4","icon_name":null,"category":"social","progress":{"current_count":0,"required_count":1,"progress_percentage":0.0},"last_updated":"2025-08-01T23:48:39.530577+00:00"},{"id":11,"name":"Crowd Favorite","description":"Collect 200 upvotes (lifetime)","xp_reward":300,"tier":"T4","icon_name":null,"category":"social","progress":{"current_count":0,"required_count":200,"progress_percentage":0.0},"last_updated":"2025-08-01T23:48:39.497832+00:00"},{"id":10,"name":"Rising Star","description":"20 unique users upvoted your pins","xp_reward":225,"tier":"T3","icon_name":null,"category":"social","progress":{"current_count":0,"required_count":1,"progress_percentage":0.0},"last_updated":"2025-08-01T23:48:39.466740+00:00"},{"id":9,"name":"Community Voice","description":"Write 25 comments","xp_reward":225,"tier":"T3","icon_name":null,"category":"social","progress":{"current_count":0,"required_count":1,"progress_percentage":0.0},"last_updated":"2025-08-01T23:48:39.436361+00:00"},{"id":8,"name":"Hype Machine","description":"Give 150 upvotes (lifetime)","xp_reward":200,"tier":"T3","icon_name":null,"category":"social","progress":{"current_count":1,"required_count":150,"progress_percentage":0.7},"last_updated":"2025-08-01T23:48:39.405679+00:00"},{"id":7,"name":"Popular Pin","description":"Get 15 upvotes on a single pin","xp_reward":150,"tier":"T2","icon_name":null,"category":"social","progress":{"current_count":0,"required_count":1,"progress_percentage":0.0},"last_updated":"2025-08-01T23:48:39.373099+00:00"},{"id":6,"name":"Conversation Starter","description":"Comment on 10 different pins","xp_reward":150,"tier":"T2","icon_name":null,"category":"social","progress":{"current_count":0,"required_count":1,"progress_percentage":0.0},"last_updated":"2025-08-01T23:48:39.341782+00:00"},{"id":5,"name":"Week-Long Hype","description":"Give ≥ 1 upvote 7 days in a row","xp_reward":150,"tier":"T2","icon_name":null,"category":"social","progress":{"current_count":0,"required_count":1,"progress_percentage":0.0},"last_updated":"2025-08-01T23:48:39.311084+00:00"},{"id":4,"name":"Local Buzz","description":"Get 10 upvotes from 5 unique users","xp_reward":100,"tier":"T1","icon_name":null,"category":"social","progress":{"current_count":0,"required_count":10,"progress_percentage":0.0},"last_updated":"2025-08-01T23:48:39.279343+00:00"},{"id":3,"name":"Reaction Rookie","description":"Give 25 upvotes","xp_reward":100,"tier":"T1","icon_name":null,"category":"social","progress":{"current_count":1,"required_count":25,"progress_percentage":4.0},"last_updated":"2025-08-01T23:48:39.246887+00:00"},{"id":2,"name":"Noticed","description":"Get 1 upvote on one of your pins","xp_reward":25,"tier":"T0","icon_name":null,"category":"social","progress":{"current_count":0,"required_count":1,"progress_percentage":0.0},"last_updated":"2025-08-01T23:48:39.214670+00:00"}]
flutter: ⏰ Timestamp: 2025-08-01T21:30:31.486224
flutter: ✅ Request successful
flutter: ========================
flutter:
flutter: 🏷️ Adding all labels on top...
flutter: 🏷️ All labels added on top
flutter: ✅ Added POI icon: poi-hospital
flutter: Could not add building icons: PlatformException(layerAlreadyExists, Layer already exists, Layer with id building-icons already exists., null)
flutter: Could not add building labels: PlatformException(layerAlreadyExists, Layer already exists, Layer with id building-labels already exists., null)
flutter: Could not add road labels: PlatformException(layerAlreadyExists, Layer already exists, Layer with id road-labels already exists., null)
flutter: Could not add place labels: PlatformException(layerAlreadyExists, Layer already exists, Layer with id place-labels already exists., null)
flutter: Could not add water labels: PlatformException(layerAlreadyExists, Layer already exists, Layer with id water-labels already exists., null)
flutter:
flutter: 📥 === API RESPONSE ===
flutter: 🎯 GET https://api.bopmaps.com/api/gamification/user-achievements/completed_by_category/?category=social
flutter: 📊 Status Code: 200
flutter: 📋 Response Headers: {connection: keep-alive, transfer-encoding: chunked, date: Sat, 02 Aug 2025 02:30:31 GMT, content-encoding: gzip, vary: Accept, origin, Cookie, strict-transport-security: max-age=********; includeSubDomains; preload, referrer-policy: same-origin, report-to: {"group":"cf-nel","max_age":604800,"endpoints":[{"url":"https://a.nel.cloudflare.com/report/v4?s=UCrr%****************************************%2FA6kcGkBbVy6%2BMhLqlGElvUxh%2BnbXfzvlk5kYKr3ssV9DBWIfhg%2FLlM%2FLSUAUEw%3D%3D"}]}, cf-cache-status: DYNAMIC, content-type: application/json, cross-origin-opener-policy: same-origin, server: cloudflare, alt-svc: h3=":443"; ma=86400, nel: {"report_to":"cf-nel","success_fraction":0.0,"max_age":604800}, cf-ray: 968a275bcc35d644-IAD, x-frame-options: DENY, x-content-type-options: nosniff, allow: GET, HEAD, OPTIONS}
flutter: 📦 Response Body: [{"id":1,"name":"First React","description":"Give 1 upvote","xp_reward":25,"tier":"T0","icon_name":null,"completed_at":"2025-08-01T23:48:39.183728+00:00","category":"social"}]
flutter: ⏰ Timestamp: 2025-08-01T21:30:31.508645
flutter: ✅ Request successful
flutter: ========================
flutter:
flutter:
flutter: 📥 === API RESPONSE ===
flutter: 🎯 GET https://api.bopmaps.com/api/gamification/user-achievements/in_progress_by_category/?category=social
flutter: 📊 Status Code: 200
flutter: 📋 Response Headers: {connection: keep-alive, transfer-encoding: chunked, date: Sat, 02 Aug 2025 02:30:31 GMT, content-encoding: gzip, vary: Accept, origin, Cookie, strict-transport-security: max-age=********; includeSubDomains; preload, referrer-policy: same-origin, report-to: {"group":"cf-nel","max_age":604800,"endpoints":[{"url":"https://a.nel.cloudflare.com/report/v4?s=YeKuI3Q4LJlmA0RxjRzouNrHfvO1RUdnYhi5EkgmDOr3XzycAVvxpN4SquJdIE5a8gNQRCVBkiLOZwAb5AbYUfOpqYESCgpIXNIpp2FyDg%3D%3D"}]}, cf-cache-status: DYNAMIC, content-type: application/json, cross-origin-opener-policy: same-origin, server: cloudflare, alt-svc: h3=":443"; ma=86400, nel: {"report_to":"cf-nel","success_fraction":0.0,"max_age":604800}, cf-ray: 968a275c5f2f59c7-IAD, x-frame-options: DENY, x-content-type-options: nosniff, allow: GET, HEAD, OPTIONS}
flutter: 📦 Response Body: [{"id":21,"name":"Silent Impact","description":"A Legend-rank user upvotes your pin","xp_reward":350,"tier":"SECRET","icon_name":null,"category":"social","progress":{"current_count":0,"required_count":1,"progress_percentage":0.0},"last_updated":"2025-08-01T23:48:39.819729+00:00"},{"id":20,"name":"Viral Pin","description":"One pin hits 75 upvotes","xp_reward":1000,"tier":"T7","icon_name":null,"category":"social","progress":{"current_count":0,"required_count":1,"progress_percentage":0.0},"last_updated":"2025-08-01T23:48:39.781955+00:00"},{"id":19,"name":"Social Icon","description":"500 upvotes received + 100 unique supporters","xp_reward":800,"tier":"T7","icon_name":null,"category":"social","progress":{"current_count":0,"required_count":500,"progress_percentage":0.0},"last_updated":"2025-08-01T23:48:39.750770+00:00"},{"id":18,"name":"Comment Legend","description":"Write 100 comments","xp_reward":650,"tier":"T6","icon_name":null,"category":"social","progress":{"current_count":0,"required_count":1,"progress_percentage":0.0},"last_updated":"2025-08-01T23:48:39.720252+00:00"},{"id":17,"name":"Multi-Hit Maker","description":"3 pins with ≥ 30 upvotes each","xp_reward":650,"tier":"T6","icon_name":null,"category":"social","progress":{"current_count":0,"required_count":1,"progress_percentage":0.0},"last_updated":"2025-08-01T23:48:39.688730+00:00"},{"id":16,"name":"Super Hype","description":"Give 750 upvotes total","xp_reward":600,"tier":"T6","icon_name":null,"category":"social","progress":{"current_count":1,"required_count":750,"progress_percentage":0.1},"last_updated":"2025-08-01T23:48:39.657774+00:00"},{"id":15,"name":"Ripple Effect","description":"50 unique users upvoted your pins","xp_reward":450,"tier":"T5","icon_name":null,"category":"social","progress":{"current_count":0,"required_count":1,"progress_percentage":0.0},"last_updated":"2025-08-01T23:48:39.624506+00:00"},{"id":14,"name":"Hot Pin","description":"Get 40 upvotes on a single pin","xp_reward":400,"tier":"T5","icon_name":null,"category":"social","progress":{"current_count":0,"required_count":1,"progress_percentage":0.0},"last_updated":"2025-08-01T23:48:39.593792+00:00"},{"id":13,"name":"Weekly Warrior","description":"Complete 3 different weekly challenges","xp_reward":300,"tier":"T4","icon_name":null,"category":"social","progress":{"current_count":0,"required_count":1,"progress_percentage":0.0},"last_updated":"2025-08-01T23:48:39.562259+00:00"},{"id":12,"name":"Consistency King","description":"Give ≥ 2 upvotes 15 consecutive days","xp_reward":300,"tier":"T4","icon_name":null,"category":"social","progress":{"current_count":0,"required_count":1,"progress_percentage":0.0},"last_updated":"2025-08-01T23:48:39.530577+00:00"},{"id":11,"name":"Crowd Favorite","description":"Collect 200 upvotes (lifetime)","xp_reward":300,"tier":"T4","icon_name":null,"category":"social","progress":{"current_count":0,"required_count":200,"progress_percentage":0.0},"last_updated":"2025-08-01T23:48:39.497832+00:00"},{"id":10,"name":"Rising Star","description":"20 unique users upvoted your pins","xp_reward":225,"tier":"T3","icon_name":null,"category":"social","progress":{"current_count":0,"required_count":1,"progress_percentage":0.0},"last_updated":"2025-08-01T23:48:39.466740+00:00"},{"id":9,"name":"Community Voice","description":"Write 25 comments","xp_reward":225,"tier":"T3","icon_name":null,"category":"social","progress":{"current_count":0,"required_count":1,"progress_percentage":0.0},"last_updated":"2025-08-01T23:48:39.436361+00:00"},{"id":8,"name":"Hype Machine","description":"Give 150 upvotes (lifetime)","xp_reward":200,"tier":"T3","icon_name":null,"category":"social","progress":{"current_count":1,"required_count":150,"progress_percentage":0.7},"last_updated":"2025-08-01T23:48:39.405679+00:00"},{"id":7,"name":"Popular Pin","description":"Get 15 upvotes on a single pin","xp_reward":150,"tier":"T2","icon_name":null,"category":"social","progress":{"current_count":0,"required_count":1,"progress_percentage":0.0},"last_updated":"2025-08-01T23:48:39.373099+00:00"},{"id":6,"name":"Conversation Starter","description":"Comment on 10 different pins","xp_reward":150,"tier":"T2","icon_name":null,"category":"social","progress":{"current_count":0,"required_count":1,"progress_percentage":0.0},"last_updated":"2025-08-01T23:48:39.341782+00:00"},{"id":5,"name":"Week-Long Hype","description":"Give ≥ 1 upvote 7 days in a row","xp_reward":150,"tier":"T2","icon_name":null,"category":"social","progress":{"current_count":0,"required_count":1,"progress_percentage":0.0},"last_updated":"2025-08-01T23:48:39.311084+00:00"},{"id":4,"name":"Local Buzz","description":"Get 10 upvotes from 5 unique users","xp_reward":100,"tier":"T1","icon_name":null,"category":"social","progress":{"current_count":0,"required_count":10,"progress_percentage":0.0},"last_updated":"2025-08-01T23:48:39.279343+00:00"},{"id":3,"name":"Reaction Rookie","description":"Give 25 upvotes","xp_reward":100,"tier":"T1","icon_name":null,"category":"social","progress":{"current_count":1,"required_count":25,"progress_percentage":4.0},"last_updated":"2025-08-01T23:48:39.246887+00:00"},{"id":2,"name":"Noticed","description":"Get 1 upvote on one of your pins","xp_reward":25,"tier":"T0","icon_name":null,"category":"social","progress":{"current_count":0,"required_count":1,"progress_percentage":0.0},"last_updated":"2025-08-01T23:48:39.214670+00:00"}]
flutter: ⏰ Timestamp: 2025-08-01T21:30:31.510364
flutter: ✅ Request successful
flutter: ========================
flutter:
flutter: ✅ Loaded 21 achievements for category social using optimized endpoints
flutter: ❌ [SpotifyService] No access token found in either storage location
flutter:
flutter: 📊 Achievements Summary:
flutter: - Total: 74
flutter: - Completed: 7
flutter: - In Progress: 67
flutter: ❌ Error loading achievements for category social: A GamificationProvider was used after being disposed.
flutter: Once you have called dispose() on a GamificationProvider, it can no longer be used.
flutter:
flutter: 📊 Achievements Summary:
flutter: - Total: 74
flutter: - Completed: 7
flutter: - In Progress: 67
flutter: 🍎 Initializing Apple Music service...
flutter: 🍎 Initializing Apple Music service...
flutter: ✅ Added POI icon: poi-restaurant
flutter: ✅ Added POI icon: poi-school
flutter: ✅ Added POI icon: poi-cafe
flutter: ✅ Added POI icon: poi-bank
flutter:
flutter: 📥 === API RESPONSE ===
flutter: 🎯 GET https://api.bopmaps.com/api/gamification/user-achievements/in_progress_by_category/?category=genre
flutter: 📊 Status Code: 200
flutter: 📋 Response Headers: {connection: keep-alive, transfer-encoding: chunked, date: Sat, 02 Aug 2025 02:30:31 GMT, content-encoding: gzip, vary: Accept, origin, Cookie, strict-transport-security: max-age=********; includeSubDomains; preload, referrer-policy: same-origin, report-to: {"group":"cf-nel","max_age":604800,"endpoints":[{"url":"https://a.nel.cloudflare.com/report/v4?s=dQgqhNJvPRdrx0hwUiAGjtIvrix0ks3JV2E%2BNbqTGCYA9pEwKPXZsFVZq%2BZhzydjbL2fItdjIn2Oq6tFHvhFwSErh9Y7Y%2FyKph7HZJj81g%3D%3D"}]}, cf-cache-status: DYNAMIC, content-type: application/json, cross-origin-opener-policy: same-origin, server: cloudflare, alt-svc: h3=":443"; ma=86400, nel: {"report_to":"cf-nel","success_fraction":0.0,"max_age":604800}, cf-ray: 968a275bdd85f4ca-IAD, x-frame-options: DENY, x-content-type-options: nosniff, allow: GET, HEAD, OPTIONS}
flutter: 📦 Response Body: []
flutter: ⏰ Timestamp: 2025-08-01T21:30:31.581505
flutter: ✅ Request successful
flutter: ========================
flutter:
flutter:
flutter: 📥 === API RESPONSE ===
flutter: 🎯 GET https://api.bopmaps.com/api/gamification/user-achievements/completed_by_category/?category=location
flutter: 📊 Status Code: 200
flutter: 📋 Response Headers: {connection: keep-alive, transfer-encoding: chunked, date: Sat, 02 Aug 2025 02:30:31 GMT, content-encoding: gzip, vary: Accept, origin, Cookie, strict-transport-security: max-age=********; includeSubDomains; preload, referrer-policy: same-origin, report-to: {"group":"cf-nel","max_age":604800,"endpoints":[{"url":"https://a.nel.cloudflare.com/report/v4?s=YzsYUkpQvQkBcMql6pT3Nl%2B2PgkYYMI6b%2Foa699u7dFg9vfx44dIu1EMYnxJ%2BPWr5h1qoXAT2sZdyN%2FJcYb9QRoHV1wR1j6xPDlzbTeXXg%3D%3D"}]}, cf-cache-status: DYNAMIC, content-type: application/json, cross-origin-opener-policy: same-origin, server: cloudflare, alt-svc: h3=":443"; ma=86400, nel: {"report_to":"cf-nel","success_fraction":0.0,"max_age":604800}, cf-ray: 968a275beb6520ca-IAD, x-frame-options: DENY, x-content-type-options: nosniff, allow: GET, HEAD, OPTIONS}
flutter: 📦 Response Body: []
flutter: ⏰ Timestamp: 2025-08-01T21:30:31.582122
flutter: ✅ Request successful
flutter: ========================
flutter:
flutter:
flutter: 📥 === API RESPONSE ===
flutter: 🎯 GET https://api.bopmaps.com/api/gamification/user-achievements/
flutter: 📊 Status Code: 200
flutter: 📋 Response Headers: {connection: keep-alive, transfer-encoding: chunked, date: Sat, 02 Aug 2025 02:30:31 GMT, content-encoding: gzip, vary: Accept, origin, Cookie, strict-transport-security: max-age=********; includeSubDomains; preload, referrer-policy: same-origin, report-to: {"group":"cf-nel","max_age":604800,"endpoints":[{"url":"https://a.nel.cloudflare.com/report/v4?s=6zSxUYVoqRhZltWW2wMzNTfNJetlUPAQ%2Bo3fP%2B1x4npBtOx2RoncepCWrFpI2V4UEV7BjU7v2mmfo5zw%2FOU1DfcAhUN%2BS5AlDiAqFhOz%2Bg%3D%3D"}]}, cf-cache-status: DYNAMIC, content-type: application/json, cross-origin-opener-policy: same-origin, server: cloudflare, alt-svc: h3=":443"; ma=86400, nel: {"report_to":"cf-nel","success_fraction":0.0,"max_age":604800}, cf-ray: 968a275bdf01c96a-IAD, x-frame-options: DENY, x-content-type-options: nosniff, allow: GET, HEAD, OPTIONS}
flutter: 📦 Response Body: {"count":21,"next":"https://api.bopmaps.com/api/gamification/user-achievements/?page=2","previous":null,"results":[{"id":1,"achievement":{"id":52,"name":"First React","description":"Give 1 upvote","icon":null,"icon_name":null,"criteria":{"reactions_given":1},"type":"social","challenge_id":"first_react","tier":"T0","xp_reward":25,"is_secret":false,"created_at":"2025-08-01T23:23:10.703144Z","background_color":"#FFFFFF","primary_color":"#000000","category":"Social Challenge","reward_skin":null,"icon_data":null,"progress":{"current_count":0,"progress_percentage":0.0},"completed_at":"2025-08-01T23:48:39.183728Z"},"progress":{"max_streak":1,"current_streak":1,"daily_reactions":{"2025-08-01":1},"reactions_given":1,"last_reaction_date":"2025-08-01"},"completed_at":"2025-08-01T23:48:39.183728Z","progress_percentage":0,"last_updated":"2025-08-01T23:48:39.183846Z"},{"id":2,"achievement":{"id":53,"name":"Noticed","description":"Get 1 upvote on one of your pins","icon":null,"icon_name":null,"criteria":{"reactions_received":1},"type":"social","challenge_id":"first_like_received","tier":"T0","xp_reward":25,"is_secret":false,"created_at":"2025-08-01T23:23:10.708293Z","background_color":"#FFFFFF","primary_color":"#000000","category":"Social Challenge","reward_skin":null,"icon_data":null,"progress":{"current_count":0,"progress_percentage":0.0},"completed_at":null},"progress":{"max_streak":1,"current_streak":1,"daily_reactions":{"2025-08-01":1},"reactions_given":1,"last_reaction_date":"2025-08-01"},"completed_at":null,"progress_percentage":0,"last_updated":"2025-08-01T23:48:39.214670Z"},{"id":3,"achievement":{"id":54,"name":"Reaction Rookie","description":"Give 25 upvotes","icon":null,"icon_name":null,"criteria":{"reactions_given":25},"type":"social","challenge_id":"react_25_given","tier":"T1","xp_reward":100,"is_secret":false,"created_at":"2025-08-01T23:23:10.713660Z","background_color":"#FFFFFF","primary_color":"#000000","category":"Social Challenge","reward_skin":null,"icon_data":null,"progress":{"current_count":0,"progress_percentage":0.0},"completed_at":null},"progress":{"max_streak":1,"current_streak":1,"daily_reactions":{"2025-08-01":1},"reactions_given":1,"last_reaction_date":"2025-08-01"},"completed_at":null,"progress_percentage":0,"last_updated":"2025-08-01T23:48:39.246887Z"},{"id":4,"achievement":{"id":55,"name":"Local Buzz","description":"Get 10 upvotes from 5 unique users","icon":null,"icon_name":null,"criteria":{"unique_reactors":5,"reactions_received":10},"type":"social","challenge_id":"local_buzz","tier":"T1","xp_reward":100,"is_secret":false,"created_at":"2025-08-01T23:23:10.719343Z","background_color":"#FFFFFF","primary_color":"#000000","category":"Social Challenge","reward_skin":null,"icon_data":null,"progress":{"current_count":0,"progress_percentage":0.0},"completed_at":null},"progress":{"max_streak":1,"current_streak":1,"daily_reactions":{"2025-08-01":1},"reactions_given":1,"last_reaction_date":"2025-08-01"},"completed_at":null,"progress_percentage":0,"last_updated":"2025-08-01T23:48:39.279343Z"},{"id":5,"achievement":{"id":56,"name":"Week-Long Hype","description":"Give ≥ 1 upvote 7 days in a row","icon":null,"icon_name":null,"criteria":{"daily_reactions":1,"consecutive_days":7},"type":"social","challenge_id":"react_streak_7","tier":"T2","xp_reward":150,"is_secret":false,"created_at":"2025-08-01T23:23:10.724580Z","background_color":"#FFFFFF","primary_color":"#000000","category":"Social Challenge","reward_skin":null,"icon_data":null,"progress":{"current_count":0,"progress_percentage":0.0},"completed_at":null},"progress":{"max_streak":1,"current_streak":1,"daily_reactions":{"2025-08-01":1},"reactions_given":1,"last_reaction_date":"2025-08-01"},"completed_at":null,"progress_percentage":0,"last_updated":"2025-08-01T23:48:39.311084Z"},{"id":6,"achievement":{"id":57,"name":"Conversation Starter","description":"Comment on 10 different pins","icon":null,"icon_name":null,"criteria":{"comments_given":10},"type":"social","challenge_id":"comments_10","tier":"T2","xp_reward":150,"is_secret":false,"created_at":"2025-08-01T23:23:10.729842Z","background_color":"#FFFFFF","primary_color":"#000000","category":"Social Challenge","reward_skin":null,"icon_data":null,"progress":{"current_count":0,"progress_percentage":0.0},"completed_at":null},"progress":{"max_streak":1,"current_streak":1,"daily_reactions":{"2025-08-01":1},"reactions_given":1,"last_reaction_date":"2025-08-01"},"completed_at":null,"progress_percentage":0,"last_updated":"2025-08-01T23:48:39.341782Z"},{"id":7,"achievement":{"id":58,"name":"Popular Pin","description":"Get 15 upvotes on a single pin","icon":null,"icon_name":null,"criteria":{"single_pin_reactions":15},"type":"social","challenge_id":"popular_pin","tier":"T2","xp_reward":150,"is_secret":false,"created_at":"2025-08-01T23:23:10.735027Z","background_color":"#FFFFFF","primary_color":"#000000","category":"Social Challenge","reward_skin":null,"icon_data":null,"progress":{"current_count":0,"progress_percentage":0.0},"completed_at":null},"progress":{"max_streak":1,"current_streak":1,"daily_reactions":{"2025-08-01":1},"reactions_given":1,"last_reaction_date":"2025-08-01"},"completed_at":null,"progress_percentage":0,"last_updated":"2025-08-01T23:48:39.373099Z"},{"id":8,"achievement":{"id":59,"name":"Hype Machine","description":"Give 150 upvotes (lifetime)","icon":null,"icon_name":null,"criteria":{"reactions_given":150},"type":"social","challenge_id":"react_100_given","tier":"T3","xp_reward":200,"is_secret":false,"created_at":"2025-08-01T23:23:10.740306Z","background_color":"#FFFFFF","primary_color":"#000000","category":"Social Challenge","reward_skin":null,"icon_data":null,"progress":{"current_count":0,"progress_percentage":0.0},"completed_at":null},"progress":{"max_streak":1,"current_streak":1,"daily_reactions":{"2025-08-01":1},"reactions_given":1,"last_reaction_date":"2025-08-01"},"completed_at":null,"progress_percentage":0,"last_updated":"2025-08-01T23:48:39.405679Z"},{"id":9,"achievement":{"id":60,"name":"Community Voice","description":"Write 25 comments","icon":null,"icon_name":null,"criteria":{"comments_given":25},"type":"social","challenge_id":"comments_25","tier":"T3","xp_reward":225,"is_secret":false,"created_at":"2025-08-01T23:23:10.745726Z","background_color":"#FFFFFF","primary_color":"#000000","category":"Social Challenge","reward_skin":null,"icon_data":null,"progress":{"current_count":0,"progress_percentage":0.0},"completed_at":null},"progress":{"max_streak":1,"current_streak":1,"daily_reactions":{"2025-08-01":1},"reactions_given":1,"last_reaction_date":"2025-08-01"},"completed_at":null,"progress_percentage":0,"last_updated":"2025-08-01T23:48:39.436361Z"},{"id":10,"achievement":{"id":61,"name":"Rising Star","description":"20 unique users upvoted your pins","icon":null,"icon_name":null,"criteria":{"unique_reactors":20},"type":"social","challenge_id":"unique_supporters_15","tier":"T3","xp_reward":225,"is_secret":false,"created_at":"2025-08-01T23:23:10.751139Z","background_color":"#FFFFFF","primary_color":"#000000","category":"Social Challenge","reward_skin":null,"icon_data":null,"progress":{"current_count":0,"progress_percentage":0.0},"completed_at":null},"progress":{"max_streak":1,"current_streak":1,"daily_reactions":{"2025-08-01":1},"reactions_given":1,"last_reaction_date":"2025-08-01"},"completed_at":null,"progress_percentage":0,"last_updated":"2025-08-01T23:48:39.466740Z"},{"id":11,"achievement":{"id":62,"name":"Crowd Favorite","description":"Collect 200 upvotes (lifetime)","icon":null,"icon_name":null,"criteria":{"reactions_received":200},"type":"social","challenge_id":"react_100_received","tier":"T4","xp_reward":300,"is_secret":false,"created_at":"2025-08-01T23:23:10.756950Z","background_color":"#FFFFFF","primary_color":"#000000","category":"Social Challenge","reward_skin":null,"icon_data":null,"progress":{"current_count":0,"progress_percentage":0.0},"completed_at":null},"progress":{"max_streak":1,"current_streak":1,"daily_reactions":{"2025-08-01":1},"reactions_given":1,"last_reaction_date":"2025-08-01"},"completed_at":null,"progress_percentage":0,"last_updated":"2025-08-01T23:48:39.497832Z"},{"id":12,"achievement":{"id":63,"name":"Consistency King","description":"Give ≥ 2 upvotes 15 consecutive days","icon":null,"icon_name":null,"criteria":{"daily_reactions":2,"consecutive_days":15},"type":"social","challenge_id":"react_streak_15","tier":"T4","xp_reward":300,"is_secret":false,"created_at":"2025-08-01T23:23:10.762337Z","background_color":"#FFFFFF","primary_color":"#000000","category":"Social Challenge","reward_skin":null,"icon_data":null,"progress":{"current_count":0,"progress_percentage":0.0},"completed_at":null},"progress":{"max_streak":1,"current_streak":1,"daily_reactions":{"2025-08-01":1},"reactions_given":1,"last_reaction_date":"2025-08-01"},"completed_at":null,"progress_percentage":0,"last_updated":"2025-08-01T23:48:39.530577Z"},{"id":13,"achievement":{"id":64,"name":"Weekly Warrior","description":"Complete 3 different weekly challenges","icon":null,"icon_name":null,"criteria":{"weekly_challenges_completed":3},"type":"social","challenge_id":"weekly_challenges_3","tier":"T4","xp_reward":300,"is_secret":false,"created_at":"2025-08-01T23:23:10.767707Z","background_color":"#FFFFFF","primary_color":"#000000","category":"Social Challenge","reward_skin":null,"icon_data":null,"progress":{"current_count":0,"progress_percentage":0.0},"completed_at":null},"progress":{"max_streak":1,"current_streak":1,"daily_reactions":{"2025-08-01":1},"reactions_given":1,"last_reaction_date":"2025-08-01"},"completed_at":null,"progress_percentage":0,"last_updated":"2025-08-01T23:48:39.562259Z"},{"id":14,"achievement":{"id":65,"name":"Hot Pin","description":"Get 40 upvotes on a single pin","icon":null,"icon_name":null,"criteria":{"single_pin_reactions":40},"type":"social","challenge_id":"hot_pin","tier":"T5","xp_reward":400,"is_secret":false,"created_at":"2025-08-01T23:23:10.772979Z","background_color":"#FFFFFF","primary_color":"#000000","category":"Social Challenge","reward_skin":null,"icon_data":null,"progress":{"current_count":0,"progress_percentage":0.0},"completed_at":null},"progress":{"max_streak":1,"current_streak":1,"daily_reactions":{"2025-08-01":1},"reactions_given":1,"last_reaction_date":"2025-08-01"},"completed_at":null,"progress_percentage":0,"last_updated":"2025-08-01T23:48:39.593792Z"},{"id":15,"achievement":{"id":66,"name":"Ripple Effect","description":"50 unique users upvoted your pins","icon":null,"icon_name":null,"criteria":{"unique_reactors":50},"type":"social","challenge_id":"unique_supporters_40","tier":"T5","xp_reward":450,"is_secret":false,"created_at":"2025-08-01T23:23:10.778031Z","background_color":"#FFFFFF","primary_color":"#000000","category":"Social Challenge","reward_skin":null,"icon_data":null,"progress":{"current_count":0,"progress_percentage":0.0},"completed_at":null},"progress":{"max_streak":1,"current_streak":1,"daily_reactions":{"2025-08-01":1},"reactions_given":1,"last_reaction_date":"2025-08-01"},"completed_at":null,"progress_percentage":0,"last_updated":"2025-08-01T23:48:39.624506Z"},{"id":16,"achievement":{"id":67,"name":"Super Hype","description":"Give 750 upvotes total","icon":null,"icon_name":null,"criteria":{"reactions_given":750},"type":"social","challenge_id":"react_500_given","tier":"T6","xp_reward":600,"is_secret":false,"created_at":"2025-08-01T23:23:10.785693Z","background_color":"#FFFFFF","primary_color":"#000000","category":"Social Challenge","reward_skin":null,"icon_data":null,"progress":{"current_count":0,"progress_percentage":0.0},"completed_at":null},"progress":{"max_streak":1,"current_streak":1,"daily_reactions":{"2025-08-01":1},"reactions_given":1,"last_reaction_date":"2025-08-01"},"completed_at":null,"progress_percentage":0,"last_updated":"2025-08-01T23:48:39.657774Z"},{"id":17,"achievement":{"id":68,"name":"Multi-Hit Maker","description":"3 pins with ≥ 30 upvotes each","icon":null,"icon_name":null,"criteria":{"reactions_per_pin":30,"pins_with_reactions":3},"type":"social","challenge_id":"multi_hit","tier":"T6","xp_reward":650,"is_secret":false,"created_at":"2025-08-01T23:23:10.790908Z","background_color":"#FFFFFF","primary_color":"#000000","category":"Social Challenge","reward_skin":null,"icon_data":null,"progress":{"current_count":0,"progress_percentage":0.0},"completed_at":null},"progress":{"max_streak":1,"current_streak":1,"daily_reactions":{"2025-08-01":1},"reactions_given":1,"last_reaction_date":"2025-08-01"},"completed_at":null,"progress_percentage":0,"last_updated":"2025-08-01T23:48:39.688730Z"},{"id":18,"achievement":{"id":69,"name":"Comment Legend","description":"Write 100 comments","icon":null,"icon_name":null,"criteria":{"comments_given":100},"type":"social","challenge_id":"comments_100","tier":"T6","xp_reward":650,"is_secret":false,"created_at":"2025-08-01T23:23:10.795900Z","background_color":"#FFFFFF","primary_color":"#000000","category":"Social Challenge","reward_skin":null,"icon_data":null,"progress":{"current_count":0,"progress_percentage":0.0},"completed_at":null},"progress":{"max_streak":1,"current_streak":1,"daily_reactions":{"2025-08-01":1},"reactions_given":1,"last_reaction_date":"2025-08-01"},"completed_at":null,"progress_percentage":0,"last_updated":"2025-08-01T23:48:39.720252Z"},{"id":19,"achievement":{"id":70,"name":"Social Icon","description":"500 upvotes received + 100 unique supporters","icon":null,"icon_name":null,"criteria":{"unique_reactors":100,"reactions_received":500},"type":"social","challenge_id":"react_350_received","tier":"T7","xp_reward":800,"is_secret":false,"created_at":"2025-08-01T23:23:10.800918Z","background_color":"#FFFFFF","primary_color":"#000000","category":"Social Challenge","reward_skin":null,"icon_data":null,"progress":{"current_count":0,"progress_percentage":0.0},"completed_at":null},"progress":{"max_streak":1,"current_streak":1,"daily_reactions":{"2025-08-01":1},"reactions_given":1,"last_reaction_date":"2025-08-01"},"completed_at":null,"progress_percentage":0,"last_updated":"2025-08-01T23:48:39.750770Z"},{"id":20,"achievement":{"id":71,"name":"Viral Pin","description":"One pin hits 75 upvotes","icon":null,"icon_name":null,"criteria":{"single_pin_reactions":75},"type":"social","challenge_id":"viral_pin","tier":"T7","xp_reward":1000,"is_secret":false,"created_at":"2025-08-01T23:23:10.806090Z","background_color":"#FFFFFF","primary_color":"#000000","category":"Social Challenge","reward_skin":null,"icon_data":null,"progress":{"current_count":0,"progress_percentage":0.0},"completed_at":null},"progress":{"max_streak":1,"current_streak":1,"daily_reactions":{"2025-08-01":1},"reactions_given":1,"last_reaction_date":"2025-08-01"},"completed_at":null,"progress_percentage":0,"last_updated":"2025-08-01T23:48:39.781955Z"}]}
flutter: ⏰ Timestamp: 2025-08-01T21:30:31.586158
flutter: ✅ Request successful
flutter: ========================
flutter:
flutter: ✅ Added POI icon: poi-hotel
flutter: ✅ Added POI icon: poi-shop
flutter: 🎨 No cached pins, applying filter
flutter: 🔍 Stopping scanning animation
flutter: 🔍 Removed scanning layers
flutter: 🔍 Starting scanning animation
flutter: 📍 User avatar image already added, skipping
flutter: ✅ Created new user avatar symbol
flutter: 🔝 User avatar repositioned on top of all layers
flutter: ✅ Added POI icon: poi-hospital
flutter: ✅ Added POI icon: poi-museum
flutter:
flutter: 📥 === API RESPONSE ===
flutter: 🎯 GET https://api.bopmaps.com/api/gamification/user-achievements/in_progress_by_category/?category=artist
flutter: 📊 Status Code: 200
flutter: 📋 Response Headers: {connection: keep-alive, transfer-encoding: chunked, date: Sat, 02 Aug 2025 02:30:31 GMT, content-encoding: gzip, vary: Accept, origin, Cookie, strict-transport-security: max-age=********; includeSubDomains; preload, referrer-policy: same-origin, report-to: {"group":"cf-nel","max_age":604800,"endpoints":[{"url":"https://a.nel.cloudflare.com/report/v4?s=IWDPQWqy17UEjqWTX7leS1dGH%2FOOY0qwW8lPWSJa1%2B9UG88BoKrZxijcRbzq8deoC0Gnq%2BVWvREcA1or%2BV9H%2FCWiRKjnMfC1hOQQoakBow%3D%3D"}]}, cf-cache-status: DYNAMIC, content-type: application/json, cross-origin-opener-policy: same-origin, server: cloudflare, alt-svc: h3=":443"; ma=86400, nel: {"report_to":"cf-nel","success_fraction":0.0,"max_age":604800}, cf-ray: 968a275c58fd5824-IAD, x-frame-options: DENY, x-content-type-options: nosniff, allow: GET, HEAD, OPTIONS}
flutter: 📦 Response Body: []
flutter: ⏰ Timestamp: 2025-08-01T21:30:31.630777
flutter: ✅ Request successful
flutter: ========================
flutter:
flutter:
flutter: 📥 === API RESPONSE ===
flutter: 🎯 GET https://api.bopmaps.com/api/gamification/achievements/rank_badge_data/
flutter: 📊 Status Code: 200
flutter: 📋 Response Headers: {connection: keep-alive, transfer-encoding: chunked, date: Sat, 02 Aug 2025 02:30:31 GMT, content-encoding: gzip, vary: Accept, origin, Cookie, strict-transport-security: max-age=********; includeSubDomains; preload, referrer-policy: same-origin, report-to: {"group":"cf-nel","max_age":604800,"endpoints":[{"url":"https://a.nel.cloudflare.com/report/v4?s=KfybntjnhgSe%2F%2Fedmxai6A0%2BoHKPRAWIPwUffjxNtnezKaDwLFllDKpSumZjDRBRVb7NMUADND1WuKOaBx91dTkYfAk33g%2FIm9Bc1xW5QA%3D%3D"}]}, cf-cache-status: DYNAMIC, content-type: application/json, cross-origin-opener-policy: same-origin, server: cloudflare, alt-svc: h3=":443"; ma=86400, nel: {"report_to":"cf-nel","success_fraction":0.0,"max_age":604800}, cf-ray: 968a275c5bdb59ec-IAD, x-frame-options: DENY, x-content-type-options: nosniff, allow: GET, HEAD, OPTIONS}
flutter: 📦 Response Body: {"current_rank":{"id":"level_1","name":"Basement Bopper","level":1,"emoji":"🎧","primary_color":"#1A1A1A","background_color":"#2D2D2D"},"next_rank":{"id":"level_2","name":"Selector","level":2,"emoji":"🎵","primary_color":"#4A90E2","background_color":"#2171C7"},"total_xp":25,"current_xp":25,"next_level_xp":500,"xp_progress":5.0,"user_level":1,"all_ranks":[{"id":"level_1","name":"Basement Bopper","level":1,"required_xp":0,"emoji":"🎧","primary_color":"#1A1A1A","background_color":"#2D2D2D","unlocked":true},{"id":"level_2","name":"Selector","level":2,"required_xp":500,"emoji":"🎵","primary_color":"#4A90E2","background_color":"#2171C7","unlocked":false},{"id":"level_3","name":"Tastemaker","level":3,"required_xp":1500,"emoji":"🎶","primary_color":"#9B51E0","background_color":"#6B2E9E","unlocked":false},{"id":"level_4","name":"Trendsetter","level":4,"required_xp":3500,"emoji":"🎤","primary_color":"#F2994A","background_color":"#D97B29","unlocked":false},{"id":"level_5","name":"Icon","level":5,"required_xp":7000,"emoji":"⭐","primary_color":"#EB5757","background_color":"#C62828","unlocked":false},{"id":"level_6","name":"Architect","level":6,"required_xp":12000,"emoji":"🏗️","primary_color":"#27AE60","background_color":"#1E8449","unlocked":false},{"id":"level_7","name":"Legend","level":7,"required_xp":20000,"emoji":"👑","primary_color":"#F2C94C","background_color":"#DBA520","unlocked":false}],"recent_achievements":[{"name":"First React","xp_earned":25,"completed_at":"2025-08-01T23:48:39.183728Z","icon":{"name":"emoji_events","color":"#000000"}}],"close_achievements":[],"stats":{"total_achievements":1,"total_possible":74,"completion_rate":1.4}}
flutter: ⏰ Timestamp: 2025-08-01T21:30:31.632025
flutter: ✅ Request successful
flutter: ========================
flutter:
flutter:
flutter: 🎯 Parsing rank data from API:
flutter: - Rank ID: level_1
flutter: - Level: 1
flutter: 🎯 Getting rank for level 1
flutter: 📊 Selected rank: Basement Bopper (required level: 1)
flutter: - Selected rank by level: Basement Bopper
flutter:
flutter: 🎯 Parsing rank data from API:
flutter: - Rank ID: level_2
flutter: - Level: 2
flutter: 🎯 Getting rank for level 2
flutter: 📊 Selected rank: Selector (required level: 2)
flutter: - Selected rank by level: Selector
flutter:
flutter: 🚀 === API REQUEST ===
flutter: 📤 GET https://api.bopmaps.com/api/gamification/skins/unlocked/
flutter: 📋 Headers: {Content-Type: application/json, Accept: application/json, Authorization: Bearer [HIDDEN]}
flutter: ⏰ Timestamp: 2025-08-01T21:30:31.646282
flutter:
flutter: ✅ Full profile fetched successfully
flutter: 👤 Username: mothusom68
flutter: 📧 Email: <EMAIL>
flutter: 📝 Bio: None
flutter: 🎵 Spotify: false
flutter: 🍎 Apple Music: false
flutter: 🎤 Raw top_artists: [Playboi Carti, Ken Carson, Lil Uzi Vert, Yeat, SABAI, ILLENIUM, Seven Lions, YOASOBI, Lil Tecca, SoFaygo]
flutter: 🎼 Raw top_genres: [deep house, rage rap, electro pop, emo rap, future bass, pop rap, j-pop, hyperpop, punk rock, melodic bass, melodic rap, cloud rap, electronic, lo-fi]
flutter: 🎯 Raw artist_genres: {Yeat: [rage rap], SABAI: [melodic bass, future bass, edm], SoFaygo: [rage rap], YOASOBI: [j-pop, anime], ILLENIUM: [melodic bass, future bass, edm], Lil Tecca: [melodic rap, rap], Ken Carson: [rage rap], Seven Lions: [melodic bass, future bass, edm, chillstep, dubstep, progressive trance], Lil Uzi Vert: [melodic rap], Playboi Carti: [rage rap]}
flutter: 🖼️ Raw artist_image_urls: {Yeat: https://i.scdn.co/image/ab6761610000e5eb15fda24afae244cbd5c2dfac, SoFaygo: https://i.scdn.co/image/ab6761610000e5ebac01d197d00d16cee2f2dc7d, YOASOBI: https://i.scdn.co/image/ab6761610000e5eb507349709ae19263301a62f7, ILLENIUM: https://i.scdn.co/image/ab6761610000e5eb98e56c05d6acbf782609afa2, Lil Tecca: https://i.scdn.co/image/ab6761610000e5eb5433d9d8519ad0635d8bd869, Ken Carson: https://i.scdn.co/image/ab6761610000e5eb95ccca370d8bd50e84c222bc, Seven Lions: https://i.scdn.co/image/ab6761610000e5ebb9b5d42437456af7b87f2f8e, Lil Uzi Vert: https://i.scdn.co/image/ab6761610000e5eba8ce348f34f18241d3249fa9, Playboi Carti: https://i.scdn.co/image/ab6761610000e5ebba50ca67ffc3097f6ea1710a}
flutter: 🎵 Raw artist_spotify_ids: {Yeat: 3qiHUAX7zY4Qnjx8TNUzVx, SoFaygo: 2SJhf6rTOU53g8yBdAjPby, YOASOBI: 64tJ2EAv1R6UaZqc4iOCyj, ILLENIUM: 45eNHdiiabvmbp4erw26rg, Lil Tecca: 4Ga1P7PMIsmqEZqhYZQgDo, Ken Carson: 3gBZUcNeVumkeeJ19CY2sX, Seven Lions: 6fcTRFpz0yH79qSKfof7lp, Lil Uzi Vert: 4O15NlyKLIASxsJ0PrXPfz, Playboi Carti: 699OTQXzgjhIYAHMy9RyPD}
flutter: ✅ Added POI icon: poi-school
flutter: ✅ Added POI icon: poi-library
flutter: 💾 Full profile cached successfully
flutter: 💾 Header data cached successfully
flutter: 🎵 Loaded 10 artists from user profile
flutter: 🎵 FINAL: 10 total artists from user profile
flutter: 👤 [AIRecommendationService] Received 10 user top artists.
flutter: 📍 Received position update: lat: 36.**************, lng: -86.**************, accuracy: 10.476811082671006m, speed: 0.0m/s
flutter: 📍 User avatar image already added, skipping
flutter: 📍 SmartFetch: Handling location update at 36.**************, -86.**************
flutter: 📍 SmartFetch: Fetch already in progress, ignoring location update
flutter: ✅ Added POI icon: poi-bank
flutter: 🔍 Added scanning layers on top of all other layers
flutter: 🗺️ LocationManager: Location acquired in 579ms: lat: 36.**************, lng: -86.**************, accuracy: 10.476811082671006m, speed: 0.0m/s, altitude: 159.99554590022845m
flutter: 🗺️ LocationManager: Initial position set: lat: 36.**************, lng: -86.**************, accuracy: 10.476811082671006m
flutter: 🗺️ LocationManager: Location success rate: 66.7% (2/3)
flutter: 🗺️ LocationManager: Starting foreground position stream
flutter: 🗺️ LocationManager: Foreground location tracking started successfully
flutter: MapProvider: Position update received - pin refresh handled by SmartLocationFetchManager
flutter: 🎵 [MapScreen] Build - currentlyPlayingPinId: null
flutter: 🎵 [MapScreen] Build - hasCurrentlyPlayingPin: false
flutter: 🎵 [MapScreen] _buildCaptionPopup called
flutter: 🎵 [MapScreen] - currentlyPlayingPinId: null
flutter: 🎵 [MapScreen] - currentlyPlayingPinData: null
flutter: 🎵 [MapScreen] ❌ No currently playing pin or pin data
flutter: NavigationHelper: Getting current tab...
flutter: NavigationHelper: Checking route - name: /map, looking for: /profile, match: false
flutter: NavigationHelper: Checking route - name: /map, looking for: /explore, match: false
flutter: NavigationHelper: Checking route - name: /map, looking for: /friends, match: false
flutter: NavigationHelper: Checking route - name: /map, looking for: /challenges, match: false
flutter: NavigationHelper: Checking route - name: /map, looking for: /map, match: true
flutter: NavigationHelper: Current tab is MAP (explicit)
flutter: 🎨 No cached pins, applying filter
flutter: 🔍 Stopping scanning animation
flutter: 🔍 Removed scanning layers
flutter: 🔍 Starting scanning animation
flutter: 📍 User avatar image already added, skipping
flutter: ✅ Added POI icon: poi-park
flutter: ✅ Loaded stored Apple Music authentication
flutter: ✅ Apple Music service initialized. Authenticated: true
flutter: 🌐 Making Apple Music API request to: https://api.music.apple.com/v1/me/library/playlists?limit=100
flutter: ✅ Loaded stored Apple Music authentication
flutter: ✅ Apple Music service initialized. Authenticated: true
flutter: ✅ Added POI icon: poi-shop
flutter: ✅ Created new user avatar symbol
flutter: 🔝 User avatar repositioned on top of all layers
flutter: ✅ Added POI icon: poi-default
flutter: ✅ Added POI icon: poi-museum
flutter: ✅ Added POI icon: poi-library
flutter: ✅ Added POI icon: poi-park
flutter: 🔍 Added scanning layers on top of all other layers
flutter: ✅ Added POI icon: poi-default
flutter:
flutter: 📥 === API RESPONSE ===
flutter: 🎯 GET https://api.bopmaps.com/api/gamification/skins/unlocked/
flutter: 📊 Status Code: 404
flutter: 📋 Response Headers: {connection: keep-alive, transfer-encoding: chunked, date: Sat, 02 Aug 2025 02:30:31 GMT, content-encoding: gzip, vary: origin, Cookie, strict-transport-security: max-age=********; includeSubDomains; preload, referrer-policy: same-origin, report-to: {"group":"cf-nel","max_age":604800,"endpoints":[{"url":"https://a.nel.cloudflare.com/report/v4?s=3PrhjAshQr0jfwZcajxqk0bfpmgaV%2BMU8wK%2BH8LlWr9EeraSdTJmECjx2r4lx7lM%2F4QYJEB0OCl6v%2Fn%2FnJWnk6vgJFrpMySfETIdtxlGXA%3D%3D"}]}, cf-cache-status: DYNAMIC, content-type: text/html; charset=utf-8, cross-origin-opener-policy: same-origin, server: cloudflare, alt-svc: h3=":443"; ma=86400, nel: {"report_to":"cf-nel","success_fraction":0.0,"max_age":604800}, cf-ray: 968a275ffccee647-IAD, x-frame-options: DENY, x-content-type-options: nosniff}
flutter: 📦 Response Body: 
<!doctype html>
<html lang="en">
<head>
  <title>Not Found</title>
</head>
<body>
  <h1>Not Found</h1><p>The requested resource was not found on this server.</p>
</body>
</html>
flutter: ⏰ Timestamp: 2025-08-01T21:30:31.781936
flutter: ⚠️ Client error - check request
flutter: ========================
flutter:
flutter: ❌ Error loading user rank data: A GamificationProvider was used after being disposed.
flutter: Once you have called dispose() on a GamificationProvider, it can no longer be used.
flutter: 🌐 Making Apple Music API request to: https://api.music.apple.com/v1/me/library/songs?limit=25
NSPredicate: Use of 'MLN_IF' as an NSExpression function is forbidden.
NSPredicate: Use of 'mgl_does:have:' as an NSExpression function is forbidden.
flutter: 🎯 Added POI labels with icons
flutter: Could not add POI labels: PlatformException(layerAlreadyExists, Layer already exists, Layer with id poi-labels already exists., null)
flutter: 📍 🧹 Cleared ALL layer features
flutter: 📍 🧹 Safe to clear layer features - no progressive rendering active
flutter: 📍 🧹 Cleared ALL layer features
flutter: 📍 Cleared 0 pins and 0 clusters
flutter: 📍 Filter change - using smart fetch manager
flutter: 📍 SmartFetch: Updated filter to PinFilterType.all
flutter: 📍 SmartFetch: Force refresh requested
flutter: 📍 SmartFetch: Fetch already in progress, skipping
flutter: 📍 Waiting for smart fetch manager to complete...
flutter: ✓ [SpotifyService] Got 78 playlist tracks (offset: 40, total available: 118)
flutter: ✅ Added 20 tracks from "Mood Booster: the Happy Playlist"
flutter: 🎵 [SpotifyService] Getting playlist tracks (playlist: 0okKcRyYEwq8guFxzAPtlB, limit: 80, offset: 0)
flutter: 🌍 [SpotifyService] Adding market parameter for direct Spotify API
flutter: ✅ API Response status: 200
flutter: 📋 Response body preview: {"data":[{"id":"p.oOzA1RvSlDQ5qRQ","type":"library-playlists","href":"/v1/me/library/playlists/p.oOzA1RvSlDQ5qRQ","attributes":{"lastModifiedDate":"2024-08-12T22:23:22Z","canEdit":true,"name":"Alternative/Other","isPublic":false,"description":{"standard":""},"artwork":{"width":null,"height":null,"url":"https://store-032.blobstore.apple.com/sq-mq-us-032-000002/cc/71/d0/cc71d000-3834-f858-fdef-d8d2fbed8ceb/image?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20250802T023032Z&X-Amz-SignedHeaders=host&...
flutter: ✅ Loaded 49 initial Apple Music playlists structure.
flutter: 🔍 Sample initial playlist raw data: {id: p.oOzA1RvSlDQ5qRQ, type: library-playlists, href: /v1/me/library/playlists/p.oOzA1RvSlDQ5qRQ, attributes: {lastModifiedDate: 2024-08-12T22:23:22Z, canEdit: true, name: Alternative/Other, isPublic: false, description: {standard: }, artwork: {width: null, height: null, url: https://store-032.blobstore.apple.com/sq-mq-us-032-000002/cc/71/d0/cc71d000-3834-f858-fdef-d8d2fbed8ceb/image?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20250802T023032Z&X-Amz-SignedHeaders=host&X-Amz-Expires=86400&X-Amz-Credential=MKIAJC19DXS75RV205ZP%2F20250802%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Signature=7da2e8974b405be15cc79bfac29f4d71b7be6ec9a31dfbf16ae8929bcca084d9}, hasCatalog: false, playParams: {id: p.oOzA1RvSlDQ5qRQ, kind: playlist, isLibrary: true}, dateAdded: 2023-01-02T02:19:26Z}}
flutter: 🔍 Playlist: Alternative/Other (attributes keys: [lastModifiedDate, canEdit, name, isPublic, description, artwork, hasCatalog, playParams, dateAdded])
flutter: 🔍 Playlist: Beats/Instrumentals (attributes keys: [lastModifiedDate, canEdit, name, description, isPublic, artwork, hasCatalog, dateAdded, playParams])
flutter: 🔍 Playlist: Classic Trap (attributes keys: [lastModifiedDate, canEdit, name, description, isPublic, artwork, hasCatalog, dateAdded, playParams])
flutter: 🔍 Playlist: Damn :( (attributes keys: [lastModifiedDate, canEdit, name, isPublic, description, artwork, hasCatalog, dateAdded, playParams])
flutter: 🔍 Playlist: Drill (attributes keys: [lastModifiedDate, canEdit, name, isPublic, description, artwork, hasCatalog, playParams, dateAdded])
flutter: 🔍 Playlist: Drive (attributes keys: [lastModifiedDate, canEdit, name, isPublic, description, artwork, hasCatalog, dateAdded, playParams])
flutter: 🔍 Playlist: EDM/Future Bass (attributes keys: [lastModifiedDate, canEdit, name, isPublic, description, artwork, hasCatalog, dateAdded, playParams])
flutter: 🔍 Playlist: Favorite Songs (attributes keys: [lastModifiedDate, canEdit, name, isPublic, artwork, hasCatalog, dateAdded, playParams])
flutter: 🔍 Playlist: Go 🔥 (attributes keys: [lastModifiedDate, canEdit, name, isPublic, description, artwork, hasCatalog, playParams, dateAdded])
flutter: 🔍 Playlist: Hip Hop/Rap (attributes keys: [lastModifiedDate, canEdit, name, isPublic, description, artwork, hasCatalog, dateAdded, playParams])
flutter: 🔍 Playlist: Hypertrap (attributes keys: [lastModifiedDate, canEdit, name, isPublic, description, artwork, hasCatalog, playParams, dateAdded])
flutter: 🔍 Playlist: Inspirational & Fast Rap (attributes keys: [lastModifiedDate, canEdit, name, description, isPublic, artwork, hasCatalog, playParams, dateAdded])
flutter: 🔍 Playlist: Japanese/Anime (attributes keys: [lastModifiedDate, canEdit, name, isPublic, description, artwork, hasCatalog, dateAdded, playParams])
flutter: 🔍 Playlist: Let’s Go 🔥 (attributes keys: [lastModifiedDate, canEdit, name, isPublic, hasCatalog, playParams, dateAdded])
flutter: 🔍 Playlist: Let’s try this again (attributes keys: [lastModifiedDate, canEdit, name, isPublic, description, artwork, hasCatalog, playParams, dateAdded])
flutter: 🔍 Playlist: Lit 🔥 (attributes keys: [lastModifiedDate, canEdit, name, isPublic, description, artwork, hasCatalog, playParams, dateAdded])
flutter: 🔍 Playlist: LoFi & Chill Rap (attributes keys: [lastModifiedDate, canEdit, name, isPublic, description, artwork, hasCatalog, playParams, dateAdded])
flutter: 🔍 Playlist: lol (attributes keys: [lastModifiedDate, canEdit, name, description, isPublic, artwork, hasCatalog, playParams, dateAdded])
flutter: 🔍 Playlist: My Shazam Tracks (attributes keys: [lastModifiedDate, canEdit, name, description, isPublic, artwork, hasCatalog, playParams, dateAdded])
flutter: 🔍 Playlist: Not Faded  (attributes keys: [lastModifiedDate, canEdit, name, description, isPublic, artwork, hasCatalog, playParams, dateAdded])
flutter: 🔍 Playlist: Old School (attributes keys: [lastModifiedDate, canEdit, name, isPublic, description, artwork, hasCatalog, playParams, dateAdded])
flutter: 🔍 Playlist: pain (attributes keys: [lastModifiedDate, canEdit, name, isPublic, description, artwork, hasCatalog, playParams, dateAdded])
flutter: 🔍 Playlist: Pop (attributes keys: [lastModifiedDate, canEdit, name, description, isPublic, artwork, hasCatalog, playParams, dateAdded])
flutter: 🔍 Playlist: R&B/Chill (attributes keys: [lastModifiedDate, canEdit, name, description, isPublic, artwork, hasCatalog, dateAdded, playParams])
flutter: 🔍 Playlist: Rebirth (attributes keys: [lastModifiedDate, canEdit, name, isPublic, description, artwork, hasCatalog, dateAdded, playParams])
flutter: 🔍 Playlist: Reset (attributes keys: [lastModifiedDate, canEdit, name, description, isPublic, artwork, hasCatalog, playParams, dateAdded])
flutter: 🔍 Playlist: Rock/Punk (attributes keys: [lastModifiedDate, canEdit, name, isPublic, description, artwork, hasCatalog, dateAdded, playParams])
flutter: 🔍 Playlist: sad hype  (attributes keys: [lastModifiedDate, canEdit, name, isPublic, description, artwork, hasCatalog, playParams, dateAdded])
flutter: 🔍 Playlist: Sad Rap (attributes keys: [lastModifiedDate, canEdit, name, description, isPublic, artwork, hasCatalog, dateAdded, playParams])
flutter: 🔍 Playlist: Trap (attributes keys: [lastModifiedDate, canEdit, name, description, isPublic, artwork, hasCatalog, dateAdded, playParams])
flutter: 🔍 Playlist: Unreleased (attributes keys: [lastModifiedDate, canEdit, name, isPublic, description, artwork, hasCatalog, dateAdded, playParams])
flutter: 🔍 Playlist: Zone (attributes keys: [lastModifiedDate, canEdit, name, description, isPublic, artwork, hasCatalog, playParams, dateAdded])
flutter: 🔍 Playlist: #Hyperpop//Glitchc0re (attributes keys: [lastModifiedDate, canEdit, name, isPublic, description, artwork, hasCatalog, dateAdded, playParams])
flutter: 🔍 Playlist: 🌟 (attributes keys: [lastModifiedDate, canEdit, name, isPublic, description, hasCatalog, playParams, dateAdded])
flutter: 🔍 Playlist: 2000s and 2010s classics (attributes keys: [lastModifiedDate, canEdit, name, description, isPublic, artwork, hasCatalog, dateAdded, playParams])
flutter: ✅ Loaded 35 Apple Music playlists
flutter: 🎵 [MapScreen] _onMusicSourceChanged() called
flutter: 🎵 [MapScreen] Early return - mounted: true, hasPin: false
flutter: ❌ [SpotifyService] No access token found in either storage location
flutter: ✅ API Response status: 200
flutter: 📋 Response body preview: {"next":"/v1/me/library/songs?offset=25","data":[{"id":"i.rXzeOeqTD51VOL1","type":"library-songs","href":"/v1/me/library/songs/i.rXzeOeqTD51VOL1","attributes":{"albumName":"C.N.O.T.E Vs Gucci: Collectors Edition","discNumber":1,"genreNames":["Hip-Hop/Rap"],"hasLyrics":true,"trackNumber":12,"releaseDate":"2013-08-13","durationInMillis":290070,"name":"Activist (feat. Peeweelongway)","artistName":"Da Honorable C.N.O.T.E. & Gucci Mane","contentRating":"explicit","artwork":{"width":1200,"height":1200...
flutter: ✅ Loaded 25 Apple Music liked songs
flutter: 🎵 [MapScreen] _onMusicSourceChanged() called
flutter: 🎵 [MapScreen] Early return - mounted: true, hasPin: false
flutter: 🎵 [MapScreen] _onMusicSourceChanged() called
flutter: 🎵 [MapScreen] Early return - mounted: true, hasPin: false
flutter: ✅ Apple Music provider initialized. Connected: true
flutter: 📍 Found 27 all pins (list)
flutter: 📍 SmartFetch: Successfully fetched 27 pins
flutter: 📍 SmartFetch: Received 27 pins from API
flutter: 📍 SmartFetch: Updating cache with 27 pins
flutter: 📍 SmartFetch: Cache before clear: 0 pins
flutter: 📍 SmartFetch: Cache after update: 27 pins
flutter: 📍 Updating map pins from cache (27 pins)
flutter: 📍 🔍 Cache analysis: 27 total pins, 0 already rendered
flutter: 📍 Using progressive rendering for 27 cached pins (0 already rendered)
flutter: 📍 🚀 Starting progressive pin rendering (context: RenderingContext.incrementalUpdate)...
flutter: 📍 🏗️ Initializing empty layers...
flutter: 📍 ⚠️ Skipping layer features clear - progressive rendering is active
flutter: 📍 ⚠️ isPinFetchInProgress: true, timer active: null
flutter: 📍 Cleared 0 pins and 0 clusters
flutter: 📍 🏗️ Setting initial layer visibility: currentZoom=16.0, shouldCluster=false, threshold=14.0
flutter: 📍 ✅ Empty layers initialized, ready for progressive updates (showing individual pins)
flutter: 📍 _getCachedOrFetchPins called - returning cached pins only
flutter: 📍 Returning 27 cached pins
flutter: 📍 SmartFetch: Completed fetching pins
flutter: 📍 🔍 Total pins: 27, Already rendered: 0, Unrendered: 27
flutter: 📍 🎯 Sorting 27 pins by distance from user at (36.**************, -86.**************)
flutter: 📍 🎯 Sorted pins by distance - nearest: 0.3m, farthest: 4699.1m
flutter: 📍 🚀 Added 27 pins to progressive rendering queue (sorted by distance)
flutter: 📍 🚀 Pin IDs: [31, 22, 5, 3, 11, 10, 23, 8, 1, 25, 2, 30, 21, 29, 4, 20, 27, 6, 9, 28, 18, 15, 26, 24, 19, 13, 14]
flutter: 📍 🔄 Starting progressive rendering timer with 27 pending pins...
flutter: 📍 🔄 Current state: _isPinFetchInProgress=true
flutter: 📍 🔍 Filtered to 27 unique unrendered pins
flutter: 📍 ⚡ Processing first batch immediately...
flutter: 📍 ⚡ Processing batch of 5 NEW pins (0 already rendered, 22 remaining)
flutter: 📍 🔄 Building batch of 5 pins to BOTH layers in parallel
flutter: 📍 ⚡ Batch processed - updating layer visibility: shouldCluster=false, current zoom=16.0 (5 pins added, 5 total)
flutter: 📍 Enhanced layer switching: showClusters=false
flutter: 📍 Current zoom: 16.0, clustering threshold: 14.0
flutter: 📍 Current layer state: LayerState.initializing
flutter: 📍 Progressive rendering check: inProgress=true, enabled=true, pending=22
flutter: 📍 🚀 Individual layer requested during progressive rendering - pins already being rendered progressively
flutter: 📍 State transition requested: LayerState.initializing → LayerState.showingIndividual
flutter: 📍 Layer state changed: LayerState.initializing → LayerState.transitioning
flutter: 📍 State updated: LayerState.initializing → LayerState.transitioning
flutter: 📍 🔧 Computing pin features for 5 pins in parallel isolate
flutter: 📍 🔍 About to update cluster layer with 5 items
flutter: 📍 🔍 - Clusters: 0
flutter: 📍 🔍 - Individual pins: 5
flutter: 📍 🎯 Updating cluster layer: 5 items, 0 existing pins, 0 existing clusters
flutter: 📍 🎯 Adding NEW individual pin feature in cluster view: 31
[ERROR:flutter/runtime/dart_vm_initializer.cc(40)] Unhandled Exception: PlatformException(layerNotFound, Layer not found, Layer with id individual-pins-layer not found., null)
#0      StandardMethodCodec.decodeEnvelope (package:flutter/src/services/message_codecs.dart:652:7)
#1      MethodChannel._invokeMethod (package:flutter/src/services/platform_channel.dart:370:18)
<asynchronous suspension>
#2      MapLibreMethodChannel.setLayerVisibility (package:maplibre_gl_platform_interface/src/method_channel_maplibre_gl.dart:801:5)
<asynchronous suspension>
[ERROR:flutter/runtime/dart_vm_initializer.cc(40)] Unhandled Exception: PlatformException(layerNotFound, Layer not found, Layer with id cluster-pins-layer not found., null)
#0      StandardMethodCodec.decodeEnvelope (package:flutter/src/services/message_codecs.dart:652:7)
#1      MethodChannel._invokeMethod (package:flutter/src/services/platform_channel.dart:370:18)
<asynchronous suspension>
#2      MapLibreMethodChannel.setLayerVisibility (package:maplibre_gl_platform_interface/src/method_channel_maplibre_gl.dart:801:5)
<asynchronous suspension>
flutter: 📊 Parallel processing: 5 pins in 43ms
flutter: 📊 Average processing time: 43ms per batch
flutter: 📊 Total pins processed: 5
flutter: 📍 🔧 Parallel isolate returned 5 features
flutter: 📍 🔧 Processing feature: 31
flutter: 📊 Parallel processing: 1 pins in 38ms
flutter: 📊 Average processing time: 40ms per batch
flutter: 📊 Total pins processed: 6
flutter: 📍 🎯 Adding NEW individual pin feature in cluster view: 22
flutter: 📊 Parallel processing: 1 pins in 9ms
flutter: 📊 Average processing time: 30ms per batch
flutter: 📊 Total pins processed: 7
flutter: 📍 🎯 Adding NEW individual pin feature in cluster view: 5
flutter: 📊 Parallel processing: 1 pins in 11ms
flutter: 📊 Average processing time: 25ms per batch
flutter: 📊 Total pins processed: 8
flutter: 📍 🎯 Adding NEW individual pin feature in cluster view: 3
flutter:
flutter: 📥 === API RESPONSE ===
flutter: 🎯 GET https://api.bopmaps.com/api/gamification/user-achievements/completed_by_category/?category=genre
flutter: 📊 Status Code: 200
flutter: 📋 Response Headers: {connection: keep-alive, transfer-encoding: chunked, date: Sat, 02 Aug 2025 02:30:32 GMT, content-encoding: gzip, vary: Accept, origin, Cookie, strict-transport-security: max-age=********; includeSubDomains; preload, referrer-policy: same-origin, report-to: {"group":"cf-nel","max_age":604800,"endpoints":[{"url":"https://a.nel.cloudflare.com/report/v4?s=rd%****************************************%2BIcG3baXUTMnGbJDrvP4lc1futWxRa5HR6FUdIe9FkgzbYuOg8Q4%2FvOqH8y7rAz4A%3D%3D"}]}, cf-cache-status: DYNAMIC, content-type: application/json, cross-origin-opener-policy: same-origin, server: cloudflare, alt-svc: h3=":443"; ma=86400, nel: {"report_to":"cf-nel","success_fraction":0.0,"max_age":604800}, cf-ray: 968a275bc9981459-IAD, x-frame-options: DENY, x-content-type-options: nosniff, allow: GET, HEAD, OPTIONS}
flutter: 📦 Response Body: []
flutter: ⏰ Timestamp: 2025-08-01T21:30:32.504687
flutter: ✅ Request successful
flutter: ========================
flutter:
flutter: ✅ Loaded 0 achievements for category genre using optimized endpoints
flutter: ⚠️ No achievements found for category genre
flutter: 📍 ✅ Created empty individual pins layer
flutter: 📍 ✅ Created empty cluster layer
flutter:
flutter: 📥 === API RESPONSE ===
flutter: 🎯 GET https://api.bopmaps.com/api/gamification/user-achievements/in_progress_by_category/?category=location
flutter: 📊 Status Code: 200
flutter: 📋 Response Headers: {connection: keep-alive, transfer-encoding: chunked, date: Sat, 02 Aug 2025 02:30:32 GMT, content-encoding: gzip, vary: Accept, origin, Cookie, strict-transport-security: max-age=********; includeSubDomains; preload, referrer-policy: same-origin, report-to: {"group":"cf-nel","max_age":604800,"endpoints":[{"url":"https://a.nel.cloudflare.com/report/v4?s=o65geoQQX%2F0qDoGgejQtedfxxgVtdRPJ3mRzghSGyzfH%2Fk%2FErm8ZCCud9C0HLAAAE5UvU3hvrIeqsXvFb0%2FJEBq40F%2Bi%2B04PWyX5CZ0b6A%3D%3D"}]}, cf-cache-status: DYNAMIC, content-type: application/json, cross-origin-opener-policy: same-origin, server: cloudflare, alt-svc: h3=":443"; ma=86400, nel: {"report_to":"cf-nel","success_fraction":0.0,"max_age":604800}, cf-ray: 968a275be83c3b47-IAD, x-frame-options: DENY, x-content-type-options: nosniff, allow: GET, HEAD, OPTIONS}
flutter: 📦 Response Body: []
flutter: ⏰ Timestamp: 2025-08-01T21:30:32.516725
flutter: ✅ Request successful
flutter: ========================
flutter:
flutter: ✅ Loaded 0 achievements for category location using optimized endpoints
flutter: ⚠️ No achievements found for category location
flutter:
flutter: 📥 === API RESPONSE ===
flutter: 🎯 GET https://api.bopmaps.com/api/gamification/user-achievements/completed_summary/
flutter: 📊 Status Code: 200
flutter: 📋 Response Headers: {connection: keep-alive, transfer-encoding: chunked, date: Sat, 02 Aug 2025 02:30:32 GMT, content-encoding: gzip, vary: Accept, origin, Cookie, strict-transport-security: max-age=********; includeSubDomains; preload, referrer-policy: same-origin, report-to: {"group":"cf-nel","max_age":604800,"endpoints":[{"url":"https://a.nel.cloudflare.com/report/v4?s=mC5wEHAdCn8MEdLZLbN6717LdTdzhOmwaEc%2FCW2pqYIQQfpf2Zf%2BAyknSL%2FtyLFeO1AwXxqzRx2jqM%2FGVDf8ZSEtWYKqfQYfNo%2FdWxc%3D"}]}, cf-cache-status: DYNAMIC, content-type: application/json, cross-origin-opener-policy: same-origin, server: cloudflare, alt-svc: h3=":443"; ma=86400, nel: {"report_to":"cf-nel","success_fraction":0.0,"max_age":604800}, cf-ray: 968a275bda3987ac-IAD, x-frame-options: DENY, x-content-type-options: nosniff, allow: GET, HEAD, OPTIONS}
flutter: 📦 Response Body: {"location":{"count":0,"recent":[]},"artist":{"count":0,"recent":[]},"genre":{"count":0,"recent":[]},"social":{"count":1,"recent":[{"id":1,"name":"First React","xp_reward":25,"tier":"T0","completed_at":"2025-08-01T23:48:39.183728+00:00"}]},"total_completed":1,"total_xp":25}
flutter: ⏰ Timestamp: 2025-08-01T21:30:32.526598
flutter: ✅ Request successful
flutter: ========================
flutter:
flutter: ✅ Loaded completed summary: 1 total completed, 25 total XP
flutter: ✅ Loaded completion stats from optimized endpoint
flutter: ❌ Error loading completion stats: A GamificationProvider was used after being disposed.
flutter: Once you have called dispose() on a GamificationProvider, it can no longer be used.
flutter: 📊 Parallel processing: 1 pins in 24ms
flutter: 📊 Average processing time: 25ms per batch
flutter: 📊 Total pins processed: 9
flutter: 📍 🎯 Adding NEW individual pin feature in cluster view: 11
flutter: 📍 Found 27 pins immediately from cache
flutter: 📊 Parallel processing: 1 pins in 5ms
flutter: 📊 Average processing time: 21ms per batch
flutter: 📊 Total pins processed: 10
flutter: 📍 🔧 Added 5 new features to cluster-pins-source memory: 0 → 5
flutter: 📍 🎯 Incremental cluster layer update: 5 new features
flutter: 📍 Layer state changed: LayerState.transitioning → LayerState.showingIndividual
flutter: 📍 State updated: LayerState.transitioning → LayerState.showingIndividual
flutter: 📍 ✅ State transition successful: LayerState.showingIndividual
flutter: 📍 ✅ Cluster layer updated with 5 total features (5 new)
flutter: 📍 🎯 Skipping cluster symbol update - no cluster changes detected
flutter: 📍 ✅ Updated cluster layer with 5 new pins (total pins: 5, clusters: 0)
flutter: 📍 ❌ Error during layer visibility switch: PlatformException(layerNotFound, Layer not found, Layer with id individual-pins-layer not found., null)
flutter: 📍 🔍 Validating current state: LayerState.showingIndividual
flutter: 📍 ✅ State validation passed
flutter: 🎨 ✨ Creating progressive pin animations for 5 pins
flutter:
flutter: 📥 === API RESPONSE ===
flutter: 🎯 GET https://api.bopmaps.com/api/gamification/user-achievements/completed_by_category/?category=social
flutter: 📊 Status Code: 200
flutter: 📋 Response Headers: {connection: keep-alive, transfer-encoding: chunked, date: Sat, 02 Aug 2025 02:30:32 GMT, content-encoding: gzip, vary: Accept, origin, Cookie, strict-transport-security: max-age=********; includeSubDomains; preload, referrer-policy: same-origin, report-to: {"group":"cf-nel","max_age":604800,"endpoints":[{"url":"https://a.nel.cloudflare.com/report/v4?s=voI3gvIos%2BX6kU1JQ33A9j5XBCF7DErvxn2wY2J3XZn9Q7eu22pqFm3lsXQ5iPK36SeMin56gh3KSVRLQl2KR%2Fs4QEMHMClGjdVz4r2u1Q%3D%3D"}]}, cf-cache-status: DYNAMIC, content-type: application/json, cross-origin-opener-policy: same-origin, server: cloudflare, alt-svc: h3=":443"; ma=86400, nel: {"report_to":"cf-nel","success_fraction":0.0,"max_age":604800}, cf-ray: 968a275bff1407f0-IAD, x-frame-options: DENY, x-content-type-options: nosniff, allow: GET, HEAD, OPTIONS}
flutter: 📦 Response Body: [{"id":1,"name":"First React","description":"Give 1 upvote","xp_reward":25,"tier":"T0","icon_name":null,"completed_at":"2025-08-01T23:48:39.183728+00:00","category":"social"}]
flutter: ⏰ Timestamp: 2025-08-01T21:30:32.557117
flutter: ✅ Request successful
flutter: ========================
flutter:
flutter: ✅ Loaded 21 achievements for category social using optimized endpoints
flutter: 🎨 ✨ Progressive entrance animation for pin 31 at (LatLng(36.146712676388645, -86.80796914009892))
flutter: 🎨 🌟 Creating progressive glow for pin 31
flutter: 🎨 ✨ Pin 31 loaded with gentle animation
flutter: 📍 Cleared layer-based approach
flutter:
flutter: 📊 Achievements Summary:
flutter: - Total: 74
flutter: - Completed: 7
flutter: - In Progress: 67
flutter: ✅ Loaded 21 achievements for category social
flutter:
flutter: 📥 === API RESPONSE ===
flutter: 🎯 GET https://api.bopmaps.com/api/gamification/user-achievements/
flutter: 📊 Status Code: 200
flutter: 📋 Response Headers: {connection: keep-alive, transfer-encoding: chunked, date: Sat, 02 Aug 2025 02:30:32 GMT, content-encoding: gzip, vary: Accept, origin, Cookie, strict-transport-security: max-age=********; includeSubDomains; preload, referrer-policy: same-origin, report-to: {"group":"cf-nel","max_age":604800,"endpoints":[{"url":"https://a.nel.cloudflare.com/report/v4?s=sr4pq2%2B9KLf4R0J4Gt7Xp0K5shWoKmgCYM4Gw6MTLwia%2F0ZBHi1IjGrFdukClqFPLDYzxAECyGigvMVvnNhqbdgXOuAB7N8z6FUVYdlJsQ%3D%3D"}]}, cf-cache-status: DYNAMIC, content-type: application/json, cross-origin-opener-policy: same-origin, server: cloudflare, alt-svc: h3=":443"; ma=86400, nel: {"report_to":"cf-nel","success_fraction":0.0,"max_age":604800}, cf-ray: 968a275bfd28e619-IAD, x-frame-options: DENY, x-content-type-options: nosniff, allow: GET, HEAD, OPTIONS}
flutter: 📦 Response Body: {"count":21,"next":"https://api.bopmaps.com/api/gamification/user-achievements/?page=2","previous":null,"results":[{"id":1,"achievement":{"id":52,"name":"First React","description":"Give 1 upvote","icon":null,"icon_name":null,"criteria":{"reactions_given":1},"type":"social","challenge_id":"first_react","tier":"T0","xp_reward":25,"is_secret":false,"created_at":"2025-08-01T23:23:10.703144Z","background_color":"#FFFFFF","primary_color":"#000000","category":"Social Challenge","reward_skin":null,"icon_data":null,"progress":{"current_count":0,"progress_percentage":0.0},"completed_at":"2025-08-01T23:48:39.183728Z"},"progress":{"max_streak":1,"current_streak":1,"daily_reactions":{"2025-08-01":1},"reactions_given":1,"last_reaction_date":"2025-08-01"},"completed_at":"2025-08-01T23:48:39.183728Z","progress_percentage":0,"last_updated":"2025-08-01T23:48:39.183846Z"},{"id":2,"achievement":{"id":53,"name":"Noticed","description":"Get 1 upvote on one of your pins","icon":null,"icon_name":null,"criteria":{"reactions_received":1},"type":"social","challenge_id":"first_like_received","tier":"T0","xp_reward":25,"is_secret":false,"created_at":"2025-08-01T23:23:10.708293Z","background_color":"#FFFFFF","primary_color":"#000000","category":"Social Challenge","reward_skin":null,"icon_data":null,"progress":{"current_count":0,"progress_percentage":0.0},"completed_at":null},"progress":{"max_streak":1,"current_streak":1,"daily_reactions":{"2025-08-01":1},"reactions_given":1,"last_reaction_date":"2025-08-01"},"completed_at":null,"progress_percentage":0,"last_updated":"2025-08-01T23:48:39.214670Z"},{"id":3,"achievement":{"id":54,"name":"Reaction Rookie","description":"Give 25 upvotes","icon":null,"icon_name":null,"criteria":{"reactions_given":25},"type":"social","challenge_id":"react_25_given","tier":"T1","xp_reward":100,"is_secret":false,"created_at":"2025-08-01T23:23:10.713660Z","background_color":"#FFFFFF","primary_color":"#000000","category":"Social Challenge","reward_skin":null,"icon_data":null,"progress":{"current_count":0,"progress_percentage":0.0},"completed_at":null},"progress":{"max_streak":1,"current_streak":1,"daily_reactions":{"2025-08-01":1},"reactions_given":1,"last_reaction_date":"2025-08-01"},"completed_at":null,"progress_percentage":0,"last_updated":"2025-08-01T23:48:39.246887Z"},{"id":4,"achievement":{"id":55,"name":"Local Buzz","description":"Get 10 upvotes from 5 unique users","icon":null,"icon_name":null,"criteria":{"unique_reactors":5,"reactions_received":10},"type":"social","challenge_id":"local_buzz","tier":"T1","xp_reward":100,"is_secret":false,"created_at":"2025-08-01T23:23:10.719343Z","background_color":"#FFFFFF","primary_color":"#000000","category":"Social Challenge","reward_skin":null,"icon_data":null,"progress":{"current_count":0,"progress_percentage":0.0},"completed_at":null},"progress":{"max_streak":1,"current_streak":1,"daily_reactions":{"2025-08-01":1},"reactions_given":1,"last_reaction_date":"2025-08-01"},"completed_at":null,"progress_percentage":0,"last_updated":"2025-08-01T23:48:39.279343Z"},{"id":5,"achievement":{"id":56,"name":"Week-Long Hype","description":"Give ≥ 1 upvote 7 days in a row","icon":null,"icon_name":null,"criteria":{"daily_reactions":1,"consecutive_days":7},"type":"social","challenge_id":"react_streak_7","tier":"T2","xp_reward":150,"is_secret":false,"created_at":"2025-08-01T23:23:10.724580Z","background_color":"#FFFFFF","primary_color":"#000000","category":"Social Challenge","reward_skin":null,"icon_data":null,"progress":{"current_count":0,"progress_percentage":0.0},"completed_at":null},"progress":{"max_streak":1,"current_streak":1,"daily_reactions":{"2025-08-01":1},"reactions_given":1,"last_reaction_date":"2025-08-01"},"completed_at":null,"progress_percentage":0,"last_updated":"2025-08-01T23:48:39.311084Z"},{"id":6,"achievement":{"id":57,"name":"Conversation Starter","description":"Comment on 10 different pins","icon":null,"icon_name":null,"criteria":{"comments_given":10},"type":"social","challenge_id":"comments_10","tier":"T2","xp_reward":150,"is_secret":false,"created_at":"2025-08-01T23:23:10.729842Z","background_color":"#FFFFFF","primary_color":"#000000","category":"Social Challenge","reward_skin":null,"icon_data":null,"progress":{"current_count":0,"progress_percentage":0.0},"completed_at":null},"progress":{"max_streak":1,"current_streak":1,"daily_reactions":{"2025-08-01":1},"reactions_given":1,"last_reaction_date":"2025-08-01"},"completed_at":null,"progress_percentage":0,"last_updated":"2025-08-01T23:48:39.341782Z"},{"id":7,"achievement":{"id":58,"name":"Popular Pin","description":"Get 15 upvotes on a single pin","icon":null,"icon_name":null,"criteria":{"single_pin_reactions":15},"type":"social","challenge_id":"popular_pin","tier":"T2","xp_reward":150,"is_secret":false,"created_at":"2025-08-01T23:23:10.735027Z","background_color":"#FFFFFF","primary_color":"#000000","category":"Social Challenge","reward_skin":null,"icon_data":null,"progress":{"current_count":0,"progress_percentage":0.0},"completed_at":null},"progress":{"max_streak":1,"current_streak":1,"daily_reactions":{"2025-08-01":1},"reactions_given":1,"last_reaction_date":"2025-08-01"},"completed_at":null,"progress_percentage":0,"last_updated":"2025-08-01T23:48:39.373099Z"},{"id":8,"achievement":{"id":59,"name":"Hype Machine","description":"Give 150 upvotes (lifetime)","icon":null,"icon_name":null,"criteria":{"reactions_given":150},"type":"social","challenge_id":"react_100_given","tier":"T3","xp_reward":200,"is_secret":false,"created_at":"2025-08-01T23:23:10.740306Z","background_color":"#FFFFFF","primary_color":"#000000","category":"Social Challenge","reward_skin":null,"icon_data":null,"progress":{"current_count":0,"progress_percentage":0.0},"completed_at":null},"progress":{"max_streak":1,"current_streak":1,"daily_reactions":{"2025-08-01":1},"reactions_given":1,"last_reaction_date":"2025-08-01"},"completed_at":null,"progress_percentage":0,"last_updated":"2025-08-01T23:48:39.405679Z"},{"id":9,"achievement":{"id":60,"name":"Community Voice","description":"Write 25 comments","icon":null,"icon_name":null,"criteria":{"comments_given":25},"type":"social","challenge_id":"comments_25","tier":"T3","xp_reward":225,"is_secret":false,"created_at":"2025-08-01T23:23:10.745726Z","background_color":"#FFFFFF","primary_color":"#000000","category":"Social Challenge","reward_skin":null,"icon_data":null,"progress":{"current_count":0,"progress_percentage":0.0},"completed_at":null},"progress":{"max_streak":1,"current_streak":1,"daily_reactions":{"2025-08-01":1},"reactions_given":1,"last_reaction_date":"2025-08-01"},"completed_at":null,"progress_percentage":0,"last_updated":"2025-08-01T23:48:39.436361Z"},{"id":10,"achievement":{"id":61,"name":"Rising Star","description":"20 unique users upvoted your pins","icon":null,"icon_name":null,"criteria":{"unique_reactors":20},"type":"social","challenge_id":"unique_supporters_15","tier":"T3","xp_reward":225,"is_secret":false,"created_at":"2025-08-01T23:23:10.751139Z","background_color":"#FFFFFF","primary_color":"#000000","category":"Social Challenge","reward_skin":null,"icon_data":null,"progress":{"current_count":0,"progress_percentage":0.0},"completed_at":null},"progress":{"max_streak":1,"current_streak":1,"daily_reactions":{"2025-08-01":1},"reactions_given":1,"last_reaction_date":"2025-08-01"},"completed_at":null,"progress_percentage":0,"last_updated":"2025-08-01T23:48:39.466740Z"},{"id":11,"achievement":{"id":62,"name":"Crowd Favorite","description":"Collect 200 upvotes (lifetime)","icon":null,"icon_name":null,"criteria":{"reactions_received":200},"type":"social","challenge_id":"react_100_received","tier":"T4","xp_reward":300,"is_secret":false,"created_at":"2025-08-01T23:23:10.756950Z","background_color":"#FFFFFF","primary_color":"#000000","category":"Social Challenge","reward_skin":null,"icon_data":null,"progress":{"current_count":0,"progress_percentage":0.0},"completed_at":null},"progress":{"max_streak":1,"current_streak":1,"daily_reactions":{"2025-08-01":1},"reactions_given":1,"last_reaction_date":"2025-08-01"},"completed_at":null,"progress_percentage":0,"last_updated":"2025-08-01T23:48:39.497832Z"},{"id":12,"achievement":{"id":63,"name":"Consistency King","description":"Give ≥ 2 upvotes 15 consecutive days","icon":null,"icon_name":null,"criteria":{"daily_reactions":2,"consecutive_days":15},"type":"social","challenge_id":"react_streak_15","tier":"T4","xp_reward":300,"is_secret":false,"created_at":"2025-08-01T23:23:10.762337Z","background_color":"#FFFFFF","primary_color":"#000000","category":"Social Challenge","reward_skin":null,"icon_data":null,"progress":{"current_count":0,"progress_percentage":0.0},"completed_at":null},"progress":{"max_streak":1,"current_streak":1,"daily_reactions":{"2025-08-01":1},"reactions_given":1,"last_reaction_date":"2025-08-01"},"completed_at":null,"progress_percentage":0,"last_updated":"2025-08-01T23:48:39.530577Z"},{"id":13,"achievement":{"id":64,"name":"Weekly Warrior","description":"Complete 3 different weekly challenges","icon":null,"icon_name":null,"criteria":{"weekly_challenges_completed":3},"type":"social","challenge_id":"weekly_challenges_3","tier":"T4","xp_reward":300,"is_secret":false,"created_at":"2025-08-01T23:23:10.767707Z","background_color":"#FFFFFF","primary_color":"#000000","category":"Social Challenge","reward_skin":null,"icon_data":null,"progress":{"current_count":0,"progress_percentage":0.0},"completed_at":null},"progress":{"max_streak":1,"current_streak":1,"daily_reactions":{"2025-08-01":1},"reactions_given":1,"last_reaction_date":"2025-08-01"},"completed_at":null,"progress_percentage":0,"last_updated":"2025-08-01T23:48:39.562259Z"},{"id":14,"achievement":{"id":65,"name":"Hot Pin","description":"Get 40 upvotes on a single pin","icon":null,"icon_name":null,"criteria":{"single_pin_reactions":40},"type":"social","challenge_id":"hot_pin","tier":"T5","xp_reward":400,"is_secret":false,"created_at":"2025-08-01T23:23:10.772979Z","background_color":"#FFFFFF","primary_color":"#000000","category":"Social Challenge","reward_skin":null,"icon_data":null,"progress":{"current_count":0,"progress_percentage":0.0},"completed_at":null},"progress":{"max_streak":1,"current_streak":1,"daily_reactions":{"2025-08-01":1},"reactions_given":1,"last_reaction_date":"2025-08-01"},"completed_at":null,"progress_percentage":0,"last_updated":"2025-08-01T23:48:39.593792Z"},{"id":15,"achievement":{"id":66,"name":"Ripple Effect","description":"50 unique users upvoted your pins","icon":null,"icon_name":null,"criteria":{"unique_reactors":50},"type":"social","challenge_id":"unique_supporters_40","tier":"T5","xp_reward":450,"is_secret":false,"created_at":"2025-08-01T23:23:10.778031Z","background_color":"#FFFFFF","primary_color":"#000000","category":"Social Challenge","reward_skin":null,"icon_data":null,"progress":{"current_count":0,"progress_percentage":0.0},"completed_at":null},"progress":{"max_streak":1,"current_streak":1,"daily_reactions":{"2025-08-01":1},"reactions_given":1,"last_reaction_date":"2025-08-01"},"completed_at":null,"progress_percentage":0,"last_updated":"2025-08-01T23:48:39.624506Z"},{"id":16,"achievement":{"id":67,"name":"Super Hype","description":"Give 750 upvotes total","icon":null,"icon_name":null,"criteria":{"reactions_given":750},"type":"social","challenge_id":"react_500_given","tier":"T6","xp_reward":600,"is_secret":false,"created_at":"2025-08-01T23:23:10.785693Z","background_color":"#FFFFFF","primary_color":"#000000","category":"Social Challenge","reward_skin":null,"icon_data":null,"progress":{"current_count":0,"progress_percentage":0.0},"completed_at":null},"progress":{"max_streak":1,"current_streak":1,"daily_reactions":{"2025-08-01":1},"reactions_given":1,"last_reaction_date":"2025-08-01"},"completed_at":null,"progress_percentage":0,"last_updated":"2025-08-01T23:48:39.657774Z"},{"id":17,"achievement":{"id":68,"name":"Multi-Hit Maker","description":"3 pins with ≥ 30 upvotes each","icon":null,"icon_name":null,"criteria":{"reactions_per_pin":30,"pins_with_reactions":3},"type":"social","challenge_id":"multi_hit","tier":"T6","xp_reward":650,"is_secret":false,"created_at":"2025-08-01T23:23:10.790908Z","background_color":"#FFFFFF","primary_color":"#000000","category":"Social Challenge","reward_skin":null,"icon_data":null,"progress":{"current_count":0,"progress_percentage":0.0},"completed_at":null},"progress":{"max_streak":1,"current_streak":1,"daily_reactions":{"2025-08-01":1},"reactions_given":1,"last_reaction_date":"2025-08-01"},"completed_at":null,"progress_percentage":0,"last_updated":"2025-08-01T23:48:39.688730Z"},{"id":18,"achievement":{"id":69,"name":"Comment Legend","description":"Write 100 comments","icon":null,"icon_name":null,"criteria":{"comments_given":100},"type":"social","challenge_id":"comments_100","tier":"T6","xp_reward":650,"is_secret":false,"created_at":"2025-08-01T23:23:10.795900Z","background_color":"#FFFFFF","primary_color":"#000000","category":"Social Challenge","reward_skin":null,"icon_data":null,"progress":{"current_count":0,"progress_percentage":0.0},"completed_at":null},"progress":{"max_streak":1,"current_streak":1,"daily_reactions":{"2025-08-01":1},"reactions_given":1,"last_reaction_date":"2025-08-01"},"completed_at":null,"progress_percentage":0,"last_updated":"2025-08-01T23:48:39.720252Z"},{"id":19,"achievement":{"id":70,"name":"Social Icon","description":"500 upvotes received + 100 unique supporters","icon":null,"icon_name":null,"criteria":{"unique_reactors":100,"reactions_received":500},"type":"social","challenge_id":"react_350_received","tier":"T7","xp_reward":800,"is_secret":false,"created_at":"2025-08-01T23:23:10.800918Z","background_color":"#FFFFFF","primary_color":"#000000","category":"Social Challenge","reward_skin":null,"icon_data":null,"progress":{"current_count":0,"progress_percentage":0.0},"completed_at":null},"progress":{"max_streak":1,"current_streak":1,"daily_reactions":{"2025-08-01":1},"reactions_given":1,"last_reaction_date":"2025-08-01"},"completed_at":null,"progress_percentage":0,"last_updated":"2025-08-01T23:48:39.750770Z"},{"id":20,"achievement":{"id":71,"name":"Viral Pin","description":"One pin hits 75 upvotes","icon":null,"icon_name":null,"criteria":{"single_pin_reactions":75},"type":"social","challenge_id":"viral_pin","tier":"T7","xp_reward":1000,"is_secret":false,"created_at":"2025-08-01T23:23:10.806090Z","background_color":"#FFFFFF","primary_color":"#000000","category":"Social Challenge","reward_skin":null,"icon_data":null,"progress":{"current_count":0,"progress_percentage":0.0},"completed_at":null},"progress":{"max_streak":1,"current_streak":1,"daily_reactions":{"2025-08-01":1},"reactions_given":1,"last_reaction_date":"2025-08-01"},"completed_at":null,"progress_percentage":0,"last_updated":"2025-08-01T23:48:39.781955Z"}]}
flutter: ⏰ Timestamp: 2025-08-01T21:30:32.577154
flutter: ✅ Request successful
flutter: ========================
flutter:
flutter:
flutter: 📥 === API RESPONSE ===
flutter: 🎯 GET https://api.bopmaps.com/api/gamification/user-achievements/completed_by_category/?category=artist
flutter: 📊 Status Code: 200
flutter: 📋 Response Headers: {connection: keep-alive, transfer-encoding: chunked, date: Sat, 02 Aug 2025 02:30:32 GMT, content-encoding: gzip, vary: Accept, origin, Cookie, strict-transport-security: max-age=********; includeSubDomains; preload, referrer-policy: same-origin, report-to: {"group":"cf-nel","max_age":604800,"endpoints":[{"url":"https://a.nel.cloudflare.com/report/v4?s=wtWO7ld76TgyLG2t15Paa8GbJub3nWAOjSD%2BaRdQT2xr5yoITJLkQmhCzmyGS%2FQJTOHo%2F%2F0WeRTvwkyTHoeNxPHRQ6cRLGxn8S5j6hfX8Q%3D%3D"}]}, cf-cache-status: DYNAMIC, content-type: application/json, cross-origin-opener-policy: same-origin, server: cloudflare, alt-svc: h3=":443"; ma=86400, nel: {"report_to":"cf-nel","success_fraction":0.0,"max_age":604800}, cf-ray: 968a275bccc3081a-IAD, x-frame-options: DENY, x-content-type-options: nosniff, allow: GET, HEAD, OPTIONS}
flutter: 📦 Response Body: []
flutter: ⏰ Timestamp: 2025-08-01T21:30:32.580106
flutter: ✅ Request successful
flutter: ========================
flutter:
flutter: ✅ Loaded 0 achievements for category artist using optimized endpoints
flutter: ⚠️ No achievements found for category artist
flutter: 📚 Finished loading all category achievements
flutter: 📊 Total achievements loaded: 74
flutter:
flutter: 📥 === API RESPONSE ===
flutter: 🎯 GET https://api.bopmaps.com/api/gamification/user-achievements/in_progress_by_category/?category=location
flutter: 📊 Status Code: 200
flutter: 📋 Response Headers: {connection: keep-alive, transfer-encoding: chunked, date: Sat, 02 Aug 2025 02:30:32 GMT, content-encoding: gzip, vary: Accept, origin, Cookie, strict-transport-security: max-age=********; includeSubDomains; preload, referrer-policy: same-origin, report-to: {"group":"cf-nel","max_age":604800,"endpoints":[{"url":"https://a.nel.cloudflare.com/report/v4?s=uVSQX47QG3I%2FovuwgSxzzNjuGUVp01m6wbwbX2mmBFQqG7yNS9M48cprUj4PWq6eeko6tt1rOi50G3qIY4hahu6NEgLdEPWFRmADcBIbxw%3D%3D"}]}, cf-cache-status: DYNAMIC, content-type: application/json, cross-origin-opener-policy: same-origin, server: cloudflare, alt-svc: h3=":443"; ma=86400, nel: {"report_to":"cf-nel","success_fraction":0.0,"max_age":604800}, cf-ray: 968a275c3926e2ca-IAD, x-frame-options: DENY, x-content-type-options: nosniff, allow: GET, HEAD, OPTIONS}
flutter: 📦 Response Body: []
flutter: ⏰ Timestamp: 2025-08-01T21:30:32.600893
flutter: ✅ Request successful
flutter: ========================
flutter:
flutter: ✅ Loaded 0 achievements for category location using optimized endpoints
flutter: ⚠️ No achievements found for category location
flutter:
flutter: 📥 === API RESPONSE ===
flutter: 🎯 GET https://api.bopmaps.com/api/gamification/achievements/rank_badge_data/
flutter: 📊 Status Code: 200
flutter: 📋 Response Headers: {connection: keep-alive, transfer-encoding: chunked, date: Sat, 02 Aug 2025 02:30:32 GMT, content-encoding: gzip, vary: Accept, origin, Cookie, strict-transport-security: max-age=********; includeSubDomains; preload, referrer-policy: same-origin, report-to: {"group":"cf-nel","max_age":604800,"endpoints":[{"url":"https://a.nel.cloudflare.com/report/v4?s=tnt1if%2BcLyLzOaqlbFfSjOVHq3PH7cTZkCzr74mCLsBRSMEFLeBpCvajOdXq4uEi4xkVW9DMOIWjN0GwT%2FSNDKmxbXHvVV8MBUtzB%2F%2FUGA%3D%3D"}]}, cf-cache-status: DYNAMIC, content-type: application/json, cross-origin-opener-policy: same-origin, server: cloudflare, alt-svc: h3=":443"; ma=86400, nel: {"report_to":"cf-nel","success_fraction":0.0,"max_age":604800}, cf-ray: 968a275bd9a98220-IAD, x-frame-options: DENY, x-content-type-options: nosniff, allow: GET, HEAD, OPTIONS}
flutter: 📦 Response Body: {"current_rank":{"id":"level_1","name":"Basement Bopper","level":1,"emoji":"🎧","primary_color":"#1A1A1A","background_color":"#2D2D2D"},"next_rank":{"id":"level_2","name":"Selector","level":2,"emoji":"🎵","primary_color":"#4A90E2","background_color":"#2171C7"},"total_xp":25,"current_xp":25,"next_level_xp":500,"xp_progress":5.0,"user_level":1,"all_ranks":[{"id":"level_1","name":"Basement Bopper","level":1,"required_xp":0,"emoji":"🎧","primary_color":"#1A1A1A","background_color":"#2D2D2D","unlocked":true},{"id":"level_2","name":"Selector","level":2,"required_xp":500,"emoji":"🎵","primary_color":"#4A90E2","background_color":"#2171C7","unlocked":false},{"id":"level_3","name":"Tastemaker","level":3,"required_xp":1500,"emoji":"🎶","primary_color":"#9B51E0","background_color":"#6B2E9E","unlocked":false},{"id":"level_4","name":"Trendsetter","level":4,"required_xp":3500,"emoji":"🎤","primary_color":"#F2994A","background_color":"#D97B29","unlocked":false},{"id":"level_5","name":"Icon","level":5,"required_xp":7000,"emoji":"⭐","primary_color":"#EB5757","background_color":"#C62828","unlocked":false},{"id":"level_6","name":"Architect","level":6,"required_xp":12000,"emoji":"🏗️","primary_color":"#27AE60","background_color":"#1E8449","unlocked":false},{"id":"level_7","name":"Legend","level":7,"required_xp":20000,"emoji":"👑","primary_color":"#F2C94C","background_color":"#DBA520","unlocked":false}],"recent_achievements":[{"name":"First React","xp_earned":25,"completed_at":"2025-08-01T23:48:39.183728Z","icon":{"name":"emoji_events","color":"#000000"}}],"close_achievements":[],"stats":{"total_achievements":1,"total_possible":74,"completion_rate":1.4}}
flutter: ⏰ Timestamp: 2025-08-01T21:30:32.617710
flutter: ✅ Request successful
flutter: ========================
flutter:
flutter:
flutter: 🎯 Parsing rank data from API:
flutter: - Rank ID: level_1
flutter: - Level: 1
flutter: 🎯 Getting rank for level 1
flutter: 📊 Selected rank: Basement Bopper (required level: 1)
flutter: - Selected rank by level: Basement Bopper
flutter:
flutter: 🎯 Parsing rank data from API:
flutter: - Rank ID: level_2
flutter: - Level: 2
flutter: 🎯 Getting rank for level 2
flutter: 📊 Selected rank: Selector (required level: 2)
flutter: - Selected rank by level: Selector
flutter:
flutter: 📥 === API RESPONSE ===
flutter: 🎯 GET https://api.bopmaps.com/api/gamification/user-achievements/completed_by_category/?category=genre
flutter: 📊 Status Code: 200
flutter: 📋 Response Headers: {connection: keep-alive, transfer-encoding: chunked, date: Sat, 02 Aug 2025 02:30:32 GMT, content-encoding: gzip, vary: Accept, origin, Cookie, strict-transport-security: max-age=********; includeSubDomains; preload, referrer-policy: same-origin, report-to: {"group":"cf-nel","max_age":604800,"endpoints":[{"url":"https://a.nel.cloudflare.com/report/v4?s=RkQsAgWW6g2GmLbHWxCcccLOCgkyyGWJenSYRPqiS1boyxrxwmyvuptOiZL30ZWC0WpeAMWrAPCmwugUkHR49CI3CqmL7BdcQSbP9Mc%3D"}]}, cf-cache-status: DYNAMIC, content-type: application/json, cross-origin-opener-policy: same-origin, server: cloudflare, alt-svc: h3=":443"; ma=86400, nel: {"report_to":"cf-nel","success_fraction":0.0,"max_age":604800}, cf-ray: 968a275c595f576d-IAD, x-frame-options: DENY, x-content-type-options: nosniff, allow: GET, HEAD, OPTIONS}
flutter: 📦 Response Body: []
flutter: ⏰ Timestamp: 2025-08-01T21:30:32.619736
flutter: ✅ Request successful
flutter: ========================
flutter:
flutter: ✅ Loaded 0 achievements for category genre using optimized endpoints
flutter: ⚠️ No achievements found for category genre
flutter: 🎨 ✨ Progressive entrance animation for pin 22 at (LatLng(36.144896578642566, -86.81081346522859))
flutter: 🎨 🌟 Creating progressive glow for pin 22
flutter: 🎨 ✨ Pin 22 loaded with gentle animation
flutter:
flutter: 📥 === API RESPONSE ===
flutter: 🎯 GET https://api.bopmaps.com/api/gamification/user-achievements/completed_by_category/?category=artist
flutter: 📊 Status Code: 200
flutter: 📋 Response Headers: {connection: keep-alive, transfer-encoding: chunked, date: Sat, 02 Aug 2025 02:30:32 GMT, content-encoding: gzip, vary: Accept, origin, Cookie, strict-transport-security: max-age=********; includeSubDomains; preload, referrer-policy: same-origin, report-to: {"group":"cf-nel","max_age":604800,"endpoints":[{"url":"https://a.nel.cloudflare.com/report/v4?s=WSqTuDrYNp7mdLiEfr9FtZehw4hhXtbzGYeiKIMyqt%2FAqOSc6zkSgL42ag%2FXbj3%2BB5VUBRwdfXouaJsBQWsaYvPLxm0V1VS0u%2F7UZOLQ1g%3D%3D"}]}, cf-cache-status: DYNAMIC, content-type: application/json, cross-origin-opener-policy: same-origin, server: cloudflare, alt-svc: h3=":443"; ma=86400, nel: {"report_to":"cf-nel","success_fraction":0.0,"max_age":604800}, cf-ray: 968a275c5e09390a-IAD, x-frame-options: DENY, x-content-type-options: nosniff, allow: GET, HEAD, OPTIONS}
flutter: 📦 Response Body: []
flutter: ⏰ Timestamp: 2025-08-01T21:30:32.624404
flutter: ✅ Request successful
flutter: ========================
flutter:
flutter: ✅ Loaded 0 achievements for category artist using optimized endpoints
flutter: ⚠️ No achievements found for category artist
flutter: ❌ Error initializing provider: A GamificationProvider was used after being disposed.
flutter: Once you have called dispose() on a GamificationProvider, it can no longer be used.
flutter: 🎮 Gamification initialization failed: A GamificationProvider was used after being disposed.
flutter: Once you have called dispose() on a GamificationProvider, it can no longer be used.
flutter: ✅ Header data fetched successfully
flutter: 👤 Username: mothusom68
flutter: 🖼️ Profile pic: https://lh3.googleusercontent.com/a/ACg8ocJsin8X8WYA9poJ18OudABwwD87LbSbv_PMMcgNsAeRa50L9SFD=s96-c
flutter: 📍 ⚠️ Skipping layer features clear - progressive rendering is active
flutter: 📍 ⚠️ isPinFetchInProgress: true, timer active: true
flutter: 📍 Cleared 5 pins and 0 clusters
flutter:
flutter: 🚀 === API REQUEST ===
flutter: 📤 GET https://api.bopmaps.com/api/gamification/skins/?page=1
flutter: 📋 Headers: {Content-Type: application/json, Accept: application/json, Authorization: Bearer [HIDDEN]}
flutter: ⏰ Timestamp: 2025-08-01T21:30:32.628013
flutter:
flutter:
flutter: 🚀 === API REQUEST ===
flutter: 📤 GET https://api.bopmaps.com/api/gamification/skins/unlocked/
flutter: 📋 Headers: {Content-Type: application/json, Accept: application/json, Authorization: Bearer [HIDDEN]}
flutter: ⏰ Timestamp: 2025-08-01T21:30:32.628579
flutter:
flutter: 💾 Header data cached successfully
flutter: ✅ MapScreen: User profile initialized successfully
flutter: 📍 ❌ Error initializing empty glow sources: PlatformException(sourceNotFound, Source not found, Source with id glow-polygons-source not found., null)
flutter: 🔄 ===== SYNCING FILTERED PINS TO CACHE =====
flutter: 🔄 Syncing 27 filtered pins to cache
flutter: 🔄 Pin 31 already has coordinates: lat=36.146712676388645, lng=-86.80796914009892
flutter: 🔄 ✅ Cached filtered pin: 31 (lat=36.146712676388645, lng=-86.80796914009892)
flutter: 🔄 Pin 22 already has coordinates: lat=36.144896578642566, lng=-86.81081346522859
flutter: 🔄 ✅ Cached filtered pin: 22 (lat=36.144896578642566, lng=-86.81081346522859)
flutter: 🔄 Pin 5 already has coordinates: lat=36.149796004212334, lng=-86.80621776257429
flutter: 🔄 ✅ Cached filtered pin: 5 (lat=36.149796004212334, lng=-86.80621776257429)
flutter: 🔄 Pin 3 already has coordinates: lat=36.14365940935097, lng=-86.81369061241375
flutter: 🔄 ✅ Cached filtered pin: 3 (lat=36.14365940935097, lng=-86.81369061241375)
flutter: 🔄 Pin 11 already has coordinates: lat=36.143057499372496, lng=-86.80258594530756
flutter: 🔄 ✅ Cached filtered pin: 11 (lat=36.143057499372496, lng=-86.80258594530756)
flutter: 🔄 Pin 10 already has coordinates: lat=36.145673506425894, lng=-86.80033276754217
flutter: 🔄 ✅ Cached filtered pin: 10 (lat=36.145673506425894, lng=-86.80033276754217)
flutter: 🔄 Pin 23 already has coordinates: lat=36.15069491041301, lng=-86.80191912912967
flutter: 🔄 ✅ Cached filtered pin: 23 (lat=36.15069491041301, lng=-86.80191912912967)
flutter: 🔄 Pin 8 already has coordinates: lat=36.13845874790999, lng=-86.8059766933522
flutter: 🔄 ✅ Cached filtered pin: 8 (lat=36.13845874790999, lng=-86.8059766933522)
flutter: 🔄 Pin 1 already has coordinates: lat=36.1565883201972, lng=-86.80760297431151
flutter: 🔄 ✅ Cached filtered pin: 1 (lat=36.1565883201972, lng=-86.80760297431151)
flutter: 🔄 Pin 25 already has coordinates: lat=36.150990589009595, lng=-86.79576053262336
flutter: 🔄 ✅ Cached filtered pin: 25 (lat=36.150990589009595, lng=-86.79576053262336)
flutter: 🔄 Pin 2 already has coordinates: lat=36.15138693243462, lng=-86.81999936998884
flutter: 🔄 ✅ Cached filtered pin: 2 (lat=36.15138693243462, lng=-86.81999936998884)
flutter: 🔄 Pin 30 already has coordinates: lat=36.153155828993334, lng=-86.79709849047417
flutter: 🔄 ✅ Cached filtered pin: 30 (lat=36.153155828993334, lng=-86.79709849047417)
flutter: 🔄 Pin 21 already has coordinates: lat=36.15214147216192, lng=-86.7950660887525
flutter: 🔄 ✅ Cached filtered pin: 21 (lat=36.15214147216192, lng=-86.7950660887525)
flutter: 🔄 Pin 29 already has coordinates: lat=36.151280700715205, lng=-86.78902917555408
flutter: 🔄 ✅ Cached filtered pin: 29 (lat=36.151280700715205, lng=-86.78902917555408)
flutter: 🔄 Pin 4 already has coordinates: lat=36.132206082905114, lng=-86.79589157170174
flutter: 🔄 ✅ Cached filtered pin: 4 (lat=36.132206082905114, lng=-86.79589157170174)
flutter: 🔄 Pin 20 already has coordinates: lat=36.157377739288954, lng=-86.78485620297654
flutter: 🔄 ✅ Cached filtered pin: 20 (lat=36.157377739288954, lng=-86.78485620297654)
flutter: 🔄 Pin 27 already has coordinates: lat=36.15824941618403, lng=-86.78419131319633
flutter: 🔄 ✅ Cached filtered pin: 27 (lat=36.15824941618403, lng=-86.78419131319633)
flutter: 🔄 Pin 6 already has coordinates: lat=36.16676832004515, lng=-86.82091270659058
flutter: 🔄 ✅ Cached filtered pin: 6 (lat=36.16676832004515, lng=-86.82091270659058)
flutter: 🔄 Pin 9 already has coordinates: lat=36.157696860958595, lng=-86.7825454756899
flutter: 🔄 ✅ Cached filtered pin: 9 (lat=36.157696860958595, lng=-86.7825454756899)
flutter: 🔄 Pin 28 already has coordinates: lat=36.16312913973052, lng=-86.78293852121507
flutter: 🔄 ✅ Cached filtered pin: 28 (lat=36.16312913973052, lng=-86.78293852121507)
flutter: 🔄 Pin 18 already has coordinates: lat=36.146650260814354, lng=-86.77561358315819
flutter: 🔄 ✅ Cached filtered pin: 18 (lat=36.146650260814354, lng=-86.77561358315819)
flutter: 🔄 Pin 15 already has coordinates: lat=36.15833189653237, lng=-86.77609753214917
flutter: 🔄 ✅ Cached filtered pin: 15 (lat=36.15833189653237, lng=-86.77609753214917)
flutter: 🔄 Pin 26 already has coordinates: lat=36.157524736529766, lng=-86.77469089028642
flutter: 🔄 ✅ Cached filtered pin: 26 (lat=36.157524736529766, lng=-86.77469089028642)
flutter: 🔄 Pin 24 already has coordinates: lat=36.1654684702875, lng=-86.78038389283871
flutter: 🔄 ✅ Cached filtered pin: 24 (lat=36.1654684702875, lng=-86.78038389283871)
flutter: 🔄 Pin 19 already has coordinates: lat=36.17083129630792, lng=-86.78767330739612
flutter: 🔄 ✅ Cached filtered pin: 19 (lat=36.17083129630792, lng=-86.78767330739612)
flutter: 🔄 Pin 13 already has coordinates: lat=36.10994853444598, lng=-86.80902689147204
flutter: 🔄 ✅ Cached filtered pin: 13 (lat=36.10994853444598, lng=-86.80902689147204)
flutter: 🔄 Pin 14 already has coordinates: lat=36.10493724268142, lng=-86.80044659691973
flutter: 🔄 ✅ Cached filtered pin: 14 (lat=36.10493724268142, lng=-86.80044659691973)
flutter: 🔄 Final cache size: 27
flutter: 🔄 ===== FILTER SYNC COMPLETE =====
flutter: 📍 Using progressive rendering for filter with 27 pins
flutter: 📍 🏗️ Initializing empty layers...
flutter: 📍 ⚠️ Skipping layer features clear - progressive rendering is active
flutter: 📍 ⚠️ isPinFetchInProgress: true, timer active: true
flutter: 📍 Cleared 0 pins and 0 clusters
flutter: 📍 🏗️ Setting initial layer visibility: currentZoom=16.0, shouldCluster=false, threshold=14.0
flutter: 📍 ✅ Empty layers initialized, ready for progressive updates (showing individual pins)
flutter: 📍 🔄 Set rendering context to filterChange for progressive rendering
flutter: 🔍 Starting scanning animation
flutter: 📍 🔄 Forcing layers visible after successful filter change with 27 pins
flutter: 📍 ⏱️ Processing pin batch (27 pending, 0 rendered)
flutter: 📍 ⚡ Processing batch of 5 NEW pins (0 already rendered, 22 remaining)
flutter: 📍 🔄 Building batch of 5 pins to BOTH layers in parallel
flutter: 📍 ⚡ Batch processed - updating layer visibility: shouldCluster=false, current zoom=16.0 (5 pins added, 5 total)
flutter: 📍 Enhanced layer switching: showClusters=false
flutter: 📍 Current zoom: 16.0, clustering threshold: 14.0
flutter: 📍 Current layer state: LayerState.showingIndividual
flutter: 📍 Progressive rendering check: inProgress=false, enabled=true, pending=22
flutter: 📍 State transition requested: LayerState.showingIndividual → LayerState.showingIndividual
[ERROR:flutter/runtime/dart_vm_initializer.cc(40)] Unhandled Exception: PlatformException(layerNotFound, Layer not found, Layer with id individual-pins-layer not found., null)
#0      StandardMethodCodec.decodeEnvelope (package:flutter/src/services/message_codecs.dart:652:7)
#1      MethodChannel._invokeMethod (package:flutter/src/services/platform_channel.dart:370:18)
<asynchronous suspension>
#2      MapLibreMethodChannel.setLayerVisibility (package:maplibre_gl_platform_interface/src/method_channel_maplibre_gl.dart:801:5)
<asynchronous suspension>
[ERROR:flutter/runtime/dart_vm_initializer.cc(40)] Unhandled Exception: PlatformException(layerNotFound, Layer not found, Layer with id cluster-pins-layer not found., null)
#0      StandardMethodCodec.decodeEnvelope (package:flutter/src/services/message_codecs.dart:652:7)
#1      MethodChannel._invokeMethod (package:flutter/src/services/platform_channel.dart:370:18)
<asynchronous suspension>
#2      MapLibreMethodChannel.setLayerVisibility (package:maplibre_gl_platform_interface/src/method_channel_maplibre_gl.dart:801:5)
<asynchronous suspension>
flutter: 📊 Parallel processing: 5 pins in 16ms
flutter: 📊 Average processing time: 20ms per batch
flutter: 📊 Total pins processed: 15
flutter:
flutter: 📥 === API RESPONSE ===
flutter: 🎯 GET https://api.bopmaps.com/api/gamification/skins/unlocked/
flutter: 📊 Status Code: 404
flutter: 📋 Response Headers: {connection: keep-alive, transfer-encoding: chunked, date: Sat, 02 Aug 2025 02:30:32 GMT, content-encoding: gzip, vary: origin, Cookie, strict-transport-security: max-age=********; includeSubDomains; preload, referrer-policy: same-origin, report-to: {"group":"cf-nel","max_age":604800,"endpoints":[{"url":"https://a.nel.cloudflare.com/report/v4?s=m3Qpw7mC7jvEfROT2bjHgUn9IjmP7T101c4AtTi18FYlmAGrL8BQWJGRHMG4%2BTFrbTe2jZbOIlX5bURiRdGe43yIBBPrvzGBYp3xhs0QAw%3D%3D"}]}, cf-cache-status: DYNAMIC, content-type: text/html; charset=utf-8, cross-origin-opener-policy: same-origin, server: cloudflare, alt-svc: h3=":443"; ma=86400, nel: {"report_to":"cf-nel","success_fraction":0.0,"max_age":604800}, cf-ray: 968a27660cc5e2ca-IAD, x-frame-options: DENY, x-content-type-options: nosniff}
flutter: 📦 Response Body: 
<!doctype html>
<html lang="en">
<head>
  <title>Not Found</title>
</head>
<body>
  <h1>Not Found</h1><p>The requested resource was not found on this server.</p>
</body>
</html>
flutter: ⏰ Timestamp: 2025-08-01T21:30:32.698925
flutter: ⚠️ Client error - check request
flutter: ========================
flutter:
flutter:
flutter: 📥 === API RESPONSE ===
flutter: 🎯 GET https://api.bopmaps.com/api/gamification/skins/?page=1
flutter: 📊 Status Code: 404
flutter: 📋 Response Headers: {connection: keep-alive, transfer-encoding: chunked, date: Sat, 02 Aug 2025 02:30:32 GMT, content-encoding: gzip, vary: origin, Cookie, strict-transport-security: max-age=********; includeSubDomains; preload, referrer-policy: same-origin, report-to: {"group":"cf-nel","max_age":604800,"endpoints":[{"url":"https://a.nel.cloudflare.com/report/v4?s=%2FnTgzlG3SbcF2MButsB%2B0IM3riLd3xA9aSOSIou7Nu099QEWtrdocLo%2FZsamBZ%2BFUS04LQgiDriWHAv277Jm06fWZSKWhMaylwrBO0ddaA%3D%3D"}]}, cf-cache-status: DYNAMIC, content-type: text/html; charset=utf-8, cross-origin-opener-policy: same-origin, server: cloudflare, alt-svc: h3=":443"; ma=86400, nel: {"report_to":"cf-nel","success_fraction":0.0,"max_age":604800}, cf-ray: 968a27660fba390a-IAD, x-frame-options: DENY, x-content-type-options: nosniff}
flutter: 📦 Response Body: 
<!doctype html>
<html lang="en">
<head>
  <title>Not Found</title>
</head>
<body>
  <h1>Not Found</h1><p>The requested resource was not found on this server.</p>
</body>
</html>
flutter: ⏰ Timestamp: 2025-08-01T21:30:32.701296
flutter: ⚠️ Client error - check request
flutter: ========================
flutter:
flutter: 🔐 Auth state: token=true, user=true, isAuthenticated=true
flutter: 👤 Current user: mothusom68 (<EMAIL>)
flutter: 🎯 Display name computed: mothusom68
flutter: 🔑 Token starts with: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************.ppRGTJE5co8Kk65n-k-URqeeAyeuTW36IPXdbC3cuyU
flutter: 📊 Parallel processing: 5 pins in 13ms
flutter: 📊 Average processing time: 19ms per batch
flutter: 📊 Total pins processed: 20
flutter: 📊 Parallel processing: 1 pins in 10ms
flutter: 📊 Average processing time: 18ms per batch
flutter: 📊 Total pins processed: 21
flutter: 📊 Parallel processing: 1 pins in 1ms
flutter: 📊 Average processing time: 17ms per batch
flutter: 📊 Total pins processed: 22
flutter: 📊 Parallel processing: 1 pins in 0ms
flutter: 📊 Average processing time: 15ms per batch
flutter: 📊 Total pins processed: 23
flutter: 📊 Parallel processing: 1 pins in 0ms
flutter: 📊 Average processing time: 14ms per batch
flutter: 📊 Total pins processed: 24
flutter: 📊 Parallel processing: 1 pins in 0ms
flutter: 📊 Average processing time: 13ms per batch
flutter: 📊 Total pins processed: 25
flutter: 📊 Parallel processing: 5 pins in 1ms
flutter: 📊 Average processing time: 12ms per batch
flutter: 📊 Total pins processed: 30
flutter: 📊 Parallel processing: 1 pins in 5ms
flutter: 📊 Average processing time: 11ms per batch
flutter: 📊 Total pins processed: 31
flutter: 📊 Parallel processing: 1 pins in 1ms
flutter: 📊 Average processing time: 11ms per batch
flutter: 📊 Total pins processed: 32
flutter: 📊 Parallel processing: 1 pins in 0ms
flutter: 📊 Average processing time: 10ms per batch
flutter: 📊 Total pins processed: 33
flutter: 📊 Parallel processing: 1 pins in 23ms
flutter: 📊 Average processing time: 11ms per batch
flutter: 📊 Total pins processed: 34
flutter: 📊 Parallel processing: 1 pins in 11ms
flutter: 📊 Average processing time: 11ms per batch
flutter: 📊 Total pins processed: 35
[ERROR:flutter/runtime/dart_vm_initializer.cc(40)] Unhandled Exception: WebSocketChannelException: WebSocketException: Connection to 'https://api.bopmaps.com:0/ws/achievements/?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************.ppRGTJE5co8Kk65n-k-URqeeAyeuTW36IPXdbC3cuyU#' was not upgraded to websocket, HTTP status code: 403
flutter: 📊 Parallel processing: 5 pins in 13ms
flutter: 📊 Average processing time: 11ms per batch
flutter: 📊 Total pins processed: 40
flutter: 📊 Parallel processing: 1 pins in 6ms
flutter: 📊 Average processing time: 10ms per batch
flutter: 📊 Total pins processed: 41
flutter: 📊 Parallel processing: 1 pins in 13ms
flutter: 📊 Average processing time: 11ms per batch
flutter: 📊 Total pins processed: 42
flutter: 📊 Parallel processing: 5 pins in 11ms
flutter: 📊 Average processing time: 11ms per batch
flutter: 📊 Total pins processed: 47
flutter: 📊 Parallel processing: 1 pins in 21ms
flutter: 📊 Average processing time: 11ms per batch
flutter: 📊 Total pins processed: 48
flutter: 📊 Parallel processing: 1 pins in 13ms
flutter: 📊 Average processing time: 11ms per batch
flutter: 📊 Total pins processed: 49
flutter: ✓ [SpotifyService] Got 80 playlist tracks (offset: 0, total available: 227)
flutter: ✅ Added 20 tracks from "Mood Booster 2025 🌞 Positive Vibes, Happy Music"
flutter: 🎵 [SpotifyService] Getting playlist tracks (playlist: 5Q6REFoDkWgKcuj3h0A97l, limit: 80, offset: 0)
flutter: 🌍 [SpotifyService] Adding market parameter for direct Spotify API
flutter: 📊 Parallel processing: 1 pins in 24ms
flutter: 📊 Average processing time: 12ms per batch
flutter: 📊 Total pins processed: 50
flutter: 📊 Parallel processing: 1 pins in 3ms
flutter: 📊 Average processing time: 11ms per batch
flutter: 📊 Total pins processed: 51
flutter: 📊 Parallel processing: 1 pins in 8ms
flutter: 📊 Average processing time: 11ms per batch
flutter: 📊 Total pins processed: 52
flutter: ❌ [SpotifyService] No access token found in either storage location
VERBOSE: OSOperationRepo flushDeltaQueue in background: false with queue: [<OSDelta OS_UPDATE_PROPERTIES_DELTA with property: tags value: ["email": "<EMAIL>", "is_verified": "false", "username": "mothusom68", "has_apple_music": "false", "has_spotify": "false", "user_id": "79", "has_soundcloud": "false"]>]
VERBOSE: OSPropertyOperationExecutor enqueue delta <OSDelta OS_UPDATE_PROPERTIES_DELTA with property: tags value: ["email": "<EMAIL>", "is_verified": "false", "username": "mothusom68", "has_apple_music": "false", "has_spotify": "false", "user_id": "79", "has_soundcloud": "false"]>
VERBOSE: OSPropertyOperationExecutor processDeltaQueue with queue: [<OSDelta OS_UPDATE_PROPERTIES_DELTA with property: tags value: ["email": "<EMAIL>", "is_verified": "false", "username": "mothusom68", "has_apple_music": "false", "has_spotify": "false", "user_id": "79", "has_soundcloud": "false"]>]
VERBOSE: HTTP Request (OneSignalUser.OSRequestUpdateProperties) with URL: https://api.onesignal.com/apps/************************************/users/by/onesignal_id/************************************, with parameters: {
  "refresh_device_metadata" : false,
  "properties" : {
    "tags" : {
      "has_soundcloud" : "false",
      "has_spotify" : "false",
      "is_verified" : "false",
      "username" : "mothusom68",
      "email" : "<EMAIL>",
      "has_apple_music" : "false",
      "user_id" : "79"
    }
  }
} and headers: {
    "OneSignal-Subscription-Id" = "************************************";
}
flutter: 📊 Parallel processing: 2 pins in 2ms
flutter: 📊 Average processing time: 11ms per batch
flutter: 📊 Total pins processed: 54
flutter: 📊 Parallel processing: 1 pins in 11ms
flutter: 📊 Average processing time: 11ms per batch
flutter: 📊 Total pins processed: 55
flutter: 📊 Parallel processing: 1 pins in 2ms
flutter: 📊 Average processing time: 10ms per batch
flutter: 📊 Total pins processed: 56
flutter: ✓ [SpotifyService] Got 19 playlist tracks (offset: 0, total available: 19)
flutter: ✅ Added 19 tracks from "songs to put you in a good mood"
flutter: 🎵 [SpotifyService] Getting playlist tracks (playlist: 4HdRhStvDVyrPiJt4l6FZr, limit: 80, offset: 20)
flutter: 🌍 [SpotifyService] Adding market parameter for direct Spotify API
VERBOSE: network request (OneSignalUser.OSRequestUpdateProperties) with URL https://api.onesignal.com/apps/************************************/users/by/onesignal_id/************************************ and headers: {
    "OneSignal-Subscription-Id" = "************************************";
}
VERBOSE: network response (OneSignalUser.OSRequestUpdateProperties) with URL https://api.onesignal.com/apps/************************************/users/by/onesignal_id/************************************: {
    errors =     (
                {
            code = "entitlements-tag-limit";
            title = "The tags for this user exceed the limit for this organization's plan. Please remove existing tags or upgrade your plan to add more tags";
        }
    );
    headers =     {
        "Access-Control-Allow-Origin" = "*";
        "Alt-Svc" = "h3=\":443\"; ma=86400";
        "Content-Length" = 192;
        "Content-Type" = "application/json; charset=utf-8";
        Date = "Sat, 02 Aug 2025 02:30:33 GMT";
        Priority = "u=3,i=?0";
        Server = cloudflare;
        "Strict-Transport-Security" = "max-age=15552000; includeSubDomains";
        Via = "1.1 google";
        "access-control-allow-headers" = "SDK-Version,Content-Type,Origin,Authorization,OneSignal-Subscription-Id";
        "cf-cache-status" = DYNAMIC;
        "cf-ray" = "968a276b784f56c8-IAD";
        "server-timing" = cfExtPri;
        traceparent = "00-82313a57d864e87db2284e4429dbe9be-7cc4254cb0143bff-00";
    };
    httpStatusCode = 409;
}
ERROR: OSPropertyOperationExecutor update properties request failed with error: <OneSignalClientError: 0x302b96cd0>
flutter: 📍 🚦 No-op transition: already in LayerState.showingIndividual
flutter: 📍 🔧 Computing pin features for 5 pins in parallel isolate
flutter: 📍 ⚠️ Error forcing layer visibility: PlatformException(layerNotFound, Layer not found, Layer with id individual-pins-layer not found., null)
flutter: 🔍 Applied all filter: 27 pins found
flutter: 📍 🔄 Reset rendering context to incrementalUpdate after filter change
flutter: 📍 ❌ Error during layer visibility switch: PlatformException(layerNotFound, Layer not found, Layer with id individual-pins-layer not found., null)
flutter: 📍 🔍 Validating current state: LayerState.showingIndividual
flutter: 📍 ✅ State validation passed
flutter: 🎉 Skipping new pin animations during initial load
flutter: 📍 🔍 About to update cluster layer with 5 items
flutter: 📍 🔍 - Clusters: 0
flutter: 📍 🔍 - Individual pins: 5
flutter: 📍 🎯 Updating cluster layer: 5 items, 5 existing pins, 0 existing clusters
flutter: 📍 ✅ No cluster layer updates needed - all features already rendered
flutter: 📍 🎯 Skipping cluster symbol update - no cluster changes detected
flutter: 📍 ✅ Updated cluster layer with 5 new pins (total pins: 5, clusters: 0)
flutter: 📍 🔧 Parallel isolate returned 5 features
flutter: 📍 🔧 Processing feature: 31
flutter: 📍 ✅ Created empty individual pins layer
flutter: 📍 ✅ Created empty cluster layer
flutter: 🎨 ✨ Progressive entrance animation for pin 5 at (LatLng(36.149796004212334, -86.80621776257429))
flutter: 🎨 🌟 Creating progressive glow for pin 5
flutter: 🎨 ✨ Pin 5 loaded with gentle animation
flutter: 🏆 User rank data loaded successfully
flutter:
flutter: 💾 Saved user progress to cache:
flutter: - Level: 1
flutter: - XP: 25
flutter: - Progress: 5.0
flutter:
flutter: 📊 Achievements Summary:
flutter: - Total: 74
flutter: - Completed: 7
flutter: - In Progress: 67
flutter: 🔌 WebSocket: Disconnected
flutter: 🔌 WebSocket: Original API URL: https://api.bopmaps.com/api
flutter: 🔌 WebSocket: Converted base URL: wss://api.bopmaps.com
flutter: 🔌 WebSocket: Final WebSocket URL: wss://api.bopmaps.com/ws/achievements/?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************.ppRGTJE5co8Kk65n-k-URqeeAyeuTW36IPXdbC3cuyU
flutter: 🔌 WebSocket: Token length: 229
flutter: 🔌 WebSocket: Connected successfully
flutter: 🔌 WebSocket connected for real-time achievements
flutter: 🎮 Gamification system initialized successfully
flutter: 📍 ⚠️ Progressive rendering active - checking if immediate cluster switch is possible
flutter: 📍 ⚠️ Current state: shouldCluster=false, currentlyShowingClusters=false
flutter: 📍 ⚠️ Rendered pins: 5, Pending pins: 22
flutter: 📍 ⚠️ Deferring layer switch until progressive rendering completes
flutter: 📍 ⚠️ Deferred layer switch: will switch to individual pins after progressive rendering
flutter: 📍 🔄 Starting progressive rendering timer with 22 pending pins...
flutter: 📍 🔄 Current state: _isPinFetchInProgress=false
flutter: 📍 🔍 Filtered to 22 unique unrendered pins
flutter: 📍 ⚡ Processing first batch immediately...
flutter: 📍 ⚡ Processing batch of 5 NEW pins (0 already rendered, 17 remaining)
flutter: 📍 🔄 Building batch of 5 pins to BOTH layers in parallel
flutter: 📍 ⚡ Batch processed - updating layer visibility: shouldCluster=false, current zoom=16.0 (5 pins added, 10 total)
flutter: 📍 Layer switch cooldown active, skipping
flutter: 📍 🔧 Computing pin features for 5 pins in parallel isolate
flutter: 🎨 ✨ Creating progressive pin animations for 5 pins
flutter: 📍 🔍 About to update cluster layer with 10 items
flutter: 📍 🔍 - Clusters: 0
flutter: 📍 🔍 - Individual pins: 10
flutter: 📍 🎯 Updating cluster layer: 10 items, 5 existing pins, 0 existing clusters
flutter: 📍 🎯 Adding NEW individual pin feature in cluster view: 10
flutter: 🎨 ✨ Progressive entrance animation for pin 10 at (LatLng(36.145673506425894, -86.80033276754217))
flutter: 🎨 🌟 Creating progressive glow for pin 10
flutter: 🎨 ✨ Pin 10 loaded with gentle animation
flutter: 📍 🔧 Parallel isolate returned 5 features
flutter: 📍 🔧 Processing feature: 10
flutter: 📍 🎯 Adding NEW individual pin feature in cluster view: 23
flutter: 📍 🎯 Adding NEW individual pin feature in cluster view: 8
flutter: 📍 🎯 Adding NEW individual pin feature in cluster view: 1
flutter: 📍 🎯 Adding NEW individual pin feature in cluster view: 25
flutter: 📍 🔧 Added 5 new features to cluster-pins-source memory: 5 → 10
flutter: 📍 🎯 Incremental cluster layer update: 5 new features
flutter: 📍 ✅ Cluster layer updated with 10 total features (5 new)
flutter: 📍 🎯 Skipping cluster symbol update - no cluster changes detected
flutter: 📍 ✅ Updated cluster layer with 5 new pins (total pins: 10, clusters: 0)
flutter: 📍 ✅ Initialized empty glow sources
flutter: 🎨 ✨ Progressive entrance animation for pin 3 at (LatLng(36.14365940935097, -86.81369061241375))
flutter: 🎨 🌟 Creating progressive glow for pin 3
flutter: 🎨 ✨ Pin 3 loaded with gentle animation
flutter: 🎨 ✨ Progressive entrance animation for pin 23 at (LatLng(36.15069491041301, -86.80191912912967))
flutter: 🎨 🌟 Creating progressive glow for pin 23
flutter: 🎨 ✨ Pin 23 loaded with gentle animation
flutter: 🎨 ✨ Progressive entrance animation for pin 11 at (LatLng(36.143057499372496, -86.80258594530756))
flutter: 🎨 🌟 Creating progressive glow for pin 11
flutter: 🎨 ✨ Pin 11 loaded with gentle animation
flutter: 🎨 ✨ Progressive entrance animation for pin 8 at (LatLng(36.13845874790999, -86.8059766933522))
flutter: 🎨 🌟 Creating progressive glow for pin 8
flutter: 🎨 ✨ Pin 8 loaded with gentle animation
flutter: 🎯 Touch points changed: 0 → 1
flutter: 📍 ⏱️ Processing pin batch (17 pending, 10 rendered)
flutter: 📍 ⚡ Processing batch of 5 NEW pins (0 already rendered, 12 remaining)
flutter: 📍 🔄 Building batch of 5 pins to BOTH layers in parallel
flutter: 📍 ⚡ Batch processed - updating layer visibility: shouldCluster=false, current zoom=16.0 (5 pins added, 15 total)
flutter: 📍 Enhanced layer switching: showClusters=false
flutter: 📍 Current zoom: 16.0, clustering threshold: 14.0
flutter: 📍 Current layer state: LayerState.showingIndividual
flutter: 📍 Progressive rendering check: inProgress=true, enabled=true, pending=12
flutter: 📍 🚀 Individual layer requested during progressive rendering - pins already being rendered progressively
flutter: 📍 State transition requested: LayerState.showingIndividual → LayerState.showingIndividual
flutter: 📍 🚦 No-op transition: already in LayerState.showingIndividual
flutter: 📍 🔧 Computing pin features for 5 pins in parallel isolate
flutter: 📍 🔧 Parallel isolate returned 5 features
flutter: 📍 🔧 Processing feature: 2
flutter: 📍 ✅ Showing individual layers, hiding cluster layers
flutter: 📍 ⚡ Layer switch completed in 3ms (parallelized)
flutter: 📍 🔍 Validating layer visibility: individual=true, cluster=false
flutter: 🎯 Touch points changed: 1 → 2
flutter: 🎯 Starting gesture: GestureType.pinch (was: GestureState.none)
flutter: 🎯 Gesture state changed: GestureState.pinching
flutter: 📍 🔍 About to update cluster layer with 15 items
flutter: 📍 🔍 - Clusters: 0
flutter: 📍 🔍 - Individual pins: 15
flutter: 📍 🎯 Updating cluster layer: 15 items, 10 existing pins, 0 existing clusters
flutter: 📍 🎯 Adding NEW individual pin feature in cluster view: 21
flutter: 📍 🎯 Adding NEW individual pin feature in cluster view: 2
flutter: 📍 ✅ Layer visibility validated and corrected if needed
flutter: 🎨 ✨ Creating progressive pin animations for 5 pins
flutter: 📍 🎯 Adding NEW individual pin feature in cluster view: 30
flutter: 🎨 ✨ Progressive entrance animation for pin 2 at (LatLng(36.15138693243462, -86.81999936998884))
flutter: 🎨 🌟 Creating progressive glow for pin 2
flutter: 🎨 ✨ Pin 2 loaded with gentle animation
flutter: 📍 🎯 Adding NEW individual pin feature in cluster view: 29
flutter: 📍 🎯 Adding NEW individual pin feature in cluster view: 4
flutter: 🎨 ✨ Progressive entrance animation for pin 1 at (LatLng(36.1565883201972, -86.80760297431151))
flutter: 🎨 🌟 Creating progressive glow for pin 1
flutter: 🎨 ✨ Pin 1 loaded with gentle animation
flutter: 📍 🔧 Added 5 new features to cluster-pins-source memory: 10 → 15
flutter: 📍 🎯 Incremental cluster layer update: 5 new features
flutter: 📍 ✅ Cluster layer updated with 15 total features (5 new)
flutter: 📍 🎯 Skipping cluster symbol update - no cluster changes detected
flutter: 📍 ✅ Updated cluster layer with 5 new pins (total pins: 15, clusters: 0)
flutter: 🔌 WebSocket: Error occurred: WebSocketChannelException: WebSocketException: Connection to 'https://api.bopmaps.com:0/ws/achievements/?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************.ppRGTJE5co8Kk65n-k-URqeeAyeuTW36IPXdbC3cuyU#' was not upgraded to websocket, HTTP status code: 403
flutter: 🔌 WebSocket: Connection closed
flutter: 🎨 ✨ Progressive entrance animation for pin 30 at (LatLng(36.153155828993334, -86.79709849047417))
flutter: 🎨 🌟 Creating progressive glow for pin 30
flutter: 🎨 ✨ Pin 30 loaded with gentle animation
flutter: 📍 🔍 Validating layer visibility: individual=true, cluster=false
flutter: 🎨 ✨ Progressive entrance animation for pin 25 at (LatLng(36.150990589009595, -86.79576053262336))
flutter: 🎨 🌟 Creating progressive glow for pin 25
flutter: 🎨 ✨ Pin 25 loaded with gentle animation
flutter: 📍 ✅ Layer visibility validated and corrected if needed
flutter: 🎨 ✨ Progressive entrance animation for pin 21 at (LatLng(36.15214147216192, -86.7950660887525))
flutter: 🎨 🌟 Creating progressive glow for pin 21
flutter: 🎨 ✨ Pin 21 loaded with gentle animation
flutter: 📍 ⏱️ Processing pin batch (12 pending, 15 rendered)
flutter: 📍 ⚡ Processing batch of 5 NEW pins (0 already rendered, 7 remaining)
flutter: 📍 🔄 Building batch of 5 pins to BOTH layers in parallel
flutter: 📍 ⚡ Batch processed - updating layer visibility: shouldCluster=false, current zoom=16.105536935309516 (5 pins added, 20 total)
flutter: 📍 Enhanced layer switching: showClusters=false
flutter: 📍 Current zoom: 16.105536935309516, clustering threshold: 14.0
flutter: 📍 Current layer state: LayerState.showingIndividual
flutter: 📍 Progressive rendering check: inProgress=true, enabled=true, pending=7
flutter: 📍 🚀 Individual layer requested during progressive rendering - pins already being rendered progressively
flutter: 📍 State transition requested: LayerState.showingIndividual → LayerState.showingIndividual
flutter: 📍 🚦 No-op transition: already in LayerState.showingIndividual
flutter: 📍 🔍 Validating layer visibility: individual=true, cluster=false
flutter: 📍 🔧 Computing pin features for 5 pins in parallel isolate
flutter: 📍 🔍 About to update cluster layer with 18 items
flutter: 📍 🔍 - Clusters: 1
flutter: 📍 🔍 - Individual pins: 17
flutter: 📍 🔧 Parallel isolate returned 5 features
flutter: 📍 🔧 Processing feature: 20
flutter: 📍 ✅ Showing individual layers, hiding cluster layers
flutter: 📍 ⚡ Layer switch completed in 15ms (parallelized)
flutter: 📍 🔍 Validating layer visibility: individual=true, cluster=false
flutter: 📍 ✅ Layer visibility validated and corrected if needed
flutter: 🎨 ✨ Progressive entrance animation for pin 29 at (LatLng(36.151280700715205, -86.78902917555408))
flutter: 🎨 🌟 Creating progressive glow for pin 29
flutter: 🎨 ✨ Pin 29 loaded with gentle animation
flutter: 📍 ✅ Layer visibility validated and corrected if needed
flutter: 🎨 ✨ Creating progressive pin animations for 5 pins
flutter: 🎯 Added 1 new cluster icons: {}
flutter: 📍 🎯 Updating cluster layer: 18 items, 15 existing pins, 0 existing clusters
flutter: 📍 🎯 Adding/updating cluster feature: cluster_36.15777467214386_-86.78386433062093 with 3 pins
flutter: 📍 🎯 Adding NEW individual pin feature in cluster view: 6
flutter: 🎨 ✨ Progressive entrance animation for pin 20 at (LatLng(36.157377739288954, -86.78485620297654))
flutter: 🎨 🌟 Creating progressive glow for pin 20
flutter: 🎨 ✨ Pin 20 loaded with gentle animation
flutter: 📍 🎯 Adding NEW individual pin feature in cluster view: 28
flutter: 📍 🔧 Added 3 new features to cluster-pins-source memory: 15 → 18
flutter: 📍 🎯 Incremental cluster layer update: 3 new features
flutter: 📍 ✅ Cluster layer updated with 18 total features (3 new)
flutter: 📍 🎯 Updating cluster symbols: 0 existing → 1 new
flutter: 📍 🎯 Updated cluster symbols: 1 symbols for tap handling
flutter: 📍 ✅ Updated cluster layer with 5 new pins (total pins: 20, clusters: 1)
flutter: 🎨 ✨ Progressive entrance animation for pin 4 at (LatLng(36.132206082905114, -86.79589157170174))
flutter: 🎨 🌟 Creating progressive glow for pin 4
flutter: 🎨 ✨ Pin 4 loaded with gentle animation
flutter: 🎨 ✨ Progressive entrance animation for pin 27 at (LatLng(36.15824941618403, -86.78419131319633))
flutter: 🎨 🌟 Creating progressive glow for pin 27
flutter: 🎨 ✨ Pin 27 loaded with gentle animation
flutter: 🎯 Touch points changed: 2 → 1
flutter: 🎯 Ending gesture: GestureType.pinch
flutter: 🎯 Gesture state changed: GestureState.none
flutter: 📍 ⏱️ Processing pin batch (7 pending, 20 rendered)
flutter: 📍 ⚡ Processing batch of 5 NEW pins (0 already rendered, 2 remaining)
flutter: 📍 🔄 Building batch of 5 pins to BOTH layers in parallel
flutter: 📍 ⚡ Batch processed - updating layer visibility: shouldCluster=false, current zoom=14.355972735216021 (5 pins added, 25 total)
flutter: 📍 Enhanced layer switching: showClusters=false
flutter: 📍 Current zoom: 14.355972735216021, clustering threshold: 14.0
flutter: 📍 Current layer state: LayerState.showingIndividual
flutter: 📍 Progressive rendering check: inProgress=true, enabled=true, pending=2
flutter: 📍 🚀 Individual layer requested during progressive rendering - pins already being rendered progressively
flutter: 📍 State transition requested: LayerState.showingIndividual → LayerState.showingIndividual
flutter: 📍 🚦 No-op transition: already in LayerState.showingIndividual
flutter: 🎨 ✨ Progressive entrance animation for pin 6 at (LatLng(36.16676832004515, -86.82091270659058))
flutter: 🎨 🌟 Creating progressive glow for pin 6
flutter: 🎨 ✨ Pin 6 loaded with gentle animation
flutter: ❌ [SpotifyService] No access token found in either storage location
NSPredicate: Use of 'mgl_does:have:' as an NSExpression function is forbidden.
NSPredicate: Use of 'mgl_does:have:' as an NSExpression function is forbidden.
NSPredicate: Use of 'mgl_does:have:' as an NSExpression function is forbidden.
NSPredicate: Use of 'mgl_does:have:' as an NSExpression function is forbidden.
flutter: ✓ [SpotifyService] Got 50 playlist tracks (offset: 20, total available: 70)
flutter: ✅ Added 20 tracks from "good mood bangersss🤍"
flutter: 🎵 [SpotifyService] Getting playlist tracks (playlist: 1cdB6GTLSSzsRswrkUhtw2, limit: 80, offset: 20)
flutter: 🌍 [SpotifyService] Adding market parameter for direct Spotify API
flutter: ❌ [SpotifyService] No access token found in either storage location
flutter: ✓ [SpotifyService] Got 11 playlist tracks (offset: 20, total available: 31)
flutter: ✅ Added 11 tracks from "HAPPY VIBES ONLY😜"
flutter: 🎵 Searching for mood playlists: "feel good vibes"
flutter: 🔍 [SpotifyService] Searching for playlists: "feel good vibes" (limit: 25, offset: 20)
flutter: ❌ [SpotifyService] No access token found in either storage location
flutter: ❌ [SpotifyService] No access token found in either storage location
SentryFlutterReplayScreenshotProvider received null result. Cannot capture a replay screenshot.
flutter: 🔍 [SpotifyService] Playlist search response keys: [playlists]
flutter: ✓ [SpotifyService] Got 24 playlist results for "feel good vibes"
flutter: 📚 Found 24 playlists for mood "feel good vibes"
flutter: 🎵 [SpotifyService] Getting playlist tracks (playlist: 7A83HzQeTFV5i69ZABmsqK, limit: 80, offset: 80)
flutter: 🌍 [SpotifyService] Adding market parameter for direct Spotify API
flutter: ❌ [SpotifyService] No access token found in either storage location
flutter: ✓ [SpotifyService] Got 62 playlist tracks (offset: 80, total available: 142)
flutter: ✅ Added 20 tracks from "Best feel good songs ever"
flutter: 🔧 [Search Context] Category: artistBased, Genre: null, Context: AI_ARTIST_BASED
flutter: 🔍 [SPOTIFY AI SEARCH] [2025-08-01T21:30:34.247117] QUERY: "good mood music" | LIMIT: 10 | OFFSET: 0 [CONTEXT: AI_ARTIST_BASED]
flutter: ❌ [SpotifyService] No access token found in either storage location
flutter: ❌ [SpotifyService] No access token found in either storage location
flutter: ✅ [SPOTIFY AI SEARCH] SUCCESS: 10 tracks returned for "good mood music" [CONTEXT: AI_ARTIST_BASED]
flutter: ✅ [AI SEARCH LOG] 21:30:34 | "good mood music" [AI_ARTIST_BASED] -> 10 results
flutter: 🔧 [Search Context] Category: artistBased, Genre: null, Context: AI_ARTIST_BASED
flutter: 🔍 [SPOTIFY AI SEARCH] [2025-08-01T21:30:34.492786] QUERY: "upbeat cheerful" | LIMIT: 10 | OFFSET: 0 [CONTEXT: AI_ARTIST_BASED]
flutter: ❌ [SpotifyService] No access token found in either storage location
flutter: ❌ [SpotifyService] No access token found in either storage location
flutter: 📍 🔧 Computing pin features for 5 pins in parallel isolate
flutter: 🎯 Touch points changed: 1 → 0
flutter: 📍 🔍 About to update cluster layer with 23 items
flutter: 📍 🔍 - Clusters: 1
flutter: 📍 🔍 - Individual pins: 22
flutter: 📍 🎯 Updating cluster layer: 23 items, 17 existing pins, 1 existing clusters
flutter: 📍 🎯 Adding/updating cluster feature: cluster_36.15777467214386_-86.78386433062093 with 3 pins
flutter: 📍 🎯 Adding NEW individual pin feature in cluster view: 18
flutter: 📍 🔧 Parallel isolate returned 5 features
flutter: 📍 🔧 Processing feature: 18
flutter: 📍 ✅ Showing individual layers, hiding cluster layers
flutter: 📍 ⚡ Layer switch completed in 14ms (parallelized)
flutter: 📍 🔍 Validating layer visibility: individual=true, cluster=false
flutter: 📍 🎯 Adding NEW individual pin feature in cluster view: 15
flutter: 📍 🎯 Adding NEW individual pin feature in cluster view: 26
flutter: 📍 ✅ Layer visibility validated and corrected if needed
flutter: 🎨 ✨ Creating progressive pin animations for 5 pins
flutter: 🎨 ✨ Progressive entrance animation for pin 18 at (LatLng(36.146650260814354, -86.77561358315819))
flutter: 🎨 🌟 Creating progressive glow for pin 18
flutter: 🎨 ✨ Pin 18 loaded with gentle animation
flutter: 📍 🎯 Adding NEW individual pin feature in cluster view: 24
flutter: 🎨 ✨ Progressive entrance animation for pin 9 at (LatLng(36.157696860958595, -86.7825454756899))
flutter: 🎨 🌟 Creating progressive glow for pin 9
flutter: 🎨 ✨ Pin 9 loaded with gentle animation
flutter: 📍 🎯 Adding NEW individual pin feature in cluster view: 19
flutter: 📍 ⚠️ Progressive rendering active - checking if immediate cluster switch is possible
flutter: 📍 ⚠️ Current state: shouldCluster=false, currentlyShowingClusters=false
flutter: 📍 ⚠️ Rendered pins: 25, Pending pins: 2
flutter: 📍 ⚠️ Deferring layer switch until progressive rendering completes
flutter: 📍 ⚠️ Deferred layer switch: will switch to individual pins after progressive rendering
flutter: 📍 🔧 Added 6 new features to cluster-pins-source memory: 17 → 23
flutter: 📍 🎯 Incremental cluster layer update: 6 new features
flutter: 📍 ✅ Cluster layer updated with 23 total features (6 new)
flutter: 📍 🎯 Skipping cluster symbol update - no significant changes detected
flutter: 📍 ✅ Updated cluster layer with 5 new pins (total pins: 25, clusters: 1)
flutter: 📍 🔍 Validating layer visibility: individual=true, cluster=false
flutter: 🎯 User is within aura of 1 unique pins
flutter: 📍 ✅ Layer visibility validated and corrected if needed
flutter: 🎨 ✨ Progressive entrance animation for pin 15 at (LatLng(36.15833189653237, -86.77609753214917))
flutter: 🎨 🌟 Creating progressive glow for pin 15
flutter: 🎨 ✨ Pin 15 loaded with gentle animation
flutter: 🎨 ✨ Progressive entrance animation for pin 28 at (LatLng(36.16312913973052, -86.78293852121507))
flutter: 🎨 🌟 Creating progressive glow for pin 28
flutter: 🎨 ✨ Pin 28 loaded with gentle animation
flutter: 📍 ⏱️ Processing pin batch (2 pending, 25 rendered)
flutter: 📍 ⚡ Processing batch of 2 NEW pins (0 already rendered, 0 remaining)
flutter: 📍 🔄 Building batch of 2 pins to BOTH layers in parallel
flutter: 📍 ⚡ Batch processed - updating layer visibility: shouldCluster=false, current zoom=14.355972735216021 (2 pins added, 27 total)
flutter: 📍 Layer switch cooldown active, skipping
flutter: 📍 🔧 Computing pin features for 2 pins in parallel isolate
flutter: 🎨 ✨ Creating progressive pin animations for 2 pins
flutter: 🎨 ✨ Progressive entrance animation for pin 13 at (LatLng(36.10994853444598, -86.80902689147204))
flutter: 🎨 🌟 Creating progressive glow for pin 13
flutter: 🎨 ✨ Pin 13 loaded with gentle animation
flutter: 📍 🔧 Parallel isolate returned 2 features
flutter: 📍 🔧 Processing feature: 13
flutter: 📍 🔍 About to update cluster layer with 25 items
flutter: 📍 🔍 - Clusters: 1
flutter: 📍 🔍 - Individual pins: 24
flutter: 📍 🎯 Updating cluster layer: 25 items, 22 existing pins, 1 existing clusters
flutter: 📍 🎯 Adding NEW individual pin feature in cluster view: 13
flutter: 📍 🎯 Adding NEW individual pin feature in cluster view: 14
flutter: 🎨 ✨ Progressive entrance animation for pin 26 at (LatLng(36.157524736529766, -86.77469089028642))
flutter: 🎨 🌟 Creating progressive glow for pin 26
flutter: 🎨 ✨ Pin 26 loaded with gentle animation
flutter: 📍 🔧 Added 2 new features to cluster-pins-source memory: 23 → 25
flutter: 📍 🎯 Incremental cluster layer update: 2 new features
flutter: 📍 ✅ Cluster layer updated with 25 total features (2 new)
flutter: 📍 🎯 Skipping cluster symbol update - no significant changes detected
flutter: 📍 ✅ Updated cluster layer with 2 new pins (total pins: 27, clusters: 1)
flutter: 🎨 ✨ Progressive entrance animation for pin 14 at (LatLng(36.10493724268142, -86.80044659691973))
flutter: 🎨 🌟 Creating progressive glow for pin 14
flutter: 🎨 ✨ Pin 14 loaded with gentle animation
flutter: 🎨 ✨ Progressive entrance animation for pin 24 at (LatLng(36.1654684702875, -86.78038389283871))
flutter: 🎨 🌟 Creating progressive glow for pin 24
flutter: 🎨 ✨ Pin 24 loaded with gentle animation
flutter: 📍 🔍 Validating layer visibility: individual=true, cluster=false
flutter: 🎨 ✨ Progressive entrance animation for pin 19 at (LatLng(36.17083129630792, -86.78767330739612))
flutter: 🎨 🌟 Creating progressive glow for pin 19
flutter: 🎨 ✨ Pin 19 loaded with gentle animation
flutter: 📍 ✅ Layer visibility validated and corrected if needed
flutter: 📍 ✅ Progressive rendering timer completed, all pins processed
flutter: 📍 🎉 Progressive rendering complete! Rendered 27 pins
flutter: 📍 🎉 Rendered pin IDs: [31, 22, 5, 3, 11, 10, 23, 8, 1, 25, 2, 30, 21, 29, 4, 20, 27, 6, 9, 28, 18, 15, 26, 24, 19, 13, 14]
flutter: 📍 🎉 Cache size: 27, Real pin data size: 27
flutter: 🔍 Stopping scanning animation
flutter: 🔍 Removed scanning layers
flutter: 📍 Skipping glow animation during initial load
flutter: 📍 Skipping pin bounce animation during initial load
flutter: 📍 Initial pin load completed with progressive rendering
flutter: 📍 Pin state change: Pins: 0→27, Clusters: 0→1
flutter: 📍 ✅ Pin count validation passed: 27 pins rendered
flutter: 📍 Progressive rendering completed in 1337ms
flutter: 📍 🚀 Executing deferred layer switch after progressive rendering completion
flutter: 📍 🚀 Deferred state: shouldShowClusters=false
flutter: 📍 🚀 Rendered pins available: 27
flutter: 📍 🚀 Switching to individual layer (deferred from progressive rendering)
flutter: 📍 Enhanced layer switching: showClusters=false
flutter: 📍 Current zoom: 14.355972735216021, clustering threshold: 14.0
flutter: 📍 Current layer state: LayerState.showingIndividual
flutter: 📍 Progressive rendering check: inProgress=false, enabled=true, pending=0
flutter: 📍 State transition requested: LayerState.showingIndividual → LayerState.showingIndividual
flutter: 📍 🚦 No-op transition: already in LayerState.showingIndividual
flutter: 📍 ✅ Showing individual layers, hiding cluster layers
flutter: 📍 ⚡ Layer switch completed in 5ms (parallelized)
flutter: 📍 🔍 Validating layer visibility: individual=true, cluster=false
flutter: 📍 ✅ Layer visibility validated and corrected if needed
flutter: 📍 ✅ Deferred layer switch completed successfully
flutter: 🎯 Touch points changed: 0 → 1
flutter: 📍 🔍 Validating layer visibility: individual=true, cluster=false
flutter: 📍 ✅ Layer visibility validated and corrected if needed
flutter: 🎯 Touch points changed: 1 → 0
flutter: 📍 🔍 Validating layer visibility: individual=true, cluster=false
flutter: 📍 🔧 Added custom skin for pin 31: pin-skin-31
flutter: 📍 🔧 Updated real pin data for: 31
flutter: 📍 🔧 Processing feature: 22
flutter: 📍 ✅ Layer visibility validated and corrected if needed
flutter: 📍 🔧 Added custom skin for pin 31: pin-skin-31
flutter: 📍 🔧 Updated real pin data for: 31
flutter: 📍 🔧 Processing feature: 22
flutter: 📍 🔧 Added custom skin for pin 10: pin-skin-10
flutter: 📍 🔧 Updated real pin data for: 10
flutter: 📍 🔧 Processing feature: 23
flutter: 📍 🔧 Added custom skin for pin 18: pin-skin-18
flutter: 📍 🔧 Updated real pin data for: 18
flutter: 📍 🔧 Processing feature: 15
flutter: 📍 🔧 Added custom skin for pin 13: pin-skin-13
flutter: 📍 🔧 Updated real pin data for: 13
flutter: 📍 🔧 Processing feature: 14
flutter: 📍 🔧 Added custom skin for pin 2: pin-skin-2
flutter: 📍 🔧 Updated real pin data for: 2
flutter: 📍 🔧 Processing feature: 30
flutter: 📍 🔧 Added custom skin for pin 22: pin-skin-22
flutter: 📍 🔧 Updated real pin data for: 22
flutter: 📍 🔧 Processing feature: 5
flutter: 📍 🔧 Added custom skin for pin 22: pin-skin-22
flutter: 📍 🔧 Updated real pin data for: 22
flutter: 📍 🔧 Processing feature: 5
flutter: ✅ [SPOTIFY AI SEARCH] SUCCESS: 10 tracks returned for "upbeat cheerful" [CONTEXT: AI_ARTIST_BASED]
flutter: ✅ [AI SEARCH LOG] 21:30:35 | "upbeat cheerful" [AI_ARTIST_BASED] -> 10 results
flutter: 🔧 [Search Context] Category: artistBased, Genre: null, Context: AI_ARTIST_BASED
flutter: 🔍 [SPOTIFY AI SEARCH] [2025-08-01T21:30:35.087931] QUERY: "positive vibes" | LIMIT: 10 | OFFSET: 0 [CONTEXT: AI_ARTIST_BASED]
flutter: ❌ [SpotifyService] No access token found in either storage location
flutter: ❌ [SpotifyService] No access token found in either storage location
flutter: 📍 🔧 Added custom skin for pin 20: pin-skin-20
flutter: 📍 🔧 Updated real pin data for: 20
flutter: 📍 🔧 Processing feature: 27
flutter: ✅ [SPOTIFY AI SEARCH] SUCCESS: 10 tracks returned for "positive vibes" [CONTEXT: AI_ARTIST_BASED]
flutter: ✅ [AI SEARCH LOG] 21:30:35 | "positive vibes" [AI_ARTIST_BASED] -> 10 results
flutter: ✅ Total mood tracks collected: 137
flutter: 🎭 [Content Load] Concurrent mood+personalized loading completed in 4723ms
flutter: 🔄 [Spotify API] Converting GET to POST for user endpoint: https://api.bopmaps.com/api/spotify/me/top/artists/
flutter: 🎤 [Artist Recs] Getting current user artist recommendations...
flutter: 🏆 [SpotifyService] Getting top tracks (limit: 5)
flutter: ❌ [SpotifyService] Error getting top tracks: Exception: Not connected to Spotify
flutter: 🎤 [Artist Recs] Got 0 favorite artists from top tracks: []
flutter: 🎤 [Artist Recs] Falling back to user top artists: [Playboi Carti, Ken Carson, Lil Uzi Vert, Yeat, SABAI]
flutter: 🎯 [Artist Recs] *** ENTRY POINT *** Getting recommendations for 5 artists: [Playboi Carti, Ken Carson, Lil Uzi Vert, Yeat, SABAI]
flutter: 🎯 [Artist Recs] Current artist cache size: 0
flutter: 🎯 [Artist Recs] Shuffled artists: [Ken Carson, Playboi Carti, SABAI, Lil Uzi Vert, Yeat]
flutter: 🎤 [Artist Recs] Adding direct tracks from selected artists (cached hybrid method)...
flutter: 🗄️ [Artist Cache] Cache size before artist processing: 0
flutter: 🎤 [Artist Recs] Requesting tracks for "Ken Carson"...
flutter: 🗄️ [Artist Cache] ENTRY: _getCachedArtistTracks called for "Ken Carson" (limit: 35)
flutter: 🗄️ [Artist Cache] Cache MISS for "Ken Carson", fetching...
flutter: 🗄️ [Artist Cache] Fetching tracks for "Ken Carson"...
flutter: 🗄️ [Artist Cache] Using comprehensive parallel fetching for "Ken Carson"...
flutter: 🔧 [Search Context] Category: artistBased, Genre: null, Context: AI_ARTIST_BASED
flutter: 🔍 [DEBUG] Starting getTracksFromArtistAlbums for: "Ken Carson"
flutter: 🔍 [DEBUG] Step 1: Searching for artist candidates for "Ken Carson"...
flutter: 🎤 [SpotifyService] Searching for artists: "Ken Carson" (limit: 5, offset: 0)
flutter: 🔧 [Search Context] Category: artistBased, Genre: null, Context: AI_ARTIST_BASED
flutter: 🔄 [DEBUG] Starting hybrid search for: "Ken Carson"
flutter: 🔍 [DEBUG] Starting getTracksFromArtistAlbums for: "Ken Carson"
flutter: 🔍 [DEBUG] Step 1: Searching for artist candidates for "Ken Carson"...
flutter: 🎤 [SpotifyService] Searching for artists: "Ken Carson" (limit: 5, offset: 0)
flutter: 🎤 [Artist Recs] Requesting tracks for "Playboi Carti"...
flutter: 🗄️ [Artist Cache] ENTRY: _getCachedArtistTracks called for "Playboi Carti" (limit: 35)
flutter: 🗄️ [Artist Cache] Cache MISS for "Playboi Carti", fetching...
flutter: 🗄️ [Artist Cache] Fetching tracks for "Playboi Carti"...
flutter: 🗄️ [Artist Cache] Using comprehensive parallel fetching for "Playboi Carti"...
flutter: 🔧 [Search Context] Category: artistBased, Genre: null, Context: AI_ARTIST_BASED
flutter: 🔍 [DEBUG] Starting getTracksFromArtistAlbums for: "Playboi Carti"
flutter: 🔍 [DEBUG] Step 1: Searching for artist candidates for "Playboi Carti"...
flutter: 🎤 [SpotifyService] Searching for artists: "Playboi Carti" (limit: 5, offset: 0)
flutter: 🔧 [Search Context] Category: artistBased, Genre: null, Context: AI_ARTIST_BASED
flutter: 🔄 [DEBUG] Starting hybrid search for: "Playboi Carti"
flutter: 🔍 [DEBUG] Starting getTracksFromArtistAlbums for: "Playboi Carti"
flutter: 🔍 [DEBUG] Step 1: Searching for artist candidates for "Playboi Carti"...
flutter: 🎤 [SpotifyService] Searching for artists: "Playboi Carti" (limit: 5, offset: 0)
flutter: 🎤 [Artist Recs] Requesting tracks for "SABAI"...
flutter: 🗄️ [Artist Cache] ENTRY: _getCachedArtistTracks called for "SABAI" (limit: 35)
flutter: 🗄️ [Artist Cache] Cache MISS for "SABAI", fetching...
flutter: 🗄️ [Artist Cache] Fetching tracks for "SABAI"...
flutter: 🗄️ [Artist Cache] Using comprehensive parallel fetching for "SABAI"...
flutter: 🔧 [Search Context] Category: artistBased, Genre: null, Context: AI_ARTIST_BASED
flutter: 🔍 [DEBUG] Starting getTracksFromArtistAlbums for: "SABAI"
flutter: 🔍 [DEBUG] Step 1: Searching for artist candidates for "SABAI"...
flutter: 🎤 [SpotifyService] Searching for artists: "SABAI" (limit: 5, offset: 0)
flutter: 🔧 [Search Context] Category: artistBased, Genre: null, Context: AI_ARTIST_BASED
flutter: 🔄 [DEBUG] Starting hybrid search for: "SABAI"
flutter: 🔍 [DEBUG] Starting getTracksFromArtistAlbums for: "SABAI"
flutter: 🔍 [DEBUG] Step 1: Searching for artist candidates for "SABAI"...
flutter: 🎤 [SpotifyService] Searching for artists: "SABAI" (limit: 5, offset: 0)
flutter: 🎤 [Artist Recs] Requesting tracks for "Lil Uzi Vert"...
flutter: 🗄️ [Artist Cache] ENTRY: _getCachedArtistTracks called for "Lil Uzi Vert" (limit: 35)
flutter: 🗄️ [Artist Cache] Cache MISS for "Lil Uzi Vert", fetching...
flutter: 🗄️ [Artist Cache] Fetching tracks for "Lil Uzi Vert"...
flutter: 🗄️ [Artist Cache] Using comprehensive parallel fetching for "Lil Uzi Vert"...
flutter: 🔧 [Search Context] Category: artistBased, Genre: null, Context: AI_ARTIST_BASED
flutter: 🔍 [DEBUG] Starting getTracksFromArtistAlbums for: "Lil Uzi Vert"
flutter: 🔍 [DEBUG] Step 1: Searching for artist candidates for "Lil Uzi Vert"...
flutter: 🎤 [SpotifyService] Searching for artists: "Lil Uzi Vert" (limit: 5, offset: 0)
flutter: 🔧 [Search Context] Category: artistBased, Genre: null, Context: AI_ARTIST_BASED
flutter: 🔄 [DEBUG] Starting hybrid search for: "Lil Uzi Vert"
flutter: 🔍 [DEBUG] Starting getTracksFromArtistAlbums for: "Lil Uzi Vert"
flutter: 🔍 [DEBUG] Step 1: Searching for artist candidates for "Lil Uzi Vert"...
flutter: 🎤 [SpotifyService] Searching for artists: "Lil Uzi Vert" (limit: 5, offset: 0)
flutter: 📍 🔍 Validating current state: LayerState.showingIndividual
flutter: 📍 ✅ State validation passed
flutter: ❌ [SpotifyService] No access token found in either storage location
flutter: ❌ [SpotifyService] No access token found in either storage location
flutter: ❌ [SpotifyService] No access token found in either storage location
flutter: ❌ [SpotifyService] No access token found in either storage location
flutter: ❌ [SpotifyService] No access token found in either storage location
flutter: ❌ [SpotifyService] No access token found in either storage location
flutter: ❌ [SpotifyService] No access token found in either storage location
flutter: ❌ [SpotifyService] No access token found in either storage location
flutter: ❌ [SpotifyService] No access token found in either storage location
flutter: ✅ [SpotifyService] Found 5 artists for query: "Playboi Carti"
flutter: 🔍 [DEBUG] Artist search returned 5 candidates
flutter: 🔍 [DEBUG] Evaluating artist candidates:
flutter: 🔍 [DEBUG] - "Playboi Carti": similarity = 1.000
flutter: 🔍 [DEBUG] - "Ken Carson": similarity = 0.211
flutter: 🔍 [DEBUG] - "Playboi Cartiz": similarity = 0.957
flutter: 🔍 [DEBUG] - "Lil Uzi Vert": similarity = 0.100
flutter: 🔍 [DEBUG] - "Playboi Carti": similarity = 1.000
flutter: ✅ [DEBUG] Best match: "Playboi Carti" (ID: 699OTQXzgjhIYAHMy9RyPD, similarity: 1.000)
flutter: 🔍 [DEBUG] Step 2: Getting albums for artist ID: 699OTQXzgjhIYAHMy9RyPD
flutter: 🔍 [DEBUG] Include groups: [album, single, appears_on]
flutter: 📀 [SpotifyService] Getting albums for artist: 699OTQXzgjhIYAHMy9RyPD (limit: 50, offset: 0)
flutter: ❌ LocationManager ERROR: Error getting current location: TimeoutException after 0:00:05.000000: Future not completed
flutter: 🗺️ LocationManager: Status changed from LocationStatus.available to LocationStatus.error
flutter: ❌ [SpotifyService] No access token found in either storage location
flutter: 🎵 [MapScreen] Build - currentlyPlayingPinId: null
flutter: 🎵 [MapScreen] Build - hasCurrentlyPlayingPin: false
flutter: 🎵 [MapScreen] _buildCaptionPopup called
flutter: 🎵 [MapScreen] - currentlyPlayingPinId: null
flutter: 🎵 [MapScreen] - currentlyPlayingPinData: null
flutter: 🎵 [MapScreen] ❌ No currently playing pin or pin data
flutter: NavigationHelper: Getting current tab...
flutter: NavigationHelper: Checking route - name: /map, looking for: /profile, match: false
flutter: NavigationHelper: Checking route - name: /map, looking for: /explore, match: false
flutter: NavigationHelper: Checking route - name: /map, looking for: /friends, match: false
flutter: NavigationHelper: Checking route - name: /map, looking for: /challenges, match: false
flutter: NavigationHelper: Checking route - name: /map, looking for: /map, match: true
flutter: NavigationHelper: Current tab is MAP (explicit)
flutter: 📍 🔧 Added custom skin for pin 23: pin-skin-23
flutter: 📍 🔧 Updated real pin data for: 23
flutter: 📍 🔧 Processing feature: 8
flutter: 📀 [SpotifyService] Found 50 albums for artist: 699OTQXzgjhIYAHMy9RyPD
flutter: 🔍 [DEBUG] Albums API returned 50 albums
flutter: 🔍 [DEBUG] Step 3: Extracting album IDs...
flutter: 🔍 [DEBUG] Found 50 valid album IDs: [7sFZGbkCitoqQz7Rt3LoKg, 0fSfkmx0tdPqFYkJuNX74a, 2QRedhP5RmKJiJ1i8VgDGR, 7dAm8ShwJLFm9SaJ6Yc58O, 4rJgzzfFHAVFhCSt2P4I3j, 3kP4QpDTvR9jCHnlQdsKFV, 3cdcIcidzGHVQW5e9qQgtx, 73RJkZAkpUFHyGw4gTUQjK, 32kJxu3atkwQVZ6iOjw7UB, 00owbCur1BIK7SYX990qgJ, 4copfqei1g8euiEbhrc6Dz, 3hXfBV9COJBTj5rnIh7wcC, 0to6F8xxZGgeFNMq7TEMpi, 6RAcrLO3YsRyMQUXFCXLi2, 0qgq2i1rf5vl3cQqfQSq2z, 32lGAqeVkdJxEj2iv2Q01B, 3OxfaVgvTxUTy7276t7SPU, 18NOKLkZETa4sWwLMIm0UZ, 4iqbFIdGOTzXeDtt9owjQn, 6OQ9gBfg5EXeNAEwGSs6jK, 2jkrBfnQoV4eDTaoXWnVhg, 1TiWFnZwyZ152viq7v9C31, 5O0zUvdnJr0RbWzLFneN2i, 2WrNHOba5u6P9S9xEboaUy, 59zpaLOByFkJhc9D5Xqna9, 6SpT5TOPIInmmwLyCcCAXX, 1oUzjom2A9xtyRM1SdOAGj, 6ufaA5YvSGK3E9KnRFYvvo, 6LX75kNicFqjjiAOeZgN67, 004ywPlW72Hgn1Bo9PlNOr, 0FWdxPnncm63s91g0PkTvv, 18sR8zHx4zsVJUI4bHWuPC, 0tQ7Iu6EicQTPyhYRNWjaT, 2i5NyX1puwpGt7tmaP5sEg, 4qplno3lbet8WvuTGdDG3T, 6mym3v2HWcsH1oBN6A7SPm, 0mSQUlyUIJixplxMPE1HX0, 6VFETcEfjilkAng073KLt9, 0qr1Fvi1haEDWVbFtekZLb, 1hDf16zDN5SjIoMuy6fzB8, 7xYiTrbTL57QO0bb4hXIKo, 4Qsc4at71izsrBdiJv3Kds, 3GVblX9gjhR6bhP74EZQ3G, 3RaACfwYTY9uiDy3VSWLLc, 1WaOyfNu2VdtcC2A8hXn7u, 2NTDsRS6J2OF8qZwHWQjFV, 5E7Q6PZEvJQIGMYADlFra9, 0yhgc6S7U7pLxp0hjgPkH4, 7w2kc9SJxQlMXORfY2mrpM, 0p0FGxiCrl3afABenvtWbQ]
flutter: 🔍 [DEBUG] Step 4: Fetching album details and tracks...
flutter: 📀 [SpotifyService] Getting 50 albums in batch
flutter: ❌ [SpotifyService] No access token found in either storage location
flutter: ✅ [SpotifyService] Found 5 artists for query: "Lil Uzi Vert"
flutter: 🔍 [DEBUG] Artist search returned 5 candidates
flutter: 🔍 [DEBUG] Evaluating artist candidates:
flutter: 🔍 [DEBUG] - "Lil Uzi Vert": similarity = 1.000
flutter: 🔍 [DEBUG] - "Lil Tecca": similarity = 0.250
flutter: 🔍 [DEBUG] - "Lil Uzi Vert": similarity = 1.000
flutter: 🔍 [DEBUG] - "Yeat": similarity = 0.000
flutter: 🔍 [DEBUG] - "Triple9ine": similarity = 0.000
flutter: ✅ [DEBUG] Best match: "Lil Uzi Vert" (ID: 4O15NlyKLIASxsJ0PrXPfz, similarity: 1.000)
flutter: 🔍 [DEBUG] Step 2: Getting albums for artist ID: 4O15NlyKLIASxsJ0PrXPfz
flutter: 🔍 [DEBUG] Include groups: [album, single, appears_on]
flutter: 📀 [SpotifyService] Getting albums for artist: 4O15NlyKLIASxsJ0PrXPfz (limit: 50, offset: 0)
flutter: ✅ [SpotifyService] Found 5 artists for query: "Ken Carson"
flutter: 🔍 [DEBUG] Artist search returned 5 candidates
flutter: 🔍 [DEBUG] Evaluating artist candidates:
flutter: 🔍 [DEBUG] - "Ken Carson": similarity = 1.000
flutter: 🔍 [DEBUG] - "Ken Carson": similarity = 1.000
flutter: 🔍 [DEBUG] - "Playboi Carti": similarity = 0.211
flutter: 🔍 [DEBUG] - "Kendel Carson": similarity = 0.737
flutter: 🔍 [DEBUG] - "LUCKI": similarity = 0.000
flutter: ✅ [DEBUG] Best match: "Ken Carson" (ID: 3gBZUcNeVumkeeJ19CY2sX, similarity: 1.000)
flutter: 🔍 [DEBUG] Step 2: Getting albums for artist ID: 3gBZUcNeVumkeeJ19CY2sX
flutter: 🔍 [DEBUG] Include groups: [album, single, appears_on]
flutter: 📀 [SpotifyService] Getting albums for artist: 3gBZUcNeVumkeeJ19CY2sX (limit: 50, offset: 0)
flutter: ✅ [SpotifyService] Found 5 artists for query: "Ken Carson"
flutter: 🔍 [DEBUG] Artist search returned 5 candidates
flutter: 🔍 [DEBUG] Evaluating artist candidates:
flutter: 🔍 [DEBUG] - "Ken Carson": similarity = 1.000
flutter: 🔍 [DEBUG] - "Ken Carson": similarity = 1.000
flutter: 🔍 [DEBUG] - "Playboi Carti": similarity = 0.211
flutter: 🔍 [DEBUG] - "Kendel Carson": similarity = 0.737
flutter: 🔍 [DEBUG] - "LUCKI": similarity = 0.000
flutter: ✅ [DEBUG] Best match: "Ken Carson" (ID: 3gBZUcNeVumkeeJ19CY2sX, similarity: 1.000)
flutter: 🔍 [DEBUG] Step 2: Getting albums for artist ID: 3gBZUcNeVumkeeJ19CY2sX
flutter: 🔍 [DEBUG] Include groups: [album, single, appears_on]
flutter: 📀 [SpotifyService] Getting albums for artist: 3gBZUcNeVumkeeJ19CY2sX (limit: 50, offset: 0)
flutter: ✅ [SpotifyService] Found 5 artists for query: "SABAI"
flutter: 🔍 [DEBUG] Artist search returned 5 candidates
flutter: 🔍 [DEBUG] Evaluating artist candidates:
flutter: 🔍 [DEBUG] - "SABAI": similarity = 1.000
flutter: 🔍 [DEBUG] - "Survivor": similarity = 0.000
flutter: 🔍 [DEBUG] - "SABAI": similarity = 1.000
flutter: 🔍 [DEBUG] - "Sabrina Carpenter": similarity = 0.211
flutter: 🔍 [DEBUG] - "Survive Said The Prophet": similarity = 0.167
flutter: ✅ [DEBUG] Best match: "SABAI" (ID: 4OaSyxqlkp7aVpAZwF02QZ, similarity: 1.000)
flutter: 🔍 [DEBUG] Step 2: Getting albums for artist ID: 4OaSyxqlkp7aVpAZwF02QZ
flutter: 🔍 [DEBUG] Include groups: [album, single, appears_on]
flutter: 📀 [SpotifyService] Getting albums for artist: 4OaSyxqlkp7aVpAZwF02QZ (limit: 50, offset: 0)
flutter: ✅ [SpotifyService] Found 5 artists for query: "Lil Uzi Vert"
flutter: 🔍 [DEBUG] Artist search returned 5 candidates
flutter: 🔍 [DEBUG] Evaluating artist candidates:
flutter: 🔍 [DEBUG] - "Lil Uzi Vert": similarity = 1.000
flutter: 🔍 [DEBUG] - "Lil Tecca": similarity = 0.250
flutter: 🔍 [DEBUG] - "Lil Uzi Vert": similarity = 1.000
flutter: 🔍 [DEBUG] - "Yeat": similarity = 0.000
flutter: 🔍 [DEBUG] - "Triple9ine": similarity = 0.000
flutter: ✅ [DEBUG] Best match: "Lil Uzi Vert" (ID: 4O15NlyKLIASxsJ0PrXPfz, similarity: 1.000)
flutter: 🔍 [DEBUG] Step 2: Getting albums for artist ID: 4O15NlyKLIASxsJ0PrXPfz
flutter: 🔍 [DEBUG] Include groups: [album, single, appears_on]
flutter: 📀 [SpotifyService] Getting albums for artist: 4O15NlyKLIASxsJ0PrXPfz (limit: 50, offset: 0)
flutter: ✅ [SpotifyService] Found 5 artists for query: "Playboi Carti"
flutter: 🔍 [DEBUG] Artist search returned 5 candidates
flutter: 🔍 [DEBUG] Evaluating artist candidates:
flutter: 🔍 [DEBUG] - "Playboi Carti": similarity = 1.000
flutter: 🔍 [DEBUG] - "Ken Carson": similarity = 0.211
flutter: 🔍 [DEBUG] - "Playboi Cartiz": similarity = 0.957
flutter: 🔍 [DEBUG] - "Lil Uzi Vert": similarity = 0.100
flutter: 🔍 [DEBUG] - "Playboi Carti": similarity = 1.000
flutter: ✅ [DEBUG] Best match: "Playboi Carti" (ID: 699OTQXzgjhIYAHMy9RyPD, similarity: 1.000)
flutter: 🔍 [DEBUG] Step 2: Getting albums for artist ID: 699OTQXzgjhIYAHMy9RyPD
flutter: 🔍 [DEBUG] Include groups: [album, single, appears_on]
flutter: 📀 [SpotifyService] Getting albums for artist: 699OTQXzgjhIYAHMy9RyPD (limit: 50, offset: 0)
flutter: ✅ [SpotifyService] Found 5 artists for query: "SABAI"
flutter: 🔍 [DEBUG] Artist search returned 5 candidates
flutter: 🔍 [DEBUG] Evaluating artist candidates:
flutter: 🔍 [DEBUG] - "SABAI": similarity = 1.000
flutter: 🔍 [DEBUG] - "Survivor": similarity = 0.000
flutter: 🔍 [DEBUG] - "SABAI": similarity = 1.000
flutter: 🔍 [DEBUG] - "Sabrina Carpenter": similarity = 0.211
flutter: 🔍 [DEBUG] - "Survive Said The Prophet": similarity = 0.167
flutter: ✅ [DEBUG] Best match: "SABAI" (ID: 4OaSyxqlkp7aVpAZwF02QZ, similarity: 1.000)
flutter: 🔍 [DEBUG] Step 2: Getting albums for artist ID: 4OaSyxqlkp7aVpAZwF02QZ
flutter: 🔍 [DEBUG] Include groups: [album, single, appears_on]
flutter: 📀 [SpotifyService] Getting albums for artist: 4OaSyxqlkp7aVpAZwF02QZ (limit: 50, offset: 0)
flutter: ❌ [SpotifyService] No access token found in either storage location
flutter: ❌ [SpotifyService] No access token found in either storage location
flutter: ❌ [SpotifyService] No access token found in either storage location
flutter: ❌ [SpotifyService] No access token found in either storage location
flutter: ❌ [SpotifyService] No access token found in either storage location
flutter: ❌ [SpotifyService] No access token found in either storage location
flutter: ❌ [SpotifyService] No access token found in either storage location
flutter: 📍 🔧 Added custom skin for pin 30: pin-skin-30
flutter: 📍 🔧 Updated real pin data for: 30
flutter: 📍 🔧 Processing feature: 21
flutter: ❌ [SpotifyService] No access token found in either storage location
flutter: 📀 [SpotifyService] Found 43 albums for artist: 3gBZUcNeVumkeeJ19CY2sX
flutter: 🔍 [DEBUG] Albums API returned 43 albums
flutter: 🔍 [DEBUG] Step 3: Extracting album IDs...
flutter: 🔍 [DEBUG] Found 43 valid album IDs: [1TiWFnZwyZ152viq7v9C31, 58iEeJbYd6OBGRM0TiwltL, 0HS8aknH0JQdMIiBVhyOCW, 4Pangd3z0ZrONFpx3zMrFK, 191PJkW2uvXGUJPyl9KcdF, 4Vd0qWsKeNJzsouF5Dg1yS, 1bo9PRY5mlxqyDFaL8n4YL, 2LHNo0LDY2AszQvSuAMXWy, 0FRicF4BU62Z0OUMZw9l8U, 0uSAjejTknuT68AYVrzsmz, 5khW9rcdd32i7nCJlTXzqB, 1NwCufbDWeIm6nOKGp0Et6, 33afeafKvnHqT7p2lYgdhd, 4qp7MpTf0DBrHmnHROj99y, 5GG2M4TpU5fdhrmRNXkRyy, 0CLqdKIh14TmKqLZCs9dml, 4EURMuWFiLLRmQYoH5cgiE, 2Sl8X3Uu2N4B2pVa9y5U29, 0d1BFY8z15vye3KjtLvF3u, 0G4XTKDAeBmdfV8pZFaYu2, 0345WPzPBSeISh2IpIQWxT, 6BQgHrnJsXjBiuD1Q8zrmv, 20NEJgF7RPooqJ1dW0JZM1, 0pVQ3KO4jjqdeWJUrrkkWp, 0oTzeQbO6r9zT7axCYgTEw, 4vw2hN5xBkFXRBAfnGjUmK, 0zz2XwgWhbqgaFLVflgQHp, 58IBgtbmRfMrGBbnMrDq2A, 1POWgdYTzfFt9rhKlXFwsU, 6iTOKqu4Vl8q7oPzusfOB8, 3lCxVVRYSAwQljR4Q4NUkA, 1xOSJRyVoXb7wlEJatx5Ow, 4etrVAJXrkgLCTHWVhGJgS, 4CkICP9DdEovx6lQdLpDj9, 18iaenzPDoKCGxv7NWg1Du, 46TW30Wws4pwCfLJpDJzZH, 3UIHAda8Frxxmm1PnnGtgA, 7p73eYIQUcz4ep7ZGCYUkC, 0FEFWB2Y8Kjf41SOWiyOL1, 7jUGuEJ3YaFlAFqp0i6TWi, 5ogsaPRhePMUwSe73ZTlhv, 4Z4XFVkSHFAxM8Thwhh5Ws, 3KABOXbguDvh6dnsbjVQVk]
flutter: 🔍 [DEBUG] Step 4: Fetching album details and tracks...
flutter: 📀 [SpotifyService] Getting 43 albums in batch
flutter: 📀 [SpotifyService] Found 50 albums for artist: 4O15NlyKLIASxsJ0PrXPfz
flutter: 🔍 [DEBUG] Albums API returned 50 albums
flutter: 🔍 [DEBUG] Step 3: Extracting album IDs...
flutter: 🔍 [DEBUG] Found 50 valid album IDs: [7awBKnBq0qRLNyD5lI47R0, 2ua5bFkZLZl1lIgKWtYZIz, 3aITAVBURujVe8fhI2seeR, 48xpWR8K6CGpy3ETAym3pt, 0fEO7g2c5onkaXsybEtuD2, 7IyoGB8J31fvQDwGtHAq9m, 0zicd2mBV8HTzSubByj4vP, 733e1ZfktLSwj96X5rsMeE, 6LX75kNicFqjjiAOeZgN67, 7mgdTKTCdfnLoa1HXHvLYM, 5WrbKW1nRN4vSsu70uizxX, 4nAAKxptoKwx9okwEMSnPx, 0pUFmRt9tTZudRD8r75iJX, 1Qps54HR7OJ8GJsmoRl6ob, 6rsIciAfxrSApDOpNXZSll, 5R91APRxzHreNM7LGkrDG6, 5x9m2JPlFEaLv0bVtUC5gt, 4KLUYOUYaUMbd2V48KnxIC, 0GAddHHTPkZ6HAwzpF03YV, 1oeuqGef5PwGCyb6lxPoL4, 4VubFdZQLlos3I0LbB3qgt, 68PpQBKCqvAoq4x2Jd6e1u, 0vlK5wqy7Ptp2LUV0lnGaJ, 6fwby6V70isfpvy9qVWDTa, 1N36aG06vSw0jiZkYqDhhZ, 0aZKozv764HRTY4sZJDSnJ, 1n9nqA87ulG7FxxVrpULRA, 7BIhZ0MFIPfRb9nHSKSj0m, 42Oisekly7gBYeNddUea1s, 6356sTbHy0mybRmBHJ9ZYn, 0OANvKAXccPsVkFxyv6WJY, 2FD6g8bXEn2uQMYbeqqoCg, 1w4RwPZiIoRQPEbyrRleBk, 1t7uxPQC1hxn29ELxK6ysI, 0uXwRisshXdfubkYqLPiff, 07CbJ9iCpReVDssNIX5Ajd, 0DiX1J7XSaqme5a7XbRRaz, 75Y3CqX6l3NEBqigwc1soB, 2RADneMo8givvCbM6k6yPR, 5DGjYVxEuz3T1xqzqK9gTq, 5O64gzXImcbwT7z6AHXzX7, 3QvNSOIhsgI8od9CMLbMye, 0hKt4td7lkgrlAdTtjnXaF, 462FYqbPKtgQrdP390TKuA, 53JO525sp5lwWHkwy7iqpI, 4ozTwLAT3uZQDVmG3jJ0qA, 2or7a1pVeXbiDkIbjdxh43, 3VMNt0wqQaC7AhLaHNPOwX, 2LLu6xqRlPgR80v0lg5WR1, 3A5i5WurRyY0fytprSDsKH]
flutter: 🔍 [DEBUG] Step 4: Fetching album details and tracks...
flutter: 📀 [SpotifyService] Getting 50 albums in batch
flutter: 📍 🔧 Added custom skin for pin 5: pin-skin-5
flutter: 📍 🔧 Updated real pin data for: 5
flutter: 📍 🔧 Processing feature: 3
flutter: 🎯 Touch points changed: 0 → 1
flutter: ❌ [SpotifyService] No access token found in either storage location
flutter: ❌ [SpotifyService] No access token found in either storage location
flutter: 📀 [SpotifyService] Found 50 albums for artist: 4OaSyxqlkp7aVpAZwF02QZ
flutter: 🔍 [DEBUG] Albums API returned 50 albums
flutter: 🔍 [DEBUG] Step 3: Extracting album IDs...
flutter: 🔍 [DEBUG] Found 50 valid album IDs: [7eP2OC3vqDPo4LxhKG4aZj, 5F1HggSfqcLF2oG4GrZkSY, 02J7Pn3KTdKr9c4hWF2wBN, 09H1ifi9TEFlQYY0fbnERg, 0UJd27DrNEJ0eF0v2rvUMF, 1AdBmVCS36PtE4cqZPTe6U, 62QNsprfPKxxwXpGY2Hjab, 5BGQ43Yb9mwLWA9MViSwY7, 09JWbZi0y87fUvhHlPT33Y, 49OozqhwLzGrFptc9XsdnJ, 3ngqIFdOKPboplYvJfdk1k, 524elFEcT0FBG6E6uLO9TH, 53Ebe6qeLndbfZuJvwApYK, 5h2AdovdIx0FHbqClup37W, 7uTABom65EQwpKCDW1lwbJ, 5xYuqjKoAmc40rxq0yvsrT, 2mS5bt6HNrbxWKOBhAvR7i, 4wUoZ7Dy8W1P1K7S31ZkpZ, 01n4fn8rYmBaXgC4FSa7Yz, 11zxRKOtLdmx5HU63d9Pg6, 5NBxUTl2PHpOjpyfRljKq5, 4vEjKBgSWs2EvLWQ4FNL8n, 4GQ2GyHZYYAiIGrTjvTTaX, 53QPiqPVq7KSdDH7y4gybZ, 3ULu0cTm17kDaNsRhwC3Ee, 5I9Ojgq57rsMZPyGBBhZab, 3ojRr3zvD1AKwGXlnhuSIL, 5B1cDRRu36L97uXUjJ32XK, 0GjhkPj0cYFfB6NYrsf2Gz, 1l3DW3iuvlS7uxxhxjAWoF, 3ymyjHRaZNbk8c2Sa2u8sg, 4kPYN4m2ALwHLFMAG58cba, 64BNEj8QiXewIjgv6iL0fL, 3YKLAOSRa4RmkFhgrWaDYz, 1VRtBR0yB3BkQYCQhRgQR3, 481Nl5MUXOksFNRNYPRKOx, 7GNLoPVYrbprOfyCFk6qiT, 39eo4ynRcqzM4DP8WHLMGy, 0F48IWOiRVqr2bBV4J8c1H, 5mETU0u6GjGpVdBWQhSwc7, 4gHK1BpaJfdcaH1TAGbEf6, 3U7VyDFPH3h4v6kzQLx3ui, 4ixkbJ25V5wBDInAQJMZDR, 2P3UyvYnyIpXHzMnZpGlFE, 5ft63ve9l4E71Bxm4IKw3R, 1MIRmQpsPughPc4XRMc7EQ, 1zmcfV0W2uGTC39mujWHz6, 27kclO1NtEVVDpHMFdwQs3, 15RlA5XEn2mgSYXFTzUXhu, 1G581b7hODjE3txrb877G0]
flutter: 🔍 [DEBUG] Step 4: Fetching album details and tracks...
flutter: 📀 [SpotifyService] Getting 50 albums in batch
flutter: 🎯 Touch points changed: 1 → 2
flutter: 🎯 Starting gesture: GestureType.pinch (was: GestureState.none)
flutter: 🎯 Gesture state changed: GestureState.pinching
flutter: 📀 [SpotifyService] Found 50 albums for artist: 4OaSyxqlkp7aVpAZwF02QZ
flutter: 🔍 [DEBUG] Albums API returned 50 albums
flutter: 🔍 [DEBUG] Step 3: Extracting album IDs...
flutter: 🔍 [DEBUG] Found 50 valid album IDs: [7eP2OC3vqDPo4LxhKG4aZj, 5F1HggSfqcLF2oG4GrZkSY, 02J7Pn3KTdKr9c4hWF2wBN, 09H1ifi9TEFlQYY0fbnERg, 0UJd27DrNEJ0eF0v2rvUMF, 1AdBmVCS36PtE4cqZPTe6U, 62QNsprfPKxxwXpGY2Hjab, 5BGQ43Yb9mwLWA9MViSwY7, 09JWbZi0y87fUvhHlPT33Y, 49OozqhwLzGrFptc9XsdnJ, 3ngqIFdOKPboplYvJfdk1k, 524elFEcT0FBG6E6uLO9TH, 53Ebe6qeLndbfZuJvwApYK, 5h2AdovdIx0FHbqClup37W, 7uTABom65EQwpKCDW1lwbJ, 5xYuqjKoAmc40rxq0yvsrT, 2mS5bt6HNrbxWKOBhAvR7i, 4wUoZ7Dy8W1P1K7S31ZkpZ, 01n4fn8rYmBaXgC4FSa7Yz, 11zxRKOtLdmx5HU63d9Pg6, 5NBxUTl2PHpOjpyfRljKq5, 4vEjKBgSWs2EvLWQ4FNL8n, 4GQ2GyHZYYAiIGrTjvTTaX, 53QPiqPVq7KSdDH7y4gybZ, 3ULu0cTm17kDaNsRhwC3Ee, 5I9Ojgq57rsMZPyGBBhZab, 3ojRr3zvD1AKwGXlnhuSIL, 5B1cDRRu36L97uXUjJ32XK, 0GjhkPj0cYFfB6NYrsf2Gz, 1l3DW3iuvlS7uxxhxjAWoF, 3ymyjHRaZNbk8c2Sa2u8sg, 4kPYN4m2ALwHLFMAG58cba, 64BNEj8QiXewIjgv6iL0fL, 3YKLAOSRa4RmkFhgrWaDYz, 1VRtBR0yB3BkQYCQhRgQR3, 481Nl5MUXOksFNRNYPRKOx, 7GNLoPVYrbprOfyCFk6qiT, 39eo4ynRcqzM4DP8WHLMGy, 0F48IWOiRVqr2bBV4J8c1H, 5mETU0u6GjGpVdBWQhSwc7, 4gHK1BpaJfdcaH1TAGbEf6, 3U7VyDFPH3h4v6kzQLx3ui, 4ixkbJ25V5wBDInAQJMZDR, 2P3UyvYnyIpXHzMnZpGlFE, 5ft63ve9l4E71Bxm4IKw3R, 1MIRmQpsPughPc4XRMc7EQ, 1zmcfV0W2uGTC39mujWHz6, 27kclO1NtEVVDpHMFdwQs3, 15RlA5XEn2mgSYXFTzUXhu, 1G581b7hODjE3txrb877G0]
flutter: 🔍 [DEBUG] Step 4: Fetching album details and tracks...
flutter: 📀 [SpotifyService] Getting 50 albums in batch
flutter: 📀 [SpotifyService] Found 43 albums for artist: 3gBZUcNeVumkeeJ19CY2sX
flutter: 🔍 [DEBUG] Albums API returned 43 albums
flutter: 🔍 [DEBUG] Step 3: Extracting album IDs...
flutter: 🔍 [DEBUG] Found 43 valid album IDs: [1TiWFnZwyZ152viq7v9C31, 58iEeJbYd6OBGRM0TiwltL, 0HS8aknH0JQdMIiBVhyOCW, 4Pangd3z0ZrONFpx3zMrFK, 191PJkW2uvXGUJPyl9KcdF, 4Vd0qWsKeNJzsouF5Dg1yS, 1bo9PRY5mlxqyDFaL8n4YL, 2LHNo0LDY2AszQvSuAMXWy, 0FRicF4BU62Z0OUMZw9l8U, 0uSAjejTknuT68AYVrzsmz, 5khW9rcdd32i7nCJlTXzqB, 1NwCufbDWeIm6nOKGp0Et6, 33afeafKvnHqT7p2lYgdhd, 4qp7MpTf0DBrHmnHROj99y, 5GG2M4TpU5fdhrmRNXkRyy, 0CLqdKIh14TmKqLZCs9dml, 4EURMuWFiLLRmQYoH5cgiE, 2Sl8X3Uu2N4B2pVa9y5U29, 0d1BFY8z15vye3KjtLvF3u, 0G4XTKDAeBmdfV8pZFaYu2, 0345WPzPBSeISh2IpIQWxT, 6BQgHrnJsXjBiuD1Q8zrmv, 20NEJgF7RPooqJ1dW0JZM1, 0pVQ3KO4jjqdeWJUrrkkWp, 0oTzeQbO6r9zT7axCYgTEw, 4vw2hN5xBkFXRBAfnGjUmK, 0zz2XwgWhbqgaFLVflgQHp, 58IBgtbmRfMrGBbnMrDq2A, 1POWgdYTzfFt9rhKlXFwsU, 6iTOKqu4Vl8q7oPzusfOB8, 3lCxVVRYSAwQljR4Q4NUkA, 1xOSJRyVoXb7wlEJatx5Ow, 4etrVAJXrkgLCTHWVhGJgS, 4CkICP9DdEovx6lQdLpDj9, 18iaenzPDoKCGxv7NWg1Du, 46TW30Wws4pwCfLJpDJzZH, 3UIHAda8Frxxmm1PnnGtgA, 7p73eYIQUcz4ep7ZGCYUkC, 0FEFWB2Y8Kjf41SOWiyOL1, 7jUGuEJ3YaFlAFqp0i6TWi, 5ogsaPRhePMUwSe73ZTlhv, 4Z4XFVkSHFAxM8Thwhh5Ws, 3KABOXbguDvh6dnsbjVQVk]
flutter: 🔍 [DEBUG] Step 4: Fetching album details and tracks...
flutter: 📀 [SpotifyService] Getting 43 albums in batch
flutter: 📀 [SpotifyService] Found 50 albums for artist: 699OTQXzgjhIYAHMy9RyPD
flutter: 🔍 [DEBUG] Albums API returned 50 albums
flutter: 🔍 [DEBUG] Step 3: Extracting album IDs...
flutter: 🔍 [DEBUG] Found 50 valid album IDs: [7sFZGbkCitoqQz7Rt3LoKg, 0fSfkmx0tdPqFYkJuNX74a, 2QRedhP5RmKJiJ1i8VgDGR, 7dAm8ShwJLFm9SaJ6Yc58O, 4rJgzzfFHAVFhCSt2P4I3j, 3kP4QpDTvR9jCHnlQdsKFV, 3cdcIcidzGHVQW5e9qQgtx, 73RJkZAkpUFHyGw4gTUQjK, 32kJxu3atkwQVZ6iOjw7UB, 00owbCur1BIK7SYX990qgJ, 4copfqei1g8euiEbhrc6Dz, 3hXfBV9COJBTj5rnIh7wcC, 0to6F8xxZGgeFNMq7TEMpi, 6RAcrLO3YsRyMQUXFCXLi2, 0qgq2i1rf5vl3cQqfQSq2z, 32lGAqeVkdJxEj2iv2Q01B, 3OxfaVgvTxUTy7276t7SPU, 18NOKLkZETa4sWwLMIm0UZ, 4iqbFIdGOTzXeDtt9owjQn, 6OQ9gBfg5EXeNAEwGSs6jK, 2jkrBfnQoV4eDTaoXWnVhg, 1TiWFnZwyZ152viq7v9C31, 5O0zUvdnJr0RbWzLFneN2i, 2WrNHOba5u6P9S9xEboaUy, 59zpaLOByFkJhc9D5Xqna9, 6SpT5TOPIInmmwLyCcCAXX, 1oUzjom2A9xtyRM1SdOAGj, 6ufaA5YvSGK3E9KnRFYvvo, 6LX75kNicFqjjiAOeZgN67, 004ywPlW72Hgn1Bo9PlNOr, 0FWdxPnncm63s91g0PkTvv, 18sR8zHx4zsVJUI4bHWuPC, 0tQ7Iu6EicQTPyhYRNWjaT, 2i5NyX1puwpGt7tmaP5sEg, 4qplno3lbet8WvuTGdDG3T, 6mym3v2HWcsH1oBN6A7SPm, 0mSQUlyUIJixplxMPE1HX0, 6VFETcEfjilkAng073KLt9, 0qr1Fvi1haEDWVbFtekZLb, 1hDf16zDN5SjIoMuy6fzB8, 7xYiTrbTL57QO0bb4hXIKo, 4Qsc4at71izsrBdiJv3Kds, 3GVblX9gjhR6bhP74EZQ3G, 3RaACfwYTY9uiDy3VSWLLc, 1WaOyfNu2VdtcC2A8hXn7u, 2NTDsRS6J2OF8qZwHWQjFV, 5E7Q6PZEvJQIGMYADlFra9, 0yhgc6S7U7pLxp0hjgPkH4, 7w2kc9SJxQlMXORfY2mrpM, 0p0FGxiCrl3afABenvtWbQ]
flutter: 🔍 [DEBUG] Step 4: Fetching album details and tracks...
flutter: 📀 [SpotifyService] Getting 50 albums in batch
flutter: 📀 [SpotifyService] Found 50 albums for artist: 4O15NlyKLIASxsJ0PrXPfz
flutter: 🔍 [DEBUG] Albums API returned 50 albums
flutter: 🔍 [DEBUG] Step 3: Extracting album IDs...
flutter: 🔍 [DEBUG] Found 50 valid album IDs: [7awBKnBq0qRLNyD5lI47R0, 2ua5bFkZLZl1lIgKWtYZIz, 3aITAVBURujVe8fhI2seeR, 48xpWR8K6CGpy3ETAym3pt, 0fEO7g2c5onkaXsybEtuD2, 7IyoGB8J31fvQDwGtHAq9m, 0zicd2mBV8HTzSubByj4vP, 733e1ZfktLSwj96X5rsMeE, 6LX75kNicFqjjiAOeZgN67, 7mgdTKTCdfnLoa1HXHvLYM, 5WrbKW1nRN4vSsu70uizxX, 4nAAKxptoKwx9okwEMSnPx, 0pUFmRt9tTZudRD8r75iJX, 1Qps54HR7OJ8GJsmoRl6ob, 6rsIciAfxrSApDOpNXZSll, 5R91APRxzHreNM7LGkrDG6, 5x9m2JPlFEaLv0bVtUC5gt, 4KLUYOUYaUMbd2V48KnxIC, 0GAddHHTPkZ6HAwzpF03YV, 1oeuqGef5PwGCyb6lxPoL4, 4VubFdZQLlos3I0LbB3qgt, 68PpQBKCqvAoq4x2Jd6e1u, 0vlK5wqy7Ptp2LUV0lnGaJ, 6fwby6V70isfpvy9qVWDTa, 1N36aG06vSw0jiZkYqDhhZ, 0aZKozv764HRTY4sZJDSnJ, 1n9nqA87ulG7FxxVrpULRA, 7BIhZ0MFIPfRb9nHSKSj0m, 42Oisekly7gBYeNddUea1s, 6356sTbHy0mybRmBHJ9ZYn, 0OANvKAXccPsVkFxyv6WJY, 2FD6g8bXEn2uQMYbeqqoCg, 1w4RwPZiIoRQPEbyrRleBk, 1t7uxPQC1hxn29ELxK6ysI, 0uXwRisshXdfubkYqLPiff, 07CbJ9iCpReVDssNIX5Ajd, 0DiX1J7XSaqme5a7XbRRaz, 75Y3CqX6l3NEBqigwc1soB, 2RADneMo8givvCbM6k6yPR, 5DGjYVxEuz3T1xqzqK9gTq, 5O64gzXImcbwT7z6AHXzX7, 3QvNSOIhsgI8od9CMLbMye, 0hKt4td7lkgrlAdTtjnXaF, 462FYqbPKtgQrdP390TKuA, 53JO525sp5lwWHkwy7iqpI, 4ozTwLAT3uZQDVmG3jJ0qA, 2or7a1pVeXbiDkIbjdxh43, 3VMNt0wqQaC7AhLaHNPOwX, 2LLu6xqRlPgR80v0lg5WR1, 3A5i5WurRyY0fytprSDsKH]
flutter: 🔍 [DEBUG] Step 4: Fetching album details and tracks...
flutter: 📀 [SpotifyService] Getting 50 albums in batch
flutter: ❌ [SpotifyService] No access token found in either storage location
flutter: ❌ [SpotifyService] No access token found in either storage location
flutter: ❌ [SpotifyService] No access token found in either storage location
flutter: ❌ [SpotifyService] No access token found in either storage location
flutter: ❌ [SpotifyService] No access token found in either storage location
flutter: 🗺️ ===== GESTURE DETECTOR TAP =====
flutter: 🗺️ Tap at: 227.3333282470703, 541.0
flutter: 🎯 Tap blocked - current gesture: GestureState.pinching
flutter: 🎯 Tap blocked by gesture manager
flutter: 📍 🔧 Added custom skin for pin 5: pin-skin-5
flutter: 📍 🔧 Updated real pin data for: 5
flutter: 📍 🔧 Processing feature: 3
flutter: ❌ [SpotifyService] No access token found in either storage location
flutter: ❌ [SpotifyService] No access token found in either storage location
flutter: ❌ [SpotifyService] No access token found in either storage location
flutter: ❌ [SpotifyService] No access token found in either storage location
flutter: ❌ [SpotifyService] No access token found in either storage location
flutter: 🎯 Gesture timeout - clearing state
flutter: 🎯 Gesture state changed: GestureState.none
flutter: ❌ [SpotifyService] No access token found in either storage location
flutter: ❌ [SpotifyService] No access token found in either storage location
flutter: ❌ [SpotifyService] No access token found in either storage location
flutter: 📍 🔧 Added custom skin for pin 14: pin-skin-14
flutter: 📍 🔧 Updated real pin data for: 14
flutter: 📍 🔧 Added 2 new features to individual-pins-source memory: 0 → 2
flutter: 📍 ✅ Added 2 pins to individual layer (total: 2)
flutter: 📍 ✅ Processed pin IDs: [13, 14]
flutter: 📍 ✨ Adding incremental glow effects for batch of 2 pins
flutter: 🌟 Added 4 features to glow-polygons-source memory (total: 4)
flutter: 🌟 Added 2 features to border-polygons-source memory (total: 2)
flutter: 🌟 Added 2 features to pulse-polygons-source memory (total: 2)
flutter: 🌟 Updated existing glow sources with 4 glow features (accumulated across batches)
flutter: 🌟 Added incremental glow effects for 2 pins
flutter: 📍 ✨ Incremental glow effects added for batch
flutter: 📍 ✅ Parallel layer building completed for batch of 2 pins
flutter: 📍 ✨ Incremental display: Refreshed layer visibility after batch (showing clusters)
flutter: 🎯 Touch points changed: 2 → 1
flutter: 🎯 Touch points changed: 1 → 0
flutter: 📀 [SpotifyService] Retrieved 50 albums with tracks
flutter: 🔍 [DEBUG] Multiple albums API returned 50 albums with tracks
flutter: 🔍 [DEBUG] Album "MUSIC - SORRY 4 DA WAIT" has 34 tracks
flutter: 🔍 [DEBUG] Album "MUSIC" has 30 tracks
flutter: 🔍 [DEBUG] Album "Whole Lotta Red" has 24 tracks
flutter: 🔍 [DEBUG] Album "Die Lit" has 19 tracks
flutter: 🔍 [DEBUG] Album "Playboi Carti" has 15 tracks
flutter: 🔍 [DEBUG] Album "Blick Sum (feat. Playboi Carti)" has 1 tracks
flutter: 🔍 [DEBUG] Album "ALL RED" has 1 tracks
flutter: 🔍 [DEBUG] Album "FIELD TRIP" has 1 tracks
flutter: 🔍 [DEBUG] Album "I LUV IT (feat. Playboi Carti)" has 1 tracks
flutter: 🔍 [DEBUG] Album "Unlock It (feat. Playboi Carti)" has 1 tracks
flutter: 🔍 [DEBUG] Album "Miss The Rage" has 1 tracks
flutter: 🔍 [DEBUG] Album "@ MEH" has 1 tracks
flutter: 🔍 [DEBUG] Album "Paid In Full" has 1 tracks
flutter: 🔍 [DEBUG] Album "Crumbs" has 1 tracks
flutter: 🔍 [DEBUG] Album "Broke Boi" has 1 tracks
flutter: 🔍 [DEBUG] Album "JACKBOYS 2" has 17 tracks
flutter: 🔍 [DEBUG] Album "Hurry Up Tomorrow" has 22 tracks
flutter: 🔍 [DEBUG] Album "UTOPIA" has 19 tracks
flutter: 🔍 [DEBUG] Album "WE DON'T TRUST YOU" has 17 tracks
flutter: 🔍 [DEBUG] Album "Dark Lane Demo Tapes" has 14 tracks
flutter: 🔍 [DEBUG] Album "MASA" has 30 tracks
flutter: 🔍 [DEBUG] Album "More Chaos" has 22 tracks
flutter: 🔍 [DEBUG] Album "Popular (Music from the HBO Original Series)" has 1 tracks
flutter: 🔍 [DEBUG] Album "Perfect Ten" has 10 tracks
flutter: 🔍 [DEBUG] Album "Quality Control: Control The Streets Volume 2" has 36 tracks
flutter: 🔍 [DEBUG] Album "Trip At Knight" has 18 tracks
flutter: 🔍 [DEBUG] Album "Drip or Drown 2" has 16 tracks
flutter: 🔍 [DEBUG] Album "Lil Boat 3.5" has 27 tracks
flutter: 🔍 [DEBUG] Album "The Perfect LUV Tape" has 10 tracks
flutter: 🔍 [DEBUG] Album "C,XOXO" has 14 tracks
flutter: 🔍 [DEBUG] Album "Timeless (Remix)" has 3 tracks
flutter: 🔍 [DEBUG] Album "The Highlights (Deluxe)" has 36 tracks
flutter: 🔍 [DEBUG] Album "Still Striving" has 14 tracks
flutter: 🔍 [DEBUG] Album "The Life Of Pi'erre 5" has 16 tracks
flutter: 🔍 [DEBUG] Album "Nuthin' 2 Prove" has 15 tracks
flutter: 🔍 [DEBUG] Album "C,XOXO (Magic City Edition)" has 18 tracks
flutter: 🔍 [DEBUG] Album "Head In The Clouds" has 17 tracks
flutter: 🔍 [DEBUG] Album "Perfect Timing" has 15 tracks
flutter: 🔍 [DEBUG] Album "Cozy Tapes Vol. 2: Too Cozy" has 17 tracks
flutter: 🔍 [DEBUG] Album "OMW2 REXDALE" has 14 tracks
flutter: 🔍 [DEBUG] Album "Lust For Life" has 16 tracks
flutter: 🔍 [DEBUG] Album "Sugar Honey Iced Tea" has 23 tracks
flutter: 🔍 [DEBUG] Album "Freewave" has 10 tracks
flutter: 🔍 [DEBUG] Album "Cozy Tapes: Vol. 1 Friends -" has 12 tracks
flutter: 🔍 [DEBUG] Album "Beef" has 1 tracks
flutter: 🔍 [DEBUG] Album "Big Baby DRAM (Deluxe Version)" has 22 tracks
flutter: 🔍 [DEBUG] Album "OMW2 REXDALE (DELUXE)" has 22 tracks
flutter: 🔍 [DEBUG] Album "Mansion Musick" has 11 tracks
flutter: 🔍 [DEBUG] Album "Trap Ye Season 2" has 20 tracks
flutter: 🔍 [DEBUG] Album "Popular" has 5 tracks
flutter: ✅ [DEBUG] Successfully retrieved 90 tracks from albums
flutter: ✅ [AI SEARCH LOG] 21:30:36 | "Playboi Carti" [AI_ARTIST_BASED] -> 90 results
flutter: ✅ [DEBUG] Album-based search successful: 90 tracks
flutter: ❌ [SpotifyService] No access token found in either storage location
flutter: ❌ [SpotifyService] No access token found in either storage location
flutter: ❌ [SpotifyService] No access token found in either storage location
flutter: ❌ [SpotifyService] No access token found in either storage location
flutter: 📍 🔧 Added custom skin for pin 15: pin-skin-15
flutter: 📍 🔧 Updated real pin data for: 15
flutter: 📍 🔧 Processing feature: 26
flutter: ❌ [SpotifyService] No access token found in either storage location
flutter: 📍 🔄 Zoom-triggered layer switch: zoom=12.84116680463075, shouldCluster=true, currently showing clusters=false, layers initialized=true
flutter: 📍 🔄 Switching to cluster view with 27 progressively rendered pins
flutter: 📍 🔄 Rebuilding clusters from 27 progressively rendered pins
flutter: 📍 🔄 Optimized cluster rebuild with 27 rendered pins
flutter: 📍 🎯 Updating cluster layer: 25 items, 24 existing pins, 1 existing clusters
flutter: 📍 🎯 Adding NEW individual pin feature in cluster view: 31
flutter: ❌ [SpotifyService] No access token found in either storage location
flutter: 📊 Parallel processing: 1 pins in 5ms
flutter: 📊 Average processing time: 10ms per batch
flutter: 📊 Total pins processed: 57
flutter: 📍 🎯 Adding NEW individual pin feature in cluster view: 22
flutter: 📊 Parallel processing: 1 pins in 4ms
flutter: 📊 Average processing time: 10ms per batch
flutter: 📊 Total pins processed: 58
flutter: 📍 🎯 Adding NEW individual pin feature in cluster view: 5
flutter: 📊 Parallel processing: 1 pins in 0ms
flutter: 📊 Average processing time: 10ms per batch
flutter: 📊 Total pins processed: 59
flutter: 📍 🎯 Adding NEW individual pin feature in cluster view: 3
flutter: 📊 Parallel processing: 1 pins in 0ms
flutter: 📊 Average processing time: 9ms per batch
flutter: 📊 Total pins processed: 60
flutter: 📍 🎯 Adding NEW individual pin feature in cluster view: 11
flutter: 📊 Parallel processing: 1 pins in 0ms
flutter: 📊 Average processing time: 9ms per batch
flutter: 📊 Total pins processed: 61
flutter: 📍 🎯 Adding NEW individual pin feature in cluster view: 10
flutter: ❌ [SpotifyService] No access token found in either storage location
flutter: 📊 Parallel processing: 1 pins in 0ms
flutter: 📊 Average processing time: 9ms per batch
flutter: 📊 Total pins processed: 62
flutter: 📍 🎯 Adding NEW individual pin feature in cluster view: 23
flutter: 📊 Parallel processing: 1 pins in 0ms
flutter: 📊 Average processing time: 9ms per batch
flutter: 📊 Total pins processed: 63
flutter: 📍 🎯 Adding NEW individual pin feature in cluster view: 8
flutter: 📊 Parallel processing: 1 pins in 0ms
flutter: 📊 Average processing time: 8ms per batch
flutter: 📊 Total pins processed: 64
flutter: 📍 🎯 Adding NEW individual pin feature in cluster view: 1
flutter: 📊 Parallel processing: 1 pins in 0ms
flutter: 📊 Average processing time: 8ms per batch
flutter: 📊 Total pins processed: 65
flutter: 📍 🎯 Adding NEW individual pin feature in cluster view: 25
flutter: 📊 Parallel processing: 1 pins in 0ms
flutter: 📊 Average processing time: 8ms per batch
flutter: 📊 Total pins processed: 66
flutter: 📍 🎯 Adding NEW individual pin feature in cluster view: 21
flutter: 📊 Parallel processing: 1 pins in 0ms
flutter: 📊 Average processing time: 8ms per batch
flutter: 📊 Total pins processed: 67
flutter: 📍 🎯 Adding NEW individual pin feature in cluster view: 2
flutter: 📊 Parallel processing: 1 pins in 0ms
flutter: 📊 Average processing time: 8ms per batch
flutter: 📊 Total pins processed: 68
flutter: 📍 🎯 Adding NEW individual pin feature in cluster view: 30
flutter: 📊 Parallel processing: 1 pins in 0ms
flutter: 📊 Average processing time: 7ms per batch
flutter: 📊 Total pins processed: 69
flutter: 📍 🎯 Adding NEW individual pin feature in cluster view: 29
flutter: 📊 Parallel processing: 1 pins in 0ms
flutter: 📊 Average processing time: 7ms per batch
flutter: 📊 Total pins processed: 70
flutter: 📍 🎯 Adding NEW individual pin feature in cluster view: 4
flutter: 📊 Parallel processing: 1 pins in 0ms
flutter: 📊 Average processing time: 7ms per batch
flutter: 📊 Total pins processed: 71
flutter: 📍 🎯 Adding/updating cluster feature: cluster_36.15777467214386_-86.78386433062093 with 3 pins
flutter: 📍 🎯 Adding NEW individual pin feature in cluster view: 6
flutter: 📊 Parallel processing: 1 pins in 39ms
flutter: 📊 Average processing time: 8ms per batch
flutter: 📊 Total pins processed: 72
flutter: 📍 🎯 Adding NEW individual pin feature in cluster view: 28
flutter: 📊 Parallel processing: 1 pins in 13ms
flutter: 📊 Average processing time: 8ms per batch
flutter: 📊 Total pins processed: 73
flutter: 📍 🎯 Adding NEW individual pin feature in cluster view: 18
flutter: 📊 Parallel processing: 1 pins in 20ms
flutter: 📊 Average processing time: 8ms per batch
flutter: 📊 Total pins processed: 74
flutter: 📍 🎯 Adding NEW individual pin feature in cluster view: 15
flutter: 📊 Parallel processing: 1 pins in 13ms
flutter: 📊 Average processing time: 8ms per batch
flutter: 📊 Total pins processed: 75
flutter: 📍 🎯 Adding NEW individual pin feature in cluster view: 26
flutter: 📊 Parallel processing: 1 pins in 24ms
flutter: 📊 Average processing time: 8ms per batch
flutter: 📊 Total pins processed: 76
flutter: 📍 🎯 Adding NEW individual pin feature in cluster view: 24
flutter: 📊 Parallel processing: 1 pins in 7ms
flutter: 📊 Average processing time: 8ms per batch
flutter: 📊 Total pins processed: 77
flutter: 📍 🎯 Adding NEW individual pin feature in cluster view: 19
flutter: 📍 🔧 Added custom skin for pin 27: pin-skin-27
flutter: 📍 🔧 Updated real pin data for: 27
flutter: 📍 🔧 Processing feature: 6
flutter: 📊 Parallel processing: 1 pins in 19ms
flutter: 📊 Average processing time: 9ms per batch
flutter: 📊 Total pins processed: 78
flutter: 📍 🎯 Adding NEW individual pin feature in cluster view: 13
flutter: 📀 [SpotifyService] Retrieved 50 albums with tracks
flutter: 🔍 [DEBUG] Multiple albums API returned 50 albums with tracks
flutter: 🔍 [DEBUG] Album "Eternal Atake 2" has 16 tracks
flutter: 🔍 [DEBUG] Album "Pink Tape" has 26 tracks
flutter: 🔍 [DEBUG] Album "Pluto x Baby Pluto (Deluxe)" has 24 tracks
flutter: 🔍 [DEBUG] Album "Pluto x Baby Pluto" has 16 tracks
flutter: 🔍 [DEBUG] Album "Eternal Atake (Deluxe) - LUV vs. The World 2" has 32 tracks
flutter: 🔍 [DEBUG] Album "Eternal Atake" has 18 tracks
flutter: 🔍 [DEBUG] Album "Luv Is Rage 2 (Deluxe)" has 20 tracks
flutter: 🔍 [DEBUG] Album "Luv Is Rage 2" has 16 tracks
flutter: 🔍 [DEBUG] Album "The Perfect LUV Tape" has 10 tracks
flutter: 🔍 [DEBUG] Album "Lil Uzi Vert vs. The World" has 9 tracks
flutter: 🔍 [DEBUG] Album "Luv Is Rage" has 12 tracks
flutter: 🔍 [DEBUG] Album "Uzi The Earthling! (TV Show Theme)" has 1 tracks
flutter: 🔍 [DEBUG] Album "DONNY DARKO (FEAT. LIL UZI VERT)" has 1 tracks
flutter: 🔍 [DEBUG] Album "Red Moon" has 1 tracks
flutter: 🔍 [DEBUG] Album "NFL" has 1 tracks
flutter: 🔍 [DEBUG] Album "Pink Tape: Boss Battle" has 5 tracks
flutter: 🔍 [DEBUG] Album "Pink Tape: Level 4" has 5 tracks
flutter: 🔍 [DEBUG] Album "Pink Tape: Level 3" has 5 tracks
flutter: 🔍 [DEBUG] Album "Pink Tape: Level 2" has 5 tracks
flutter: 🔍 [DEBUG] Album "Endless Fashion (with Nicki Minaj) [Versions]" has 4 tracks
flutter: 🔍 [DEBUG] Album "Blood Moon (feat. Lil Uzi Vert)" has 1 tracks
flutter: 🔍 [DEBUG] Album "Pink Tape: Level 1" has 6 tracks
flutter: 🔍 [DEBUG] Album "Werewolf (feat. Bring Me The Horizon)" has 1 tracks
flutter: 🔍 [DEBUG] Album "The End (feat. BABYMETAL)" has 1 tracks
flutter: 🔍 [DEBUG] Album "Patience (feat. Don Toliver)" has 1 tracks
flutter: 🔍 [DEBUG] Album "Endless Fashion (feat. Nicki Minaj)" has 1 tracks
flutter: 🔍 [DEBUG] Album "AmEN! (feat. Lil Uzi Vert and Daryl Palumbo of Glassjaw)" has 1 tracks
flutter: 🔍 [DEBUG] Album "Free Game" has 1 tracks
flutter: 🔍 [DEBUG] Album "Watch This (ARIZONATEARS Pluggnb Remix) [Slowed Down Version]" has 2 tracks
flutter: 🔍 [DEBUG] Album "Watch This (ARIZONATEARS Pluggnb Remix)" has 3 tracks
flutter: 🔍 [DEBUG] Album "spend the money (feat. Lil Uzi Vert)" has 1 tracks
flutter: 🔍 [DEBUG] Album "Just Wanna Rock" has 1 tracks
flutter: 🔍 [DEBUG] Album "RED & WHITE" has 9 tracks
flutter: 🔍 [DEBUG] Album "SPACE CADET" has 1 tracks
flutter: 🔍 [DEBUG] Album "I KNOW" has 1 tracks
flutter: 🔍 [DEBUG] Album "HITTIN MY SHOULDER" has 1 tracks
flutter: 🔍 [DEBUG] Album "FLEX UP" has 1 tracks
flutter: 🔍 [DEBUG] Album "Next Up (feat. Lil Uzi Vert & Gucci Mane)" has 1 tracks
flutter: 🔍 [DEBUG] Album "Heavy" has 1 tracks
flutter: 🔍 [DEBUG] Album "Demon High" has 1 tracks
flutter: 🔍 [DEBUG] Album "V12 (feat. Lil Uzi Vert)" has 1 tracks
flutter: 🔍 [DEBUG] Album "Blue Notes 2 (feat. Lil Uzi Vert)" has 1 tracks
flutter: 🔍 [DEBUG] Album "Holy Smokes (feat. Lil Uzi Vert)" has 1 tracks
flutter: 🔍 [DEBUG] Album "Teenage Dream 2 (with Lil Uzi Vert)" has 1 tracks
flutter: 🔍 [DEBUG] Album "Sossboy 2 (feat. Lil Uzi Vert)" has 1 tracks
flutter: 🔍 [DEBUG] Album "His & Hers (feat. Don Toliver & Lil Uzi Vert)" has 1 tracks
flutter: 🔍 [DEBUG] Album "BADASS" has 1 tracks
flutter: 🔍 [DEBUG] Album "Diamond Choker (feat. Lil Uzi Vert)" has 1 tracks
flutter: 🔍 [DEBUG] Album "Over Your Head" has 1 tracks
flutter: 🔍 [DEBUG] Album "Patek" has 1 tracks
flutter: ✅ [DEBUG] Successfully retrieved 90 tracks from albums
flutter: ✅ [AI SEARCH LOG] 21:30:36 | "Lil Uzi Vert" [AI_ARTIST_BASED] -> 90 results
flutter: ✅ [DEBUG] Album-based search successful: 90 tracks
flutter: 📀 [SpotifyService] Retrieved 50 albums with tracks
flutter: 🔍 [DEBUG] Multiple albums API returned 50 albums with tracks
flutter: 🔍 [DEBUG] Album "Eternal Atake 2" has 16 tracks
flutter: 🔍 [DEBUG] Album "Pink Tape" has 26 tracks
flutter: 🔍 [DEBUG] Album "Pluto x Baby Pluto (Deluxe)" has 24 tracks
flutter: 🔍 [DEBUG] Album "Pluto x Baby Pluto" has 16 tracks
flutter: 🔍 [DEBUG] Album "Eternal Atake (Deluxe) - LUV vs. The World 2" has 32 tracks
flutter: 🔍 [DEBUG] Album "Eternal Atake" has 18 tracks
flutter: 🔍 [DEBUG] Album "Luv Is Rage 2 (Deluxe)" has 20 tracks
flutter: 🔍 [DEBUG] Album "Luv Is Rage 2" has 16 tracks
flutter: 🔍 [DEBUG] Album "The Perfect LUV Tape" has 10 tracks
flutter: 🔍 [DEBUG] Album "Lil Uzi Vert vs. The World" has 9 tracks
flutter: 🔍 [DEBUG] Album "Luv Is Rage" has 12 tracks
flutter: 🔍 [DEBUG] Album "Uzi The Earthling! (TV Show Theme)" has 1 tracks
flutter: 🔍 [DEBUG] Album "DONNY DARKO (FEAT. LIL UZI VERT)" has 1 tracks

flutter: 🔍 [DEBUG] Album "Red Moon" has 1 tracks
flutter: 🔍 [DEBUG] Album "NFL" has 1 tracks
flutter: 🔍 [DEBUG] Album "Pink Tape: Boss Battle" has 5 tracks
flutter: 🔍 [DEBUG] Album "Pink Tape: Level 4" has 5 tracks
flutter: 🔍 [DEBUG] Album "Pink Tape: Level 3" has 5 tracks
flutter: 🔍 [DEBUG] Album "Pink Tape: Level 2" has 5 tracks
flutter: 🔍 [DEBUG] Album "Endless Fashion (with Nicki Minaj) [Versions]" has 4 tracks
flutter: 🔍 [DEBUG] Album "Blood Moon (feat. Lil Uzi Vert)" has 1 tracks
flutter: 🔍 [DEBUG] Album "Pink Tape: Level 1" has 6 tracks
flutter: 🔍 [DEBUG] Album "Werewolf (feat. Bring Me The Horizon)" has 1 tracks
flutter: 🔍 [DEBUG] Album "The End (feat. BABYMETAL)" has 1 tracks
flutter: 🔍 [DEBUG] Album "Patience (feat. Don Toliver)" has 1 tracks
flutter: 🔍 [DEBUG] Album "Endless Fashion (feat. Nicki Minaj)" has 1 tracks
flutter: 🔍 [DEBUG] Album "AmEN! (feat. Lil Uzi Vert and Daryl Palumbo of Glassjaw)" has 1 tracks
flutter: 🔍 [DEBUG] Album "Free Game" has 1 tracks
flutter: 🔍 [DEBUG] Album "Watch This (ARIZONATEARS Pluggnb Remix) [Slowed Down Version]" has 2 tracks
flutter: 🔍 [DEBUG] Album "Watch This (ARIZONATEARS Pluggnb Remix)" has 3 tracks
flutter: 🔍 [DEBUG] Album "spend the money (feat. Lil Uzi Vert)" has 1 tracks
flutter: 🔍 [DEBUG] Album "Just Wanna Rock" has 1 tracks
flutter: 🔍 [DEBUG] Album "RED & WHITE" has 9 tracks
flutter: 🔍 [DEBUG] Album "SPACE CADET" has 1 tracks
flutter: 🔍 [DEBUG] Album "I KNOW" has 1 tracks
flutter: 🔍 [DEBUG] Album "HITTIN MY SHOULDER" has 1 tracks
flutter: 🔍 [DEBUG] Album "FLEX UP" has 1 tracks
flutter: 🔍 [DEBUG] Album "Next Up (feat. Lil Uzi Vert & Gucci Mane)" has 1 tracks
flutter: 🔍 [DEBUG] Album "Heavy" has 1 tracks
flutter: 🔍 [DEBUG] Album "Demon High" has 1 tracks
flutter: 🔍 [DEBUG] Album "V12 (feat. Lil Uzi Vert)" has 1 tracks
flutter: 🔍 [DEBUG] Album "Blue Notes 2 (feat. Lil Uzi Vert)" has 1 tracks
flutter: 🔍 [DEBUG] Album "Holy Smokes (feat. Lil Uzi Vert)" has 1 tracks
flutter: 🔍 [DEBUG] Album "Teenage Dream 2 (with Lil Uzi Vert)" has 1 tracks
flutter: 🔍 [DEBUG] Album "Sossboy 2 (feat. Lil Uzi Vert)" has 1 tracks
flutter: 🔍 [DEBUG] Album "His & Hers (feat. Don Toliver & Lil Uzi Vert)" has 1 tracks
flutter: 🔍 [DEBUG] Album "BADASS" has 1 tracks
flutter: 🔍 [DEBUG] Album "Diamond Choker (feat. Lil Uzi Vert)" has 1 tracks
flutter: 🔍 [DEBUG] Album "Over Your Head" has 1 tracks
flutter: 🔍 [DEBUG] Album "Patek" has 1 tracks
flutter: ✅ [DEBUG] Successfully retrieved 100 tracks from albums
flutter: ✅ [AI SEARCH LOG] 21:30:36 | "Lil Uzi Vert" [AI_ARTIST_BASED] -> 100 results
flutter: 🗄️ [Artist Cache] Got 100 album tracks + 30 hybrid tracks for "Lil Uzi Vert" (PARALLEL)
flutter: 🗄️ [Artist Cache] Step 3: Deduplicating 130 tracks for "Lil Uzi Vert"...
flutter: 🗄️ [Artist Cache] ✅ Successfully cached 100 unique tracks for "Lil Uzi Vert"
flutter: ⏱️ [Artist Cache] Fetched and cached "Lil Uzi Vert" in 1726ms
flutter: 🎤 [Artist Recs] Cached tracks for "Lil Uzi Vert": 35 tracks (1727ms)
flutter: 📊 Parallel processing: 1 pins in 22ms
flutter: 📊 Average processing time: 9ms per batch
flutter: 📊 Total pins processed: 79
flutter: 📍 🎯 Adding NEW individual pin feature in cluster view: 14
flutter: 📊 Parallel processing: 1 pins in 10ms
flutter: 📊 Average processing time: 9ms per batch
flutter: 📊 Total pins processed: 80
flutter: 📍 🧹 Cleared layer features for source: cluster-pins-source
flutter: 📍 🔧 Added 25 new features to cluster-pins-source memory: 0 → 25
flutter: 📍 🎯 Full cluster layer update with 25 features
flutter: 📍 ✅ Cluster layer updated with 25 total features (25 new)
flutter: 📍 🎯 Skipping cluster symbol update - no significant changes detected
flutter: 📍 ✅ Optimized cluster rebuild complete: 1 clusters
flutter: 📍 Enhanced layer switching: showClusters=true
flutter: 📍 Current zoom: 12.84116680463075, clustering threshold: 14.0
flutter: 📍 Current layer state: LayerState.showingIndividual
flutter: 📍 Progressive rendering check: inProgress=false, enabled=true, pending=0
flutter: 📍 State transition requested: LayerState.showingIndividual → LayerState.showingClusters
flutter: 📍 Layer state changed: LayerState.showingIndividual → LayerState.transitioning
flutter: 📍 State updated: LayerState.showingIndividual → LayerState.transitioning
flutter: 📀 [SpotifyService] Retrieved 43 albums with tracks
flutter: 🔍 [DEBUG] Multiple albums API returned 43 albums with tracks
flutter: 🔍 [DEBUG] Album "More Chaos" has 22 tracks
flutter: 🔍 [DEBUG] Album "A Great Chaos (Deluxe)" has 25 tracks
flutter: 🔍 [DEBUG] Album "A Great Chaos" has 18 tracks
flutter: 🔍 [DEBUG] Album "XTENDED" has 25 tracks
flutter: 🔍 [DEBUG] Album "X" has 20 tracks
flutter: 🔍 [DEBUG] Album "Project X" has 11 tracks
flutter: 🔍 [DEBUG] Album "PB&J (with Ken Carson)" has 1 tracks
flutter: 🔍 [DEBUG] Album "delusional" has 1 tracks
flutter: 🔍 [DEBUG] Album "overseas" has 1 tracks
flutter: 🔍 [DEBUG] Album "i need u" has 1 tracks
flutter: 🔍 [DEBUG] Album "Teen Bean" has 1 tracks
flutter: 🔍 [DEBUG] Album "Teen X : Relapsed" has 5 tracks
flutter: 🔍 [DEBUG] Album "Teen X" has 6 tracks
flutter: 🔍 [DEBUG] Album "Boy Barbie" has 5 tracks
flutter: 🔍 [DEBUG] Album "Fold" has 1 tracks
flutter: 🔍 [DEBUG] Album "DOPAMINE" has 17 tracks
flutter: 🔍 [DEBUG] Album "TEC" has 16 tracks
flutter: 🔍 [DEBUG] Album "If Looks Could Kill" has 26 tracks
flutter: 🔍 [DEBUG] Album "2 Alivë" has 20 tracks
flutter: 🔍 [DEBUG] Album "NO STYLIST" has 19 tracks
flutter: 🔍 [DEBUG] Album "2 Alivë (Geëk Pack)" has 29 tracks
flutter: 🔍 [DEBUG] Album "LOVE LASTS FOREVER" has 21 tracks
flutter: 🔍 [DEBUG] Album "NS+ (ULTRA)" has 24 tracks
flutter: 🔍 [DEBUG] Album "TEC (Bonus)" has 17 tracks
flutter: 🔍 [DEBUG] Album "If Looks Could Kill (Directors Cut)" has 32 tracks
flutter: 🔍 [DEBUG] Album "Homixide Lifestyle" has 18 tracks
flutter: 🔍 [DEBUG] Album "SONY" has 20 tracks
flutter: 🔍 [DEBUG] Album "LOVE LASTS FOREVER V2.5" has 31 tracks
flutter: 🔍 [DEBUG] Album "Pink Heartz" has 16 tracks
flutter: 🔍 [DEBUG] Album "President (feat. Ken Carson)" has 1 tracks
flutter: 🔍 [DEBUG] Album "LOVE LASTS FOREVER: PRESENTED BY BLAKAMERIKA" has 23 tracks
flutter: 🔍 [DEBUG] Album "Hell Yeah" has 1 tracks
flutter: 🔍 [DEBUG] Album "Hip Hop Moshpit by STOKED - Rage Mix" has 35 tracks
flutter: 🔍 [DEBUG] Album "Natural Habitat" has 1 tracks
flutter: 🔍 [DEBUG] Album "The Twin Society" has 8 tracks
flutter: 🔍 [DEBUG] Album "New Money" has 33 tracks
flutter: 🔍 [DEBUG] Album "Fatal Attraction" has 10 tracks
flutter: 🔍 [DEBUG] Album "HYPERPOP HITS" has 37 tracks
flutter: 🔍 [DEBUG] Album "jtim" has 8 tracks
flutter: 🔍 [DEBUG] Album "Natural Habitat" has 1 tracks
flutter: 🔍 [DEBUG] Album "Tańczę teraz jak..." has 39 tracks
flutter: 🔍 [DEBUG] Album "VFILES LOUD (Vol. 2: Fake I.D.)" has 3 tracks
flutter: 🔍 [DEBUG] Album "Homixide Lifestyle 2" has 25 tracks
flutter: ✅ [DEBUG] Successfully retrieved 100 tracks from albums
flutter: ✅ [AI SEARCH LOG] 21:30:37 | "Ken Carson" [AI_ARTIST_BASED] -> 100 results
flutter: 📀 [SpotifyService] Retrieved 50 albums with tracks
flutter: 🔍 [DEBUG] Multiple albums API returned 50 albums with tracks
flutter: 🔍 [DEBUG] Album "North Star" has 14 tracks
flutter: 🔍 [DEBUG] Album "Fuck It, I'm Alright" has 2 tracks
flutter: 🔍 [DEBUG] Album "Are You Happy Now CloudNone Remix" has 2 tracks
flutter: 🔍 [DEBUG] Album "Are You Happy Now Highlnd Remix" has 3 tracks
flutter: 🔍 [DEBUG] Album "Are You Happy Now (feat. Linney) [Acoustic]" has 2 tracks
flutter: 🔍 [DEBUG] Album "I Miss You" has 1 tracks
flutter: 🔍 [DEBUG] Album "Never Fall" has 1 tracks
flutter: 🔍 [DEBUG] Album "Are You Happy Now" has 1 tracks
flutter: 🔍 [DEBUG] Album "Save You (VIP)" has 2 tracks
flutter: 🔍 [DEBUG] Album "Say It Like You Mean It (Remixes)" has 2 tracks
flutter: 🔍 [DEBUG] Album "Say It Like You Mean It (BOTCASH REMIX)" has 1 tracks
flutter: 🔍 [DEBUG] Album "Heart Don't Work Like That" has 1 tracks
flutter: 🔍 [DEBUG] Album "Somebody to Love Me" has 1 tracks
flutter: 🔍 [DEBUG] Album "My Heaven" has 1 tracks
flutter: 🔍 [DEBUG] Album "Daydream" has 1 tracks
flutter: 🔍 [DEBUG] Album "Say It Like You Mean It" has 1 tracks
flutter: 🔍 [DEBUG] Album "Landslide (BOTCASH Remix)" has 1 tracks
flutter: 🔍 [DEBUG] Album "North Star (Remixes)" has 5 tracks
flutter: 🔍 [DEBUG] Album "Harder to Breathe" has 1 tracks
flutter: 🔍 [DEBUG] Album "The Call" has 1 tracks
flutter: 🔍 [DEBUG] Album "North Star" has 1 tracks
flutter: 🔍 [DEBUG] Album "Landslide" has 1 tracks
flutter: 🔍 [DEBUG] Album "Lucky Ones" has 1 tracks
flutter: 🔍 [DEBUG] Album "Mirror" has 1 tracks
flutter: 🔍 [DEBUG] Album "Love Again (Acoustic)" has 1 tracks
flutter: 🔍 [DEBUG] Album "Love Again (Remix)" has 2 tracks
flutter: 🔍 [DEBUG] Album "Dead In The Water" has 1 tracks
flutter: 🔍 [DEBUG] Album "Ready For Love" has 1 tracks
flutter: 🔍 [DEBUG] Album "Love Again" has 1 tracks
flutter: 🔍 [DEBUG] Album "Trust Issues" has 1 tracks
flutter: 🔍 [DEBUG] Album "Love For You" has 1 tracks
flutter: 🔍 [DEBUG] Album "Falling For You" has 1 tracks
flutter: 🔍 [DEBUG] Album "Scared" has 1 tracks
flutter: 🔍 [DEBUG] Album "Me + You (The Remixes)" has 7 tracks
flutter: 🔍 [DEBUG] Album "Me + You (Rogue Remix)" has 1 tracks
flutter: 🔍 [DEBUG] Album "Me + You" has 1 tracks
flutter: 🔍 [DEBUG] Album "Million Days (Acoustic)" has 2 tracks
flutter: 🔍 [DEBUG] Album "I Need Ya" has 1 tracks
flutter: 🔍 [DEBUG] Album "Where It All Began" has 4 tracks
flutter: 🔍 [DEBUG] Album "Broken Glass" has 1 tracks
flutter: 🔍 [DEBUG] Album "Save You" has 1 tracks
flutter: 🔍 [DEBUG] Album "Million Days" has 1 tracks
flutter: 🔍 [DEBUG] Album "Another Life" has 1 tracks
flutter: 🔍 [DEBUG] Album "Don't Turn Back" has 7 tracks
flutter: 🔍 [DEBUG] Album "COPE" has 10 tracks
flutter: 🔍 [DEBUG] Album "Monstercat Instinct Vol. 7" has 50 tracks
flutter: 🔍 [DEBUG] Album "COPE (Remixes)" has 10 tracks
flutter: 🔍 [DEBUG] Album "Elements Remixed" has 4 tracks
flutter: 🔍 [DEBUG] Album "Monstercat Instinct Vol. 6" has 40 tracks
flutter: 🔍 [DEBUG] Album "Monstercat Instinct Vol. 5" has 40 tracks
flutter: ✅ [DEBUG] Successfully retrieved 100 tracks from albums
flutter: ✅ [AI SEARCH LOG] 21:30:37 | "SABAI" [AI_ARTIST_BASED] -> 100 results
flutter: 📀 [SpotifyService] Retrieved 43 albums with tracks
flutter: 🔍 [DEBUG] Multiple albums API returned 43 albums with tracks
flutter: 🔍 [DEBUG] Album "More Chaos" has 22 tracks
flutter: 🔍 [DEBUG] Album "A Great Chaos (Deluxe)" has 25 tracks
flutter: 🔍 [DEBUG] Album "A Great Chaos" has 18 tracks
flutter: 🔍 [DEBUG] Album "XTENDED" has 25 tracks
flutter: 🔍 [DEBUG] Album "X" has 20 tracks
flutter: 🔍 [DEBUG] Album "Project X" has 11 tracks
flutter: 🔍 [DEBUG] Album "PB&J (with Ken Carson)" has 1 tracks
flutter: 🔍 [DEBUG] Album "delusional" has 1 tracks
flutter: 🔍 [DEBUG] Album "overseas" has 1 tracks
flutter: 🔍 [DEBUG] Album "i need u" has 1 tracks
flutter: 🔍 [DEBUG] Album "Teen Bean" has 1 tracks
flutter: 🔍 [DEBUG] Album "Teen X : Relapsed" has 5 tracks
flutter: 🔍 [DEBUG] Album "Teen X" has 6 tracks
flutter: 🔍 [DEBUG] Album "Boy Barbie" has 5 tracks
flutter: 🔍 [DEBUG] Album "Fold" has 1 tracks
flutter: 🔍 [DEBUG] Album "DOPAMINE" has 17 tracks
flutter: 🔍 [DEBUG] Album "TEC" has 16 tracks
flutter: 🔍 [DEBUG] Album "If Looks Could Kill" has 26 tracks
flutter: 🔍 [DEBUG] Album "2 Alivë" has 20 tracks
flutter: 🔍 [DEBUG] Album "NO STYLIST" has 19 tracks
flutter: 🔍 [DEBUG] Album "2 Alivë (Geëk Pack)" has 29 tracks
flutter: 🔍 [DEBUG] Album "LOVE LASTS FOREVER" has 21 tracks
flutter: 🔍 [DEBUG] Album "NS+ (ULTRA)" has 24 tracks
flutter: 🔍 [DEBUG] Album "TEC (Bonus)" has 17 tracks
flutter: 🔍 [DEBUG] Album "If Looks Could Kill (Directors Cut)" has 32 tracks
flutter: 🔍 [DEBUG] Album "Homixide Lifestyle" has 18 tracks
flutter: 🔍 [DEBUG] Album "SONY" has 20 tracks
flutter: 🔍 [DEBUG] Album "LOVE LASTS FOREVER V2.5" has 31 tracks
flutter: 🔍 [DEBUG] Album "Pink Heartz" has 16 tracks
flutter: 🔍 [DEBUG] Album "President (feat. Ken Carson)" has 1 tracks
flutter: 🔍 [DEBUG] Album "LOVE LASTS FOREVER: PRESENTED BY BLAKAMERIKA" has 23 tracks
flutter: 🔍 [DEBUG] Album "Hell Yeah" has 1 tracks
flutter: 🔍 [DEBUG] Album "Hip Hop Moshpit by STOKED - Rage Mix" has 35 tracks
flutter: 🔍 [DEBUG] Album "Natural Habitat" has 1 tracks
flutter: 🔍 [DEBUG] Album "The Twin Society" has 8 tracks
flutter: 🔍 [DEBUG] Album "New Money" has 33 tracks
flutter: 🔍 [DEBUG] Album "Fatal Attraction" has 10 tracks
flutter: 🔍 [DEBUG] Album "HYPERPOP HITS" has 37 tracks
flutter: 🔍 [DEBUG] Album "jtim" has 8 tracks
flutter: 🔍 [DEBUG] Album "Natural Habitat" has 1 tracks
flutter: 🔍 [DEBUG] Album "Tańczę teraz jak..." has 39 tracks
flutter: 🔍 [DEBUG] Album "VFILES LOUD (Vol. 2: Fake I.D.)" has 3 tracks
flutter: 🔍 [DEBUG] Album "Homixide Lifestyle 2" has 25 tracks
flutter: ✅ [DEBUG] Successfully retrieved 90 tracks from albums
flutter: ✅ [AI SEARCH LOG] 21:30:37 | "Ken Carson" [AI_ARTIST_BASED] -> 90 results
flutter: ✅ [DEBUG] Album-based search successful: 90 tracks
flutter: 🗄️ [Artist Cache] Got 100 album tracks + 30 hybrid tracks for "Ken Carson" (PARALLEL)
flutter: 🗄️ [Artist Cache] Step 3: Deduplicating 130 tracks for "Ken Carson"...
flutter: 🗄️ [Artist Cache] ✅ Successfully cached 100 unique tracks for "Ken Carson"
flutter: ⏱️ [Artist Cache] Fetched and cached "Ken Carson" in 1839ms
flutter: 🎤 [Artist Recs] Cached tracks for "Ken Carson": 35 tracks (1840ms)
flutter: 📀 [SpotifyService] Retrieved 50 albums with tracks
flutter: 🔍 [DEBUG] Multiple albums API returned 50 albums with tracks
flutter: 🔍 [DEBUG] Album "MUSIC - SORRY 4 DA WAIT" has 34 tracks
flutter: 🔍 [DEBUG] Album "MUSIC" has 30 tracks
flutter: 🔍 [DEBUG] Album "Whole Lotta Red" has 24 tracks
flutter: 🔍 [DEBUG] Album "Die Lit" has 19 tracks
flutter: 🔍 [DEBUG] Album "Playboi Carti" has 15 tracks
flutter: 🔍 [DEBUG] Album "Blick Sum (feat. Playboi Carti)" has 1 tracks
flutter: 🔍 [DEBUG] Album "ALL RED" has 1 tracks
flutter: 🔍 [DEBUG] Album "FIELD TRIP" has 1 tracks
flutter: 🔍 [DEBUG] Album "I LUV IT (feat. Playboi Carti)" has 1 tracks
flutter: 🔍 [DEBUG] Album "Unlock It (feat. Playboi Carti)" has 1 tracks
flutter: 🔍 [DEBUG] Album "Miss The Rage" has 1 tracks
flutter: 🔍 [DEBUG] Album "@ MEH" has 1 tracks
flutter: 🔍 [DEBUG] Album "Paid In Full" has 1 tracks
flutter: 🔍 [DEBUG] Album "Crumbs" has 1 tracks
flutter: 🔍 [DEBUG] Album "Broke Boi" has 1 tracks
flutter: 🔍 [DEBUG] Album "JACKBOYS 2" has 17 tracks
flutter: 🔍 [DEBUG] Album "Hurry Up Tomorrow" has 22 tracks
flutter: 🔍 [DEBUG] Album "UTOPIA" has 19 tracks
flutter: 🔍 [DEBUG] Album "WE DON'T TRUST YOU" has 17 tracks
flutter: 🔍 [DEBUG] Album "Dark Lane Demo Tapes" has 14 tracks
flutter: 🔍 [DEBUG] Album "MASA" has 30 tracks
flutter: 🔍 [DEBUG] Album "More Chaos" has 22 tracks
flutter: 🔍 [DEBUG] Album "Popular (Music from the HBO Original Series)" has 1 tracks
flutter: 🔍 [DEBUG] Album "Perfect Ten" has 10 tracks
flutter: 🔍 [DEBUG] Album "Quality Control: Control The Streets Volume 2" has 36 tracks
flutter: 🔍 [DEBUG] Album "Trip At Knight" has 18 tracks
flutter: 🔍 [DEBUG] Album "Drip or Drown 2" has 16 tracks
flutter: 🔍 [DEBUG] Album "Lil Boat 3.5" has 27 tracks
flutter: 🔍 [DEBUG] Album "The Perfect LUV Tape" has 10 tracks
flutter: 🔍 [DEBUG] Album "C,XOXO" has 14 tracks
flutter: 🔍 [DEBUG] Album "Timeless (Remix)" has 3 tracks
flutter: 🔍 [DEBUG] Album "The Highlights (Deluxe)" has 36 tracks
flutter: 🔍 [DEBUG] Album "Still Striving" has 14 tracks
flutter: 🔍 [DEBUG] Album "The Life Of Pi'erre 5" has 16 tracks
flutter: 🔍 [DEBUG] Album "Nuthin' 2 Prove" has 15 tracks
flutter: 🔍 [DEBUG] Album "C,XOXO (Magic City Edition)" has 18 tracks
flutter: 🔍 [DEBUG] Album "Head In The Clouds" has 17 tracks
flutter: 🔍 [DEBUG] Album "Perfect Timing" has 15 tracks
flutter: 🔍 [DEBUG] Album "Cozy Tapes Vol. 2: Too Cozy" has 17 tracks
flutter: 🔍 [DEBUG] Album "OMW2 REXDALE" has 14 tracks
flutter: 🔍 [DEBUG] Album "Lust For Life" has 16 tracks
flutter: 🔍 [DEBUG] Album "Sugar Honey Iced Tea" has 23 tracks
flutter: 🔍 [DEBUG] Album "Freewave" has 10 tracks
flutter: 🔍 [DEBUG] Album "Cozy Tapes: Vol. 1 Friends -" has 12 tracks
flutter: 🔍 [DEBUG] Album "Beef" has 1 tracks
flutter: 🔍 [DEBUG] Album "Big Baby DRAM (Deluxe Version)" has 22 tracks
flutter: 🔍 [DEBUG] Album "OMW2 REXDALE (DELUXE)" has 22 tracks
flutter: 🔍 [DEBUG] Album "Mansion Musick" has 11 tracks
flutter: 🔍 [DEBUG] Album "Trap Ye Season 2" has 20 tracks
flutter: 🔍 [DEBUG] Album "Popular" has 5 tracks
flutter: ✅ [DEBUG] Successfully retrieved 100 tracks from albums
flutter: ✅ [AI SEARCH LOG] 21:30:37 | "Playboi Carti" [AI_ARTIST_BASED] -> 100 results
flutter: 🗄️ [Artist Cache] Got 100 album tracks + 30 hybrid tracks for "Playboi Carti" (PARALLEL)
flutter: 🗄️ [Artist Cache] Step 3: Deduplicating 130 tracks for "Playboi Carti"...
flutter: 🗄️ [Artist Cache] ✅ Successfully cached 100 unique tracks for "Playboi Carti"
flutter: ⏱️ [Artist Cache] Fetched and cached "Playboi Carti" in 1859ms
flutter: 🎤 [Artist Recs] Cached tracks for "Playboi Carti": 35 tracks (1859ms)
flutter: 📀 [SpotifyService] Retrieved 50 albums with tracks
flutter: 🔍 [DEBUG] Multiple albums API returned 50 albums with tracks
flutter: 🔍 [DEBUG] Album "North Star" has 14 tracks
flutter: 🔍 [DEBUG] Album "Fuck It, I'm Alright" has 2 tracks
flutter: 🔍 [DEBUG] Album "Are You Happy Now CloudNone Remix" has 2 tracks
flutter: 🔍 [DEBUG] Album "Are You Happy Now Highlnd Remix" has 3 tracks
flutter: 🔍 [DEBUG] Album "Are You Happy Now (feat. Linney) [Acoustic]" has 2 tracks
flutter: 🔍 [DEBUG] Album "I Miss You" has 1 tracks
flutter: 🔍 [DEBUG] Album "Never Fall" has 1 tracks
flutter: 🔍 [DEBUG] Album "Are You Happy Now" has 1 tracks
flutter: 🔍 [DEBUG] Album "Save You (VIP)" has 2 tracks
flutter: 🔍 [DEBUG] Album "Say It Like You Mean It (Remixes)" has 2 tracks
flutter: 🔍 [DEBUG] Album "Say It Like You Mean It (BOTCASH REMIX)" has 1 tracks
flutter: 🔍 [DEBUG] Album "Heart Don't Work Like That" has 1 tracks
flutter: 🔍 [DEBUG] Album "Somebody to Love Me" has 1 tracks
flutter: 🔍 [DEBUG] Album "My Heaven" has 1 tracks
flutter: 🔍 [DEBUG] Album "Daydream" has 1 tracks
flutter: 🔍 [DEBUG] Album "Say It Like You Mean It" has 1 tracks
flutter: 🔍 [DEBUG] Album "Landslide (BOTCASH Remix)" has 1 tracks
flutter: 🔍 [DEBUG] Album "North Star (Remixes)" has 5 tracks
flutter: 🔍 [DEBUG] Album "Harder to Breathe" has 1 tracks
flutter: 🔍 [DEBUG] Album "The Call" has 1 tracks
flutter: 🔍 [DEBUG] Album "North Star" has 1 tracks
flutter: 🔍 [DEBUG] Album "Landslide" has 1 tracks
flutter: 🔍 [DEBUG] Album "Lucky Ones" has 1 tracks
flutter: 🔍 [DEBUG] Album "Mirror" has 1 tracks
flutter: 🔍 [DEBUG] Album "Love Again (Acoustic)" has 1 tracks
flutter: 🔍 [DEBUG] Album "Love Again (Remix)" has 2 tracks
flutter: 🔍 [DEBUG] Album "Dead In The Water" has 1 tracks
flutter: 🔍 [DEBUG] Album "Ready For Love" has 1 tracks
flutter: 🔍 [DEBUG] Album "Love Again" has 1 tracks
flutter: 🔍 [DEBUG] Album "Trust Issues" has 1 tracks
flutter: 🔍 [DEBUG] Album "Love For You" has 1 tracks
flutter: 🔍 [DEBUG] Album "Falling For You" has 1 tracks
flutter: 🔍 [DEBUG] Album "Scared" has 1 tracks
flutter: 🔍 [DEBUG] Album "Me + You (The Remixes)" has 7 tracks
flutter: 🔍 [DEBUG] Album "Me + You (Rogue Remix)" has 1 tracks
flutter: 🔍 [DEBUG] Album "Me + You" has 1 tracks
flutter: 🔍 [DEBUG] Album "Million Days (Acoustic)" has 2 tracks
flutter: 🔍 [DEBUG] Album "I Need Ya" has 1 tracks
flutter: 🔍 [DEBUG] Album "Where It All Began" has 4 tracks
flutter: 🔍 [DEBUG] Album "Broken Glass" has 1 tracks
flutter: 🔍 [DEBUG] Album "Save You" has 1 tracks
flutter: 🔍 [DEBUG] Album "Million Days" has 1 tracks
flutter: 🔍 [DEBUG] Album "Another Life" has 1 tracks
flutter: 🔍 [DEBUG] Album "Don't Turn Back" has 7 tracks
flutter: 🔍 [DEBUG] Album "COPE" has 10 tracks
flutter: 🔍 [DEBUG] Album "Monstercat Instinct Vol. 7" has 50 tracks
flutter: 🔍 [DEBUG] Album "COPE (Remixes)" has 10 tracks
flutter: 🔍 [DEBUG] Album "Elements Remixed" has 4 tracks
flutter: 🔍 [DEBUG] Album "Monstercat Instinct Vol. 6" has 40 tracks
flutter: 🔍 [DEBUG] Album "Monstercat Instinct Vol. 5" has 40 tracks
flutter: ✅ [DEBUG] Successfully retrieved 90 tracks from albums
flutter: ✅ [AI SEARCH LOG] 21:30:37 | "SABAI" [AI_ARTIST_BASED] -> 90 results
flutter: ✅ [DEBUG] Album-based search successful: 90 tracks
flutter: 🗄️ [Artist Cache] Got 100 album tracks + 30 hybrid tracks for "SABAI" (PARALLEL)
flutter: 🗄️ [Artist Cache] Step 3: Deduplicating 130 tracks for "SABAI"...
flutter: 🗄️ [Artist Cache] ✅ Successfully cached 100 unique tracks for "SABAI"
flutter: ⏱️ [Artist Cache] Fetched and cached "SABAI" in 1885ms
flutter: 🎤 [Artist Recs] Cached tracks for "SABAI": 35 tracks (1885ms)
flutter: 🗄️ [Artist Cache] Cache size after artist processing: 4
flutter: 🎵 [Artist Recs] Using Last.fm for similar artist discovery (PARALLELIZED)
flutter: 🚀 [LastFM DEBUG] getSimilarArtists called with: "Ken Carson", limit: 8
flutter: 🔧 [LastFM DEBUG] API key configured: true
flutter: 🔧 [LastFM DEBUG] API key value: fd7597d7...
flutter: 🚀 [LastFM DEBUG] getSimilarArtists called with: "Playboi Carti", limit: 8
flutter: 🔧 [LastFM DEBUG] API key configured: true
flutter: 🔧 [LastFM DEBUG] API key value: fd7597d7...
flutter: 🚀 [LastFM DEBUG] getSimilarArtists called with: "SABAI", limit: 8
flutter: 🔧 [LastFM DEBUG] API key configured: true
flutter: 🔧 [LastFM DEBUG] API key value: fd7597d7...
flutter: 🎵 [LastFM] Getting similar artists for: Ken Carson
flutter: 🌐 [LastFM DEBUG] Making HTTP request to: https://api.bopmaps.com/api/lastfm/?method=artist.getSimilar&artist=Ken+Carson&api_key=fd7597d7a76640941f4db64e38649529&format=json&limit=8
flutter: 🔄 [LastFM] Created new HTTP client
flutter: 📍 Layer state changed: LayerState.transitioning → LayerState.showingClusters
flutter: 📍 State updated: LayerState.transitioning → LayerState.showingClusters
flutter: 📍 ✅ State transition successful: LayerState.showingClusters
flutter: 📍 ✅ Showing cluster layers, hiding individual layers
flutter: 📍 ⚡ Layer switch completed in 4ms (parallelized)
flutter: 📍 🔍 Validating layer visibility: individual=false, cluster=true
flutter: 📍 ✅ Layer visibility validated and corrected if needed
flutter: 📍 ✅ Layer switch completed successfully
flutter: 📍 🔍 Validating current state: LayerState.showingClusters
flutter: 📍 ✅ State validation passed
flutter: 🎵 [LastFM] Getting similar artists for: Playboi Carti
flutter: 🌐 [LastFM DEBUG] Making HTTP request to: https://api.bopmaps.com/api/lastfm/?method=artist.getSimilar&artist=Playboi+Carti&api_key=fd7597d7a76640941f4db64e38649529&format=json&limit=8
flutter: 🎵 [LastFM] Getting similar artists for: SABAI
flutter: 🌐 [LastFM DEBUG] Making HTTP request to: https://api.bopmaps.com/api/lastfm/?method=artist.getSimilar&artist=SABAI&api_key=fd7597d7a76640941f4db64e38649529&format=json&limit=8
flutter: 📍 🔧 Added custom skin for pin 21: pin-skin-21
flutter: 📍 🔧 Updated real pin data for: 21
flutter: 📍 🔧 Processing feature: 29
flutter: 📍 🔧 Added custom skin for pin 3: pin-skin-3
flutter: 📍 🔧 Updated real pin data for: 3
flutter: 📍 🔧 Processing feature: 11
flutter: 📡 [LastFM DEBUG] HTTP response received: 200
flutter: 🎵 [LastFM] Cleaned artist name: "Destroy Lonely" → "Destroy Lonely"
flutter: 🎵 [LastFM] Cleaned artist name: "Playboi Carti" → "Playboi Carti"
flutter: 🎵 [LastFM] Cleaned artist name: "Homixide Gang" → "Homixide Gang"
flutter: 🎵 [LastFM] Cleaned artist name: "OsamaSon" → "OsamaSon"
flutter: 🎵 [LastFM] Cleaned artist name: "Yeat" → "Yeat"
flutter: 🎵 [LastFM] Cleaned artist name: "Lil Uzi Vert" → "Lil Uzi Vert"
flutter: 🎵 [LastFM] Cleaned artist name: "SoFaygo" → "SoFaygo"
flutter: 🎵 [LastFM] Cleaned artist name: "Hardrock" → "Hardrock"
flutter: ✅ [LastFM] Found 8 clean similar artists for: Ken Carson
flutter: 🎯 [Artist Recs] Last.fm found 8 similar artists for: Ken Carson
flutter: 📍 🔧 Added custom skin for pin 3: pin-skin-3
flutter: 📍 🔧 Updated real pin data for: 3
flutter: 📍 🔧 Processing feature: 11
flutter: 📍 🔍 Validating layer visibility: individual=false, cluster=true
flutter: 📡 [LastFM DEBUG] HTTP response received: 200
flutter: 🎵 [LastFM] Cleaned artist name: "William Black" → "William Black"
flutter: 🎵 [LastFM] Cleaned artist name: "yetep" → "yetep"
flutter: 🎵 [LastFM] Cleaned artist name: "Illenium" → "Illenium"
flutter: 🎵 [LastFM] Cleaned artist name: "Gryffin" → "Gryffin"
flutter: 🎵 [LastFM] Cleaned artist name: "Lione" → "Lione"
flutter: 🎵 [LastFM] Cleaned artist name: "Said The Sky" → "Said The Sky"
flutter: 🎵 [LastFM] Cleaned artist name: "BEAUZ" → "BEAUZ"
flutter: 🎵 [LastFM] Cleaned artist name: "Vicetone" → "Vicetone"
flutter: ✅ [LastFM] Found 8 clean similar artists for: SABAI
flutter: 🎯 [Artist Recs] Last.fm found 8 similar artists for: SABAI
flutter: 📍 ✅ Layer visibility validated and corrected if needed
flutter: 📡 [LastFM DEBUG] HTTP response received: 200
flutter: 🎵 [LastFM] Cleaned artist name: "Ken Carson" → "Ken Carson"
flutter: 🎵 [LastFM] Cleaned artist name: "Destroy Lonely" → "Destroy Lonely"
flutter: 🎵 [LastFM] Cleaned artist name: "Lil Uzi Vert" → "Lil Uzi Vert"
flutter: 🎵 [LastFM] Cleaned artist name: "OsamaSon" → "OsamaSon"
flutter: 🎵 [LastFM] Cleaned artist name: "Travi$ Scott" → "Travi$ Scott"
flutter: 🎵 [LastFM] Cleaned artist name: "Young Thug" → "Young Thug"
flutter: 🎵 [LastFM] Cleaned artist name: "Homixide Gang" → "Homixide Gang"
flutter: 🎵 [LastFM] Cleaned artist name: "Yeat" → "Yeat"
flutter: ✅ [LastFM] Found 8 clean similar artists for: Playboi Carti
flutter: 🎯 [Artist Recs] Last.fm found 8 similar artists for: Playboi Carti
flutter: 🎯 [Artist Recs] Last.fm discovery completed in 529ms
flutter: 🎯 [Artist Recs] Found 19 unique similar artists
flutter: 🗄️ [Artist Cache] ENTRY: _getCachedArtistTracks called for "OsamaSon" (limit: 20)
flutter: 🗄️ [Artist Cache] Cache MISS for "OsamaSon", fetching...
flutter: 🗄️ [Artist Cache] Fetching tracks for "OsamaSon"...
flutter: 🗄️ [Artist Cache] Using comprehensive parallel fetching for "OsamaSon"...
flutter: 🔧 [Search Context] Category: artistBased, Genre: null, Context: AI_ARTIST_BASED
flutter: 🔍 [DEBUG] Starting getTracksFromArtistAlbums for: "OsamaSon"
flutter: 🔍 [DEBUG] Step 1: Searching for artist candidates for "OsamaSon"...
flutter: 🎤 [SpotifyService] Searching for artists: "OsamaSon" (limit: 5, offset: 0)
flutter: 🔧 [Search Context] Category: artistBased, Genre: null, Context: AI_ARTIST_BASED
flutter: 🔄 [DEBUG] Starting hybrid search for: "OsamaSon"
flutter: 🔍 [DEBUG] Starting getTracksFromArtistAlbums for: "OsamaSon"
flutter: 🔍 [DEBUG] Step 1: Searching for artist candidates for "OsamaSon"...
flutter: 🎤 [SpotifyService] Searching for artists: "OsamaSon" (limit: 5, offset: 0)
flutter: 🗄️ [Artist Cache] ENTRY: _getCachedArtistTracks called for "Young Thug" (limit: 20)
flutter: 🗄️ [Artist Cache] Cache MISS for "Young Thug", fetching...
flutter: 🗄️ [Artist Cache] Fetching tracks for "Young Thug"...
flutter: 🗄️ [Artist Cache] Using comprehensive parallel fetching for "Young Thug"...
flutter: 🔧 [Search Context] Category: artistBased, Genre: null, Context: AI_ARTIST_BASED
flutter: 🔍 [DEBUG] Starting getTracksFromArtistAlbums for: "Young Thug"
flutter: 🔍 [DEBUG] Step 1: Searching for artist candidates for "Young Thug"...
flutter: 🎤 [SpotifyService] Searching for artists: "Young Thug" (limit: 5, offset: 0)
flutter: 🔧 [Search Context] Category: artistBased, Genre: null, Context: AI_ARTIST_BASED
flutter: 🔄 [DEBUG] Starting hybrid search for: "Young Thug"
flutter: 🔍 [DEBUG] Starting getTracksFromArtistAlbums for: "Young Thug"
flutter: 🔍 [DEBUG] Step 1: Searching for artist candidates for "Young Thug"...
flutter: 🎤 [SpotifyService] Searching for artists: "Young Thug" (limit: 5, offset: 0)
flutter: 🗄️ [Artist Cache] ENTRY: _getCachedArtistTracks called for "William Black" (limit: 20)
flutter: 🗄️ [Artist Cache] Cache MISS for "William Black", fetching...
flutter: 🗄️ [Artist Cache] Fetching tracks for "William Black"...
flutter: 🗄️ [Artist Cache] Using comprehensive parallel fetching for "William Black"...
flutter: 🔧 [Search Context] Category: artistBased, Genre: null, Context: AI_ARTIST_BASED
flutter: 🔍 [DEBUG] Starting getTracksFromArtistAlbums for: "William Black"
flutter: 🔍 [DEBUG] Step 1: Searching for artist candidates for "William Black"...
flutter: 🎤 [SpotifyService] Searching for artists: "William Black" (limit: 5, offset: 0)
flutter: 🔧 [Search Context] Category: artistBased, Genre: null, Context: AI_ARTIST_BASED
flutter: 🔄 [DEBUG] Starting hybrid search for: "William Black"
flutter: 🔍 [DEBUG] Starting getTracksFromArtistAlbums for: "William Black"
flutter: 🔍 [DEBUG] Step 1: Searching for artist candidates for "William Black"...
flutter: 🎤 [SpotifyService] Searching for artists: "William Black" (limit: 5, offset: 0)
flutter: 🗄️ [Artist Cache] ENTRY: _getCachedArtistTracks called for "Ken Carson" (limit: 20)
flutter: 🗄️ [Artist Cache] Cache HIT for "Ken Carson" (100 total tracks)
flutter: ⏱️ [Artist Cache] Cache hit for "Ken Carson" in 0ms
flutter: 🗄️ [Artist Cache] ENTRY: _getCachedArtistTracks called for "Lil Uzi Vert" (limit: 20)
flutter: 🗄️ [Artist Cache] Cache HIT for "Lil Uzi Vert" (100 total tracks)
flutter: ⏱️ [Artist Cache] Cache hit for "Lil Uzi Vert" in 0ms
flutter: 🗄️ [Artist Cache] ENTRY: _getCachedArtistTracks called for "Playboi Carti" (limit: 20)
flutter: 🗄️ [Artist Cache] Cache HIT for "Playboi Carti" (100 total tracks)
flutter: ⏱️ [Artist Cache] Cache hit for "Playboi Carti" in 0ms
flutter: 🎯 [Artist Recs] Similar artist cached search for "Ken Carson" returned 20 tracks (1ms)
flutter: 🎯 [Artist Recs] Similar artist cached search for "Lil Uzi Vert" returned 20 tracks (0ms)
flutter: 🎯 [Artist Recs] Similar artist cached search for "Playboi Carti" returned 20 tracks (0ms)
flutter: 📍 🔧 Added custom skin for pin 6: pin-skin-6
flutter: 📍 🔧 Updated real pin data for: 6
flutter: 📍 🔧 Processing feature: 9
flutter: ❌ [SpotifyService] No access token found in either storage location
flutter: ❌ [SpotifyService] No access token found in either storage location
flutter: ❌ [SpotifyService] No access token found in either storage location
flutter: ❌ [SpotifyService] No access token found in either storage location
flutter: ❌ [SpotifyService] No access token found in either storage location
flutter: ❌ [SpotifyService] No access token found in either storage location
flutter: ✅ [SpotifyService] Found 5 artists for query: "Young Thug"
flutter: 🔍 [DEBUG] Artist search returned 5 candidates
flutter: 🔍 [DEBUG] Evaluating artist candidates:
flutter: 🔍 [DEBUG] - "Young Thug": similarity = 1.000
flutter: 🔍 [DEBUG] - "Future": similarity = 0.000
flutter: 🔍 [DEBUG] - "Young the Giant": similarity = 0.600
flutter: 🔍 [DEBUG] - "Gunna": similarity = 0.167
flutter: 🔍 [DEBUG] - "Bone Thugs-N-Harmony": similarity = 0.231
flutter: ✅ [DEBUG] Best match: "Young Thug" (ID: 50co4Is1HCEo8bhOyUWKpn, similarity: 1.000)
flutter: 🔍 [DEBUG] Step 2: Getting albums for artist ID: 50co4Is1HCEo8bhOyUWKpn
flutter: 🔍 [DEBUG] Include groups: [album, single, appears_on]
flutter: 📀 [SpotifyService] Getting albums for artist: 50co4Is1HCEo8bhOyUWKpn (limit: 50, offset: 0)
flutter: 📍 🔧 Added custom skin for pin 8: pin-skin-8
flutter: 📍 🔧 Updated real pin data for: 8
flutter: 📍 🔧 Processing feature: 1
flutter: ✅ [SpotifyService] Found 5 artists for query: "Young Thug"
flutter: 🔍 [DEBUG] Artist search returned 5 candidates
flutter: 🔍 [DEBUG] Evaluating artist candidates:
flutter: 🔍 [DEBUG] - "Young Thug": similarity = 1.000
flutter: 🔍 [DEBUG] - "Future": similarity = 0.000
flutter: 🔍 [DEBUG] - "Young the Giant": similarity = 0.600
flutter: 🔍 [DEBUG] - "Gunna": similarity = 0.167
flutter: 🔍 [DEBUG] - "Bone Thugs-N-Harmony": similarity = 0.231
flutter: ✅ [DEBUG] Best match: "Young Thug" (ID: 50co4Is1HCEo8bhOyUWKpn, similarity: 1.000)
flutter: 🔍 [DEBUG] Step 2: Getting albums for artist ID: 50co4Is1HCEo8bhOyUWKpn
flutter: 🔍 [DEBUG] Include groups: [album, single, appears_on]
flutter: 📀 [SpotifyService] Getting albums for artist: 50co4Is1HCEo8bhOyUWKpn (limit: 50, offset: 0)
flutter: ✅ [SpotifyService] Found 5 artists for query: "OsamaSon"
flutter: 🔍 [DEBUG] Artist search returned 5 candidates
flutter: 🔍 [DEBUG] Evaluating artist candidates:
flutter: 🔍 [DEBUG] - "OsamaSon": similarity = 1.000
flutter: 🔍 [DEBUG] - "OsamaSon": similarity = 1.000
flutter: 🔍 [DEBUG] - "Che": similarity = 0.000
flutter: 🔍 [DEBUG] - "LUCKI": similarity = 0.000
flutter: 🔍 [DEBUG] - "Lil O": similarity = 0.000
flutter: ✅ [DEBUG] Best match: "OsamaSon" (ID: 0uj6QiPsPfK8ywLC7uwBE1, similarity: 1.000)
flutter: 🔍 [DEBUG] Step 2: Getting albums for artist ID: 0uj6QiPsPfK8ywLC7uwBE1
flutter: 🔍 [DEBUG] Include groups: [album, single, appears_on]
flutter: 📀 [SpotifyService] Getting albums for artist: 0uj6QiPsPfK8ywLC7uwBE1 (limit: 50, offset: 0)
flutter: ✅ [SpotifyService] Found 5 artists for query: "OsamaSon"
flutter: 🔍 [DEBUG] Artist search returned 5 candidates
flutter: 🔍 [DEBUG] Evaluating artist candidates:
flutter: 🔍 [DEBUG] - "OsamaSon": similarity = 1.000
flutter: 🔍 [DEBUG] - "OsamaSon": similarity = 1.000
flutter: 🔍 [DEBUG] - "Che": similarity = 0.000
flutter: 🔍 [DEBUG] - "LUCKI": similarity = 0.000
flutter: 🔍 [DEBUG] - "Lil O": similarity = 0.000
flutter: ✅ [DEBUG] Best match: "OsamaSon" (ID: 0uj6QiPsPfK8ywLC7uwBE1, similarity: 1.000)
flutter: 🔍 [DEBUG] Step 2: Getting albums for artist ID: 0uj6QiPsPfK8ywLC7uwBE1
flutter: 🔍 [DEBUG] Include groups: [album, single, appears_on]
flutter: 📀 [SpotifyService] Getting albums for artist: 0uj6QiPsPfK8ywLC7uwBE1 (limit: 50, offset: 0)
flutter: ❌ [SpotifyService] No access token found in either storage location
flutter: ❌ [SpotifyService] No access token found in either storage location
flutter: ❌ [SpotifyService] No access token found in either storage location
flutter: ❌ [SpotifyService] No access token found in either storage location
flutter: 📀 [SpotifyService] Found 50 albums for artist: 0uj6QiPsPfK8ywLC7uwBE1
flutter: 🔍 [DEBUG] Albums API returned 50 albums
flutter: 🔍 [DEBUG] Step 3: Extracting album IDs...
flutter: 🔍 [DEBUG] Found 50 valid album IDs: [02Mi6lld23cYcBeXbUbo0s, 5Vb4K4Xf4KPFupmhHi5COM, 17DbwNYaf8YjmLXSwiKG3M, 0KLk3N51EO7CG7d9MdQ2XE, 075lV4wdtLwFvIvCUdSYhL, 1gbR6hQTGdJ8PH3BXgmcdg, 1o9z5t0xuvOcOp3VZ4ElXH, 7rMhfh9sfFiaE8ITYlAU9F, 0SAzYkrVlpxaNWrnTCjbYI, 73cibirs94ZVsObZcEdyny, 23mu61TZub1lTik1j31nL2, 1sl7QnYBjvTfYofuhh1KDw, 1u8XbmT230qqv62bHWEdOA, 4PyEWrZgFuXM6gOh7bIbE5, 2rfE72XzujFxGFr3lhUoyk, 2a20WSSB4zjWmZgm3vxrLV, 1tCR1YRSepfHx6CfKQD6nL, 2hY27uqdtFLsjIOtdNWVCy, 34ajV1ZcksUhB5EqPjGY2c, 3fvpsu97u06iPP4q2L60wN, 7E0cntwvy6Qyv1BHB3R5Un, 1I4B2unHObn3Ja5Is5qvzg, 0jRCXk7mbEi0VpsjLmpXPx, 6Y0HAXQJ6wHlML3G5uewG5, 00vceVhL61UxYkitUs7sTd, 57P0e7RHw6eX7KukLMKNP3, 14LdNYarvjOyNW2L2lYizD, 2KwhWfLL6TmJ9QK6sZ190x, 4qwPTACEO9D2AIFVoiUIC3, 335cRtK9wbQYSggPEi4ES1, 58hVvmr8nvO2NOXEBSfLm5, 67A39lP2d2oyDkRukYYm4F, 2mDPUgNe4bXjo7ofYvEbfI, 6VyWxik4npLtHuYKJ66zsa, 7M55Zy84Y3oEvb3DnLf8AX, 5zl023qaeJKFhreXxOELSN, 27vdTR3O1aq9rDlwhMd4wV, 2rihAF4yZfVrFhluOUV0JF, 3trgI0FTdhbfpKCfAnkCn0, 5NN6HNSxYqVxJUbtqYaAJh, 1Bp4brbxY3dXHmU06yyMBi, 62aaaPN5htwNX8KmV9a1Vv, 2NQvAnVUtjB9CsTUbWJzI8, 78FBSD5L1KowUhlioNIlcL, 6TrlRjRmvJqM2wHU80dqAc, 2jFxansMVvn7kE7WJKVPwE, 4GflSYKH3Gpk8xUdvT3iL6, 3R38DAdNXpAD5ZjOVlrjtn, 1GdRLHbCFebB3rtx9M7IUy, 7h96oeiiVLZDfoqojjm1wm]
flutter: 🔍 [DEBUG] Step 4: Fetching album details and tracks...
flutter: 📀 [SpotifyService] Getting 50 albums in batch
flutter: ❌ [SpotifyService] No access token found in either storage location
flutter: 📀 [SpotifyService] Found 50 albums for artist: 50co4Is1HCEo8bhOyUWKpn
flutter: 🔍 [DEBUG] Albums API returned 50 albums
flutter: 🔍 [DEBUG] Step 3: Extracting album IDs...
flutter: 🔍 [DEBUG] Found 50 valid album IDs: [4N6sTUH8VjSMJrMLseaNWz, 3KSeRpEslZsZGRnJHe5nXt, 727Lr7pTn0vMiLlp0VR59W, 4qwGOoWcjICLQO8s0HFonG, 0z2a9VgdVmkr0DInVJUgu6, 6qElaeK7SHsEzb8uV8eG3D, 7IKkHmEk4It4cRdOYanyvW, 3XADnbi4uhYXb7RuSJ7bre, 3ihwKkIMJWmmp1huNH0iWC, 7fZKtzZAsfH0kzeTivu5TG, 24jAbLMFzCTlIv3kQ94HwK, 1bnHPO4dKK7IjvgrtVBcQh, 4OHfmMUq6xwCo8zfQuc9Rf, 5Dwws0oO0fQLDsrwZE7rvW, 187UNqZ7MX3neMYkkevmdm, 51KZKzPd3OQT1r46a55VTt, 7EpUpNUkkEGnaCvkcn1j4H, 06WCaO8MJdfRvqHnhbFOup, 2z4c8M8aVzl7CTobIp36KF, 4sAB9WulPpnAcig7alDGTg, 0ssPpXttlI7w2Z6CXiqWgR, 6JNSNDb71rsuTa74MiUEVF, 0BsMZIueWsJLWng8A7sE8e, 7a3rhn1csJY4CbdqcQsMcy, 4N0QH3uy5K5T3fMtpLIpKq, 56hYCG1iltKiEgSPOeRRYd, 4k4w4MLXdY084qrbMGy9Sg, 17J76IiBYBQBIOyNbmnjaO, 4ycuXCWCg3MjM7GEnNCy3n, 2sOsaBI0pr8uu1RmZDbeMe, 5WAhisFRMfSACOZUyxuWsB, 0DlZyQQHeZpzQlptoYiXLP, 6zq1z4n5e0WBkSNGDuR1co, 5d61Y5uPCU4QvJCX2axHj6, 5J380O739v6VvloJG1Vg5n, 2ymNS2YXhwg2akYRX1aL6A, 2FFGg4JKNXlgddsqNIg08V, 7Hb2ipt4WhYg4khu2deBC2, 76GwLI1nybtKiwJ1maeFIw, 3Ut8zKJlqFVRMqwcAww9bP, 05zSfoTPUX9IFb4AYbBaBz, 4KeMatc07ZBH1Z2kGEDVZ1, 6zNJxFmILwoDTFYJqhQFIU, 1V6HksALLzO5ihpU3YVqJc, 7K0mSkUKYnyrOQ4ko2fl7N, 15a7Yf9rZIKhulN2NEKb3a, 7Gm8THb5g2e7vx8ZhOjWR3, 7z2Kbn2GQ9J1EzoT5MULqq, 5hUFZjxtqYvxm9FGUhHCdE, 4YL3Pkal2a72eAzXCYoCay]
flutter: 🔍 [DEBUG] Step 4: Fetching album details and tracks...
flutter: 📀 [SpotifyService] Getting 50 albums in batch
flutter: ❌ [SpotifyService] No access token found in either storage location
flutter: 📍 🔧 Added custom skin for pin 11: pin-skin-11
flutter: 📍 🔧 Updated real pin data for: 11
flutter: 📍 🔧 Added 5 new features to individual-pins-source memory: 2 → 7
flutter: 📍 ✅ Added 5 pins to individual layer (total: 7)
flutter: 📍 ✅ Processed pin IDs: [31, 22, 5, 3, 11]
flutter: 📍 ✨ Adding incremental glow effects for batch of 5 pins
flutter: 🌟 Added 10 features to glow-polygons-source memory (total: 14)
flutter: 🌟 Added 5 features to border-polygons-source memory (total: 7)
flutter: 📍 🔧 Added custom skin for pin 29: pin-skin-29
flutter: 📍 🔧 Updated real pin data for: 29
flutter: 📍 🔧 Processing feature: 4
flutter: 🌟 Added 5 features to pulse-polygons-source memory (total: 7)
flutter: 🌟 Updated existing glow sources with 10 glow features (accumulated across batches)
flutter: 🌟 Added incremental glow effects for 5 pins
flutter: 📍 ✨ Incremental glow effects added for batch
flutter: 📍 ✅ Parallel layer building completed for batch of 5 pins
flutter: 📍 🔧 Added custom skin for pin 11: pin-skin-11
flutter: 📍 🔧 Updated real pin data for: 11
flutter: 📍 🔧 Filtered 5 duplicate features for individual-pins-source
flutter: 📍 🔧 Added 0 new features to individual-pins-source memory: 7 → 7
flutter: 📍 ✅ Added 5 pins to individual layer (total: 7)
flutter: 📍 ✅ Processed pin IDs: [31, 22, 5, 3, 11]
flutter: 📍 ✨ Adding incremental glow effects for batch of 5 pins
flutter: 🌟 Added 10 features to glow-polygons-source memory (total: 24)
flutter: 🌟 Added 5 features to border-polygons-source memory (total: 12)
flutter: 🌟 Added 5 features to pulse-polygons-source memory (total: 12)
flutter: 📀 [SpotifyService] Found 50 albums for artist: 50co4Is1HCEo8bhOyUWKpn
flutter: 🔍 [DEBUG] Albums API returned 50 albums
flutter: 🔍 [DEBUG] Step 3: Extracting album IDs...
flutter: 🔍 [DEBUG] Found 50 valid album IDs: [4N6sTUH8VjSMJrMLseaNWz, 3KSeRpEslZsZGRnJHe5nXt, 727Lr7pTn0vMiLlp0VR59W, 4qwGOoWcjICLQO8s0HFonG, 0z2a9VgdVmkr0DInVJUgu6, 6qElaeK7SHsEzb8uV8eG3D, 7IKkHmEk4It4cRdOYanyvW, 3XADnbi4uhYXb7RuSJ7bre, 3ihwKkIMJWmmp1huNH0iWC, 7fZKtzZAsfH0kzeTivu5TG, 24jAbLMFzCTlIv3kQ94HwK, 1bnHPO4dKK7IjvgrtVBcQh, 4OHfmMUq6xwCo8zfQuc9Rf, 5Dwws0oO0fQLDsrwZE7rvW, 187UNqZ7MX3neMYkkevmdm, 51KZKzPd3OQT1r46a55VTt, 7EpUpNUkkEGnaCvkcn1j4H, 06WCaO8MJdfRvqHnhbFOup, 2z4c8M8aVzl7CTobIp36KF, 4sAB9WulPpnAcig7alDGTg, 0ssPpXttlI7w2Z6CXiqWgR, 6JNSNDb71rsuTa74MiUEVF, 0BsMZIueWsJLWng8A7sE8e, 7a3rhn1csJY4CbdqcQsMcy, 4N0QH3uy5K5T3fMtpLIpKq, 56hYCG1iltKiEgSPOeRRYd, 4k4w4MLXdY084qrbMGy9Sg, 17J76IiBYBQBIOyNbmnjaO, 4ycuXCWCg3MjM7GEnNCy3n, 2sOsaBI0pr8uu1RmZDbeMe, 5WAhisFRMfSACOZUyxuWsB, 0DlZyQQHeZpzQlptoYiXLP, 6zq1z4n5e0WBkSNGDuR1co, 5d61Y5uPCU4QvJCX2axHj6, 5J380O739v6VvloJG1Vg5n, 2ymNS2YXhwg2akYRX1aL6A, 2FFGg4JKNXlgddsqNIg08V, 7Hb2ipt4WhYg4khu2deBC2, 76GwLI1nybtKiwJ1maeFIw, 3Ut8zKJlqFVRMqwcAww9bP, 05zSfoTPUX9IFb4AYbBaBz, 4KeMatc07ZBH1Z2kGEDVZ1, 6zNJxFmILwoDTFYJqhQFIU, 1V6HksALLzO5ihpU3YVqJc, 7K0mSkUKYnyrOQ4ko2fl7N, 15a7Yf9rZIKhulN2NEKb3a, 7Gm8THb5g2e7vx8ZhOjWR3, 7z2Kbn2GQ9J1EzoT5MULqq, 5hUFZjxtqYvxm9FGUhHCdE, 4YL3Pkal2a72eAzXCYoCay]
flutter: 🔍 [DEBUG] Step 4: Fetching album details and tracks...
flutter: 📀 [SpotifyService] Getting 50 albums in batch
flutter: 📀 [SpotifyService] Found 50 albums for artist: 0uj6QiPsPfK8ywLC7uwBE1
flutter: 🔍 [DEBUG] Albums API returned 50 albums
flutter: 🔍 [DEBUG] Step 3: Extracting album IDs...
flutter: 🔍 [DEBUG] Found 50 valid album IDs: [02Mi6lld23cYcBeXbUbo0s, 5Vb4K4Xf4KPFupmhHi5COM, 17DbwNYaf8YjmLXSwiKG3M, 0KLk3N51EO7CG7d9MdQ2XE, 075lV4wdtLwFvIvCUdSYhL, 1gbR6hQTGdJ8PH3BXgmcdg, 1o9z5t0xuvOcOp3VZ4ElXH, 7rMhfh9sfFiaE8ITYlAU9F, 0SAzYkrVlpxaNWrnTCjbYI, 73cibirs94ZVsObZcEdyny, 23mu61TZub1lTik1j31nL2, 1sl7QnYBjvTfYofuhh1KDw, 1u8XbmT230qqv62bHWEdOA, 4PyEWrZgFuXM6gOh7bIbE5, 2rfE72XzujFxGFr3lhUoyk, 2a20WSSB4zjWmZgm3vxrLV, 1tCR1YRSepfHx6CfKQD6nL, 2hY27uqdtFLsjIOtdNWVCy, 34ajV1ZcksUhB5EqPjGY2c, 3fvpsu97u06iPP4q2L60wN, 7E0cntwvy6Qyv1BHB3R5Un, 1I4B2unHObn3Ja5Is5qvzg, 0jRCXk7mbEi0VpsjLmpXPx, 6Y0HAXQJ6wHlML3G5uewG5, 00vceVhL61UxYkitUs7sTd, 57P0e7RHw6eX7KukLMKNP3, 14LdNYarvjOyNW2L2lYizD, 2KwhWfLL6TmJ9QK6sZ190x, 4qwPTACEO9D2AIFVoiUIC3, 335cRtK9wbQYSggPEi4ES1, 58hVvmr8nvO2NOXEBSfLm5, 67A39lP2d2oyDkRukYYm4F, 2mDPUgNe4bXjo7ofYvEbfI, 6VyWxik4npLtHuYKJ66zsa, 7M55Zy84Y3oEvb3DnLf8AX, 5zl023qaeJKFhreXxOELSN, 27vdTR3O1aq9rDlwhMd4wV, 2rihAF4yZfVrFhluOUV0JF, 3trgI0FTdhbfpKCfAnkCn0, 5NN6HNSxYqVxJUbtqYaAJh, 1Bp4brbxY3dXHmU06yyMBi, 62aaaPN5htwNX8KmV9a1Vv, 2NQvAnVUtjB9CsTUbWJzI8, 78FBSD5L1KowUhlioNIlcL, 6TrlRjRmvJqM2wHU80dqAc, 2jFxansMVvn7kE7WJKVPwE, 4GflSYKH3Gpk8xUdvT3iL6, 3R38DAdNXpAD5ZjOVlrjtn, 1GdRLHbCFebB3rtx9M7IUy, 7h96oeiiVLZDfoqojjm1wm]
flutter: 🔍 [DEBUG] Step 4: Fetching album details and tracks...
flutter: 📀 [SpotifyService] Getting 50 albums in batch
flutter: 🌟 Updated existing glow sources with 10 glow features (accumulated across batches)
flutter: 🌟 Added incremental glow effects for 5 pins
flutter: 📍 ✨ Incremental glow effects added for batch
flutter: 📍 ✅ Parallel layer building completed for batch of 5 pins
flutter: 📍 ✨ Incremental display: Refreshed layer visibility after batch (showing clusters)
flutter: ❌ [SpotifyService] No access token found in either storage location
flutter: 📍 ✨ Incremental display: Refreshed layer visibility after batch (showing clusters)
flutter: ❌ [SpotifyService] No access token found in either storage location
flutter: ✅ [SpotifyService] Found 5 artists for query: "William Black"
flutter: 🔍 [DEBUG] Artist search returned 5 candidates
flutter: 🔍 [DEBUG] Evaluating artist candidates:
flutter: 🔍 [DEBUG] - "William Black": similarity = 1.000
flutter: 🔍 [DEBUG] - "William Black": similarity = 1.000
flutter: 🔍 [DEBUG] - "William Clark Green": similarity = 0.519
flutter: 🔍 [DEBUG] - "Jack Black": similarity = 0.421
flutter: 🔍 [DEBUG] - "Saul Williams": similarity = 0.545
flutter: ✅ [DEBUG] Best match: "William Black" (ID: 7d5SfGXKpgS3JK8BFIq59h, similarity: 1.000)
flutter: 🔍 [DEBUG] Step 2: Getting albums for artist ID: 7d5SfGXKpgS3JK8BFIq59h
flutter: 🔍 [DEBUG] Include groups: [album, single, appears_on]
flutter: 📀 [SpotifyService] Getting albums for artist: 7d5SfGXKpgS3JK8BFIq59h (limit: 50, offset: 0)
flutter: ✅ [SpotifyService] Found 5 artists for query: "William Black"
flutter: 🔍 [DEBUG] Artist search returned 5 candidates
flutter: 🔍 [DEBUG] Evaluating artist candidates:
flutter: 🔍 [DEBUG] - "William Black": similarity = 1.000
flutter: 🔍 [DEBUG] - "William Black": similarity = 1.000
flutter: 🔍 [DEBUG] - "William Clark Green": similarity = 0.519
flutter: 🔍 [DEBUG] - "Jack Black": similarity = 0.421
flutter: 🔍 [DEBUG] - "Saul Williams": similarity = 0.545
flutter: ✅ [DEBUG] Best match: "William Black" (ID: 7d5SfGXKpgS3JK8BFIq59h, similarity: 1.000)
flutter: 🔍 [DEBUG] Step 2: Getting albums for artist ID: 7d5SfGXKpgS3JK8BFIq59h
flutter: 🔍 [DEBUG] Include groups: [album, single, appears_on]
flutter: 📀 [SpotifyService] Getting albums for artist: 7d5SfGXKpgS3JK8BFIq59h (limit: 50, offset: 0)
flutter: ❌ [SpotifyService] No access token found in either storage location
flutter: ❌ [SpotifyService] No access token found in either storage location
flutter: ❌ [SpotifyService] No access token found in either storage location
flutter: ❌ [SpotifyService] No access token found in either storage location
flutter: ❌ [SpotifyService] No access token found in either storage location
flutter: 📀 [SpotifyService] Found 50 albums for artist: 7d5SfGXKpgS3JK8BFIq59h
flutter: 🔍 [DEBUG] Albums API returned 50 albums
flutter: 🔍 [DEBUG] Step 3: Extracting album IDs...
flutter: 🔍 [DEBUG] Found 50 valid album IDs: [4FOfIstLTkuZLYc3AJCaG1, 64ZcjP0jb06XnpDf3VxXwm, 4eMWDhANOqrYZVw8hO9pNh, 2Mzaihfp9i7m6UO5HSRtHb, 6ZEOvlvcojU1AWhb35I11T, 1Odo1jOh1HUl7g0fcz5baw, 4DxsVIzj7Fo52tiMngJkxO, 0prOosQFge1HKryicdbaZ1, 45pxPQmVRnzeOfgLKZMSPH, 2epDNme9aR6mueN6NQ9Mhx, 1z9aF1zGu0L4qA30Cezvmu, 5dccx73DnyEmbw2FdQ7pqr, 3LLqzHqmnu04kBA5y4Umhy, 1G72BOFuFqlonKRSvzxHyK, 06lxDpivRf6gtBBFLTlAUp, 38RdF3YX7jMghQqhjAvXi4, 4dno18wmZxZldvHiApenIa, 4tFVMKFJs5PXf4Nk01Yrmn, 0MaD5lTQ32Np46N7qTmR4f, 4e2QxrBv9chCtzQRIWCzN8, 4BNrohSLaSA4ifKPJyzUts, 3r3rcik5DCG8NgULSco5bX, 6sbh8lCL3pBkNYpx05qFTZ, 6MlwwEzsLHkt4cfzQDfMpO, 1U4UkhcMbsrTv3XjajyNOV, 4tyu7bnUQQE3zgnGOOAzOW, 2OrEvZTNIChKz6LQlXEZ3G, 07QJ6nCnRk7lOswVgLAE2r, 7ghIKFtH1hzdB3dLXgRcOf, 1FUaAK23gwHBdJ5dsSLdiL, 6xs2a1obuebXACO3IDc55C, 2XdpVHuASuS8tusg7qpbvS, 7HoJUJ98AOWoqDifxxZO4f, 0pAk7Nh1tcS4NvdkDHmbtG, 0ihdLjQ6b9QzmrfA9tbkZX, 6TcSd0dM3kXzNE9XtazBjI, 5IGbVBz7v9f90qWXxe1LJ6, 32q03z1Ex52fOjVn31aMs1, 5lu2U7sORKCZiJqvj8yTNg, 7637nL7aRD1fZcu5IOtq04, 0wzFfCgkhUXaoAxj5qOBkY, 0qqCC84kBtkvN3FCYMgaVg, 30uFv44hmEKmf91yabK0cU, 1PQ6YhtbVeWIlWw6vbmUnj, 1NTbAtJXFPApsdWfhIvJ4Q, 7jKKNjFSpqYkJibB9dzljd, 0hKOffv9vWtya3CkEXrVhO, 5Ru6DMKlZ5p7oS5EidhsSf, 0MA24Cu5boP9OZFMck9sJg, 443j0dvN3p665BQD9OOQnR]
flutter: 🔍 [DEBUG] Step 4: Fetching album details and tracks...
flutter: 📀 [SpotifyService] Getting 50 albums in batch
flutter: ❌ [SpotifyService] No access token found in either storage location
flutter: 📀 [SpotifyService] Found 50 albums for artist: 7d5SfGXKpgS3JK8BFIq59h
flutter: 🔍 [DEBUG] Albums API returned 50 albums
flutter: 🔍 [DEBUG] Step 3: Extracting album IDs...
flutter: 🔍 [DEBUG] Found 50 valid album IDs: [4FOfIstLTkuZLYc3AJCaG1, 64ZcjP0jb06XnpDf3VxXwm, 4eMWDhANOqrYZVw8hO9pNh, 2Mzaihfp9i7m6UO5HSRtHb, 6ZEOvlvcojU1AWhb35I11T, 1Odo1jOh1HUl7g0fcz5baw, 4DxsVIzj7Fo52tiMngJkxO, 0prOosQFge1HKryicdbaZ1, 45pxPQmVRnzeOfgLKZMSPH, 2epDNme9aR6mueN6NQ9Mhx, 1z9aF1zGu0L4qA30Cezvmu, 5dccx73DnyEmbw2FdQ7pqr, 3LLqzHqmnu04kBA5y4Umhy, 1G72BOFuFqlonKRSvzxHyK, 06lxDpivRf6gtBBFLTlAUp, 38RdF3YX7jMghQqhjAvXi4, 4dno18wmZxZldvHiApenIa, 4tFVMKFJs5PXf4Nk01Yrmn, 0MaD5lTQ32Np46N7qTmR4f, 4e2QxrBv9chCtzQRIWCzN8, 4BNrohSLaSA4ifKPJyzUts, 3r3rcik5DCG8NgULSco5bX, 6sbh8lCL3pBkNYpx05qFTZ, 6MlwwEzsLHkt4cfzQDfMpO, 1U4UkhcMbsrTv3XjajyNOV, 4tyu7bnUQQE3zgnGOOAzOW, 2OrEvZTNIChKz6LQlXEZ3G, 07QJ6nCnRk7lOswVgLAE2r, 7ghIKFtH1hzdB3dLXgRcOf, 1FUaAK23gwHBdJ5dsSLdiL, 6xs2a1obuebXACO3IDc55C, 2XdpVHuASuS8tusg7qpbvS, 7HoJUJ98AOWoqDifxxZO4f, 0pAk7Nh1tcS4NvdkDHmbtG, 0ihdLjQ6b9QzmrfA9tbkZX, 6TcSd0dM3kXzNE9XtazBjI, 5IGbVBz7v9f90qWXxe1LJ6, 32q03z1Ex52fOjVn31aMs1, 5lu2U7sORKCZiJqvj8yTNg, 7637nL7aRD1fZcu5IOtq04, 0wzFfCgkhUXaoAxj5qOBkY, 0qqCC84kBtkvN3FCYMgaVg, 30uFv44hmEKmf91yabK0cU, 1PQ6YhtbVeWIlWw6vbmUnj, 1NTbAtJXFPApsdWfhIvJ4Q, 7jKKNjFSpqYkJibB9dzljd, 0hKOffv9vWtya3CkEXrVhO, 5Ru6DMKlZ5p7oS5EidhsSf, 0MA24Cu5boP9OZFMck9sJg, 443j0dvN3p665BQD9OOQnR]
flutter: 🔍 [DEBUG] Step 4: Fetching album details and tracks...
flutter: 📀 [SpotifyService] Getting 50 albums in batch
flutter: ❌ [SpotifyService] No access token found in either storage location
flutter: ❌ [SpotifyService] No access token found in either storage location
flutter: ❌ [SpotifyService] No access token found in either storage location
flutter: ❌ [SpotifyService] No access token found in either storage location
flutter: ❌ [SpotifyService] No access token found in either storage location
flutter: 📀 [SpotifyService] Retrieved 50 albums with tracks
flutter: 🔍 [DEBUG] Multiple albums API returned 50 albums with tracks
flutter: 🔍 [DEBUG] Album "Jump Out (Bonus Tracks)" has 20 tracks
flutter: 🔍 [DEBUG] Album "Jump Out" has 18 tracks
flutter: 🔍 [DEBUG] Album "3vil Reflection" has 9 tracks
flutter: 🔍 [DEBUG] Album "Flex Musix (FLXTRA)" has 23 tracks
flutter: 🔍 [DEBUG] Album "Flex Musix" has 17 tracks
flutter: 🔍 [DEBUG] Album "Osama Season" has 12 tracks
flutter: 🔍 [DEBUG] Album "DEMON HOME" has 1 tracks
flutter: 🔍 [DEBUG] Album "grails" has 3 tracks
flutter: 🔍 [DEBUG] Album "The Whole World Is Free" has 1 tracks
flutter: 🔍 [DEBUG] Album "just score it" has 1 tracks
flutter: 🔍 [DEBUG] Album "ik what you did last summer" has 1 tracks
flutter: 🔍 [DEBUG] Album "popstar" has 1 tracks
flutter: 🔍 [DEBUG] Album "withdrawals" has 1 tracks
flutter: 🔍 [DEBUG] Album "Troops" has 1 tracks
flutter: 🔍 [DEBUG] Album "cts-v" has 1 tracks
flutter: 🔍 [DEBUG] Album "slime krew" has 1 tracks
flutter: 🔍 [DEBUG] Album "draco" has 1 tracks
flutter: 🔍 [DEBUG] Album "back from dead" has 1 tracks
flutter: 🔍 [DEBUG] Album "garfield" has 1 tracks
flutter: 🔍 [DEBUG] Album "in dat cut" has 1 tracks
flutter: 🔍 [DEBUG] Album "frontrow" has 1 tracks
flutter: 🔍 [DEBUG] Album "tony" has 1 tracks
flutter: 🔍 [DEBUG] Album "rehab" has 1 tracks
flutter: 🔍 [DEBUG] Album "on me" has 1 tracks
flutter: 🔍 [DEBUG] Album "jugg in my sleep" has 1 tracks
flutter: 🔍 [DEBUG] Album "catch em" has 1 tracks
flutter: 🔍 [DEBUG] Album "DEFEAT" has 1 tracks
flutter: 🔍 [DEBUG] Album "gotohell" has 1 tracks
flutter: 🔍 [DEBUG] Album "girl of my dreams" has 1 tracks
flutter: 🔍 [DEBUG] Album "MeVsWorld" has 1 tracks
flutter: 🔍 [DEBUG] Album "High as shit" has 1 tracks
flutter: 🔍 [DEBUG] Album "REST IN BASS" has 18 tracks
flutter: 🔍 [DEBUG] Album "War Ready" has 18 tracks
flutter: 🔍 [DEBUG] Album "After TTBR" has 26 tracks
flutter: 🔍 [DEBUG] Album "GTA9" has 18 tracks
flutter: 🔍 [DEBUG] Album "Diamonds in My Face" has 1 tracks
flutter: 🔍 [DEBUG] Album "my name is mblock" has 10 tracks
flutter: 🔍 [DEBUG] Album "Drifting Away" has 6 tracks
flutter: 🔍 [DEBUG] Album "baby evil" has 5 tracks
flutter: 🔍 [DEBUG] Album "Slimey" has 1 tracks
flutter: 🔍 [DEBUG] Album "Exodus" has 15 tracks
flutter: 🔍 [DEBUG] Album "Shawty Get Wimmie" has 1 tracks
flutter: 🔍 [DEBUG] Album "Take My Life" has 1 tracks
flutter: 🔍 [DEBUG] Album "Whatchacalit" has 1 tracks
flutter: 🔍 [DEBUG] Album "Pro" has 1 tracks
flutter: 🔍 [DEBUG] Album "My Slatt" has 2 tracks
flutter: 🔍 [DEBUG] Album "send dat drop" has 1 tracks
flutter: 🔍 [DEBUG] Album "5AVAGE" has 3 tracks
flutter: 🔍 [DEBUG] Album "The zoo." has 11 tracks
flutter: 🔍 [DEBUG] Album "truth or dare" has 4 tracks
flutter: ✅ [DEBUG] Successfully retrieved 100 tracks from albums
flutter: ✅ [AI SEARCH LOG] 21:30:38 | "OsamaSon" [AI_ARTIST_BASED] -> 100 results
flutter: ❌ [SpotifyService] No access token found in either storage location
flutter: 📀 [SpotifyService] Retrieved 50 albums with tracks
flutter: 🔍 [DEBUG] Multiple albums API returned 50 albums with tracks
flutter: 🔍 [DEBUG] Album "Slime Season 2" has 22 tracks
flutter: 🔍 [DEBUG] Album "Slime Season" has 18 tracks
flutter: 🔍 [DEBUG] Album "Slime Season 2" has 22 tracks
flutter: 🔍 [DEBUG] Album "Slime Season" has 18 tracks
flutter: 🔍 [DEBUG] Album "BUSINESS IS BUSINESS (Metro's Version)" has 17 tracks
flutter: 🔍 [DEBUG] Album "BUSINESS IS BUSINESS" has 15 tracks
flutter: 🔍 [DEBUG] Album "Punk" has 20 tracks
flutter: 🔍 [DEBUG] Album "Slime Language 2 (Deluxe)" has 31 tracks
flutter: 🔍 [DEBUG] Album "Slime Language 2" has 23 tracks
flutter: 🔍 [DEBUG] Album "Slime & B" has 13 tracks
flutter: 🔍 [DEBUG] Album "So Much Fun (Deluxe)" has 24 tracks
flutter: 🔍 [DEBUG] Album "So Much Fun" has 19 tracks
flutter: 🔍 [DEBUG] Album "Slime Language" has 15 tracks
flutter: 🔍 [DEBUG] Album "Slime Season 4" has 12 tracks
flutter: 🔍 [DEBUG] Album "SUPER SLIMEY" has 13 tracks
flutter: 🔍 [DEBUG] Album "Beautiful Thugger Girls" has 14 tracks
flutter: 🔍 [DEBUG] Album "JEFFERY" has 10 tracks
flutter: 🔍 [DEBUG] Album "TRU COLORS" has 33 tracks
flutter: 🔍 [DEBUG] Album "Slime Season 3" has 8 tracks
flutter: 🔍 [DEBUG] Album "I'm Up" has 9 tracks
flutter: 🔍 [DEBUG] Album "1017 Thug" has 20 tracks
flutter: 🔍 [DEBUG] Album "The Purple Album" has 12 tracks
flutter: 🔍 [DEBUG] Album "Barter 6" has 13 tracks
flutter: 🔍 [DEBUG] Album "Brick Sqaud" has 14 tracks
flutter: 🔍 [DEBUG] Album "1017 Thug 3 The Finale" has 13 tracks
flutter: 🔍 [DEBUG] Album "Young Thugga Mane La Flare" has 13 tracks
flutter: 🔍 [DEBUG] Album "I Came from Nothing 2" has 22 tracks
flutter: 🔍 [DEBUG] Album "Money On Money (feat. Future)" has 1 tracks
flutter: 🔍 [DEBUG] Album "Dum, Dumb, and Dumber (with Young Thug & Future)" has 1 tracks
flutter: 🔍 [DEBUG] Album "Dope Boy" has 1 tracks
flutter: 🔍 [DEBUG] Album "Lightyears (with Young Thug)" has 1 tracks
flutter: 🔍 [DEBUG] Album "Norbit" has 1 tracks
flutter: 🔍 [DEBUG] Album "RIVER" has 1 tracks
flutter: 🔍 [DEBUG] Album "Twisting Our Fingers" has 1 tracks
flutter: 🔍 [DEBUG] Album "Bless" has 1 tracks
flutter: 🔍 [DEBUG] Album "Poetry" has 1 tracks
flutter: 🔍 [DEBUG] Album "KISSES MAKE SURE" has 1 tracks
flutter: 🔍 [DEBUG] Album "pop ur shit" has 1 tracks
flutter: 🔍 [DEBUG] Album "OK (feat. Young Thug)" has 1 tracks
flutter: 🔍 [DEBUG] Album "From A Man" has 1 tracks
flutter: 🔍 [DEBUG] Album "My wrist (with Young Thug)" has 1 tracks
flutter: 🔍 [DEBUG] Album "Baby Don't Cry (feat. Young Thug)" has 1 tracks
flutter: 🔍 [DEBUG] Album "RUN" has 1 tracks
flutter: 🔍 [DEBUG] Album "Potion (with Dua Lipa & Young Thug)" has 1 tracks
flutter: 🔍 [DEBUG] Album "Tick Tock" has 1 tracks
flutter: 🔍 [DEBUG] Album "Better Believe" has 1 tracks
flutter: 🔍 [DEBUG] Album "PradadaBang" has 1 tracks
flutter: 🔍 [DEBUG] Album "That Go! (feat. T-Shyne)" has 1 tracks
flutter: 🔍 [DEBUG] Album "Bad Boy (with Young Thug)" has 1 tracks
flutter: 🔍 [DEBUG] Album "Take It To Trial (feat. Yak Gotti)" has 1 tracks
flutter: ✅ [DEBUG] Successfully retrieved 100 tracks from albums
flutter: ✅ [AI SEARCH LOG] 21:30:38 | "Young Thug" [AI_ARTIST_BASED] -> 100 results
flutter: ❌ [SpotifyService] No access token found in either storage location
flutter: ❌ [SpotifyService] No access token found in either storage location
flutter: 📀 [SpotifyService] Retrieved 50 albums with tracks
flutter: 🔍 [DEBUG] Multiple albums API returned 50 albums with tracks
flutter: 🔍 [DEBUG] Album "Jump Out (Bonus Tracks)" has 20 tracks
flutter: 🔍 [DEBUG] Album "Jump Out" has 18 tracks
flutter: 🔍 [DEBUG] Album "3vil Reflection" has 9 tracks
flutter: 🔍 [DEBUG] Album "Flex Musix (FLXTRA)" has 23 tracks
flutter: 🔍 [DEBUG] Album "Flex Musix" has 17 tracks
flutter: 🔍 [DEBUG] Album "Osama Season" has 12 tracks
flutter: 🔍 [DEBUG] Album "DEMON HOME" has 1 tracks
flutter: 🔍 [DEBUG] Album "grails" has 3 tracks
flutter: 🔍 [DEBUG] Album "The Whole World Is Free" has 1 tracks
flutter: 🔍 [DEBUG] Album "just score it" has 1 tracks
flutter: 🔍 [DEBUG] Album "ik what you did last summer" has 1 tracks
flutter: 🔍 [DEBUG] Album "popstar" has 1 tracks
flutter: 🔍 [DEBUG] Album "withdrawals" has 1 tracks
flutter: 🔍 [DEBUG] Album "Troops" has 1 tracks
flutter: 🔍 [DEBUG] Album "cts-v" has 1 tracks
flutter: 🔍 [DEBUG] Album "slime krew" has 1 tracks
flutter: 🔍 [DEBUG] Album "draco" has 1 tracks
flutter: 🔍 [DEBUG] Album "back from dead" has 1 tracks
flutter: 🔍 [DEBUG] Album "garfield" has 1 tracks
flutter: 🔍 [DEBUG] Album "in dat cut" has 1 tracks
flutter: 🔍 [DEBUG] Album "frontrow" has 1 tracks
flutter: 🔍 [DEBUG] Album "tony" has 1 tracks
flutter: 🔍 [DEBUG] Album "rehab" has 1 tracks
flutter: 🔍 [DEBUG] Album "on me" has 1 tracks
flutter: 🔍 [DEBUG] Album "jugg in my sleep" has 1 tracks
flutter: 🔍 [DEBUG] Album "catch em" has 1 tracks
flutter: 🔍 [DEBUG] Album "DEFEAT" has 1 tracks
flutter: 🔍 [DEBUG] Album "gotohell" has 1 tracks
flutter: 🔍 [DEBUG] Album "girl of my dreams" has 1 tracks
flutter: 🔍 [DEBUG] Album "MeVsWorld" has 1 tracks
flutter: 🔍 [DEBUG] Album "High as shit" has 1 tracks
flutter: 🔍 [DEBUG] Album "REST IN BASS" has 18 tracks
flutter: 🔍 [DEBUG] Album "War Ready" has 18 tracks
flutter: 🔍 [DEBUG] Album "After TTBR" has 26 tracks
flutter: 🔍 [DEBUG] Album "GTA9" has 18 tracks
flutter: 🔍 [DEBUG] Album "Diamonds in My Face" has 1 tracks
flutter: 🔍 [DEBUG] Album "my name is mblock" has 10 tracks
flutter: 🔍 [DEBUG] Album "Drifting Away" has 6 tracks
flutter: 🔍 [DEBUG] Album "baby evil" has 5 tracks
flutter: 🔍 [DEBUG] Album "Slimey" has 1 tracks
flutter: 🔍 [DEBUG] Album "Exodus" has 15 tracks
flutter: 🔍 [DEBUG] Album "Shawty Get Wimmie" has 1 tracks
flutter: 🔍 [DEBUG] Album "Take My Life" has 1 tracks
flutter: 🔍 [DEBUG] Album "Whatchacalit" has 1 tracks
flutter: 🔍 [DEBUG] Album "Pro" has 1 tracks
flutter: 🔍 [DEBUG] Album "My Slatt" has 2 tracks
flutter: 🔍 [DEBUG] Album "send dat drop" has 1 tracks
flutter: 🔍 [DEBUG] Album "5AVAGE" has 3 tracks
flutter: 🔍 [DEBUG] Album "The zoo." has 11 tracks
flutter: 🔍 [DEBUG] Album "truth or dare" has 4 tracks
flutter: ✅ [DEBUG] Successfully retrieved 90 tracks from albums
flutter: ✅ [AI SEARCH LOG] 21:30:38 | "OsamaSon" [AI_ARTIST_BASED] -> 90 results
flutter: ✅ [DEBUG] Album-based search successful: 90 tracks
flutter: 🗄️ [Artist Cache] Got 100 album tracks + 30 hybrid tracks for "OsamaSon" (PARALLEL)
flutter: 🗄️ [Artist Cache] Step 3: Deduplicating 130 tracks for "OsamaSon"...
flutter: 🗄️ [Artist Cache] ✅ Successfully cached 100 unique tracks for "OsamaSon"
flutter: ⏱️ [Artist Cache] Fetched and cached "OsamaSon" in 1080ms
flutter: 🎯 [Artist Recs] Similar artist cached search for "OsamaSon" returned 20 tracks (1080ms)
flutter: 📀 [SpotifyService] Retrieved 50 albums with tracks
flutter: 🔍 [DEBUG] Multiple albums API returned 50 albums with tracks
flutter: 🔍 [DEBUG] Album "Slime Season 2" has 22 tracks
flutter: 🔍 [DEBUG] Album "Slime Season" has 18 tracks
flutter: 🔍 [DEBUG] Album "Slime Season 2" has 22 tracks
flutter: 🔍 [DEBUG] Album "Slime Season" has 18 tracks
flutter: 🔍 [DEBUG] Album "BUSINESS IS BUSINESS (Metro's Version)" has 17 tracks
flutter: 🔍 [DEBUG] Album "BUSINESS IS BUSINESS" has 15 tracks
flutter: 🔍 [DEBUG] Album "Punk" has 20 tracks
flutter: 🔍 [DEBUG] Album "Slime Language 2 (Deluxe)" has 31 tracks
flutter: 🔍 [DEBUG] Album "Slime Language 2" has 23 tracks
flutter: 🔍 [DEBUG] Album "Slime & B" has 13 tracks
flutter: 🔍 [DEBUG] Album "So Much Fun (Deluxe)" has 24 tracks
flutter: 🔍 [DEBUG] Album "So Much Fun" has 19 tracks
flutter: 🔍 [DEBUG] Album "Slime Language" has 15 tracks
flutter: 🔍 [DEBUG] Album "Slime Season 4" has 12 tracks
flutter: 🔍 [DEBUG] Album "SUPER SLIMEY" has 13 tracks
flutter: 🔍 [DEBUG] Album "Beautiful Thugger Girls" has 14 tracks
flutter: 🔍 [DEBUG] Album "JEFFERY" has 10 tracks
flutter: 🔍 [DEBUG] Album "TRU COLORS" has 33 tracks
flutter: 🔍 [DEBUG] Album "Slime Season 3" has 8 tracks
flutter: 🔍 [DEBUG] Album "I'm Up" has 9 tracks
flutter: 🔍 [DEBUG] Album "1017 Thug" has 20 tracks
flutter: 🔍 [DEBUG] Album "The Purple Album" has 12 tracks
flutter: 🔍 [DEBUG] Album "Barter 6" has 13 tracks
flutter: 🔍 [DEBUG] Album "Brick Sqaud" has 14 tracks
flutter: 🔍 [DEBUG] Album "1017 Thug 3 The Finale" has 13 tracks
flutter: 🔍 [DEBUG] Album "Young Thugga Mane La Flare" has 13 tracks
flutter: 🔍 [DEBUG] Album "I Came from Nothing 2" has 22 tracks
flutter: 🔍 [DEBUG] Album "Money On Money (feat. Future)" has 1 tracks
flutter: 🔍 [DEBUG] Album "Dum, Dumb, and Dumber (with Young Thug & Future)" has 1 tracks
flutter: 🔍 [DEBUG] Album "Dope Boy" has 1 tracks
flutter: 🔍 [DEBUG] Album "Lightyears (with Young Thug)" has 1 tracks
flutter: 🔍 [DEBUG] Album "Norbit" has 1 tracks
flutter: 🔍 [DEBUG] Album "RIVER" has 1 tracks
flutter: 🔍 [DEBUG] Album "Twisting Our Fingers" has 1 tracks
flutter: 🔍 [DEBUG] Album "Bless" has 1 tracks
flutter: 🔍 [DEBUG] Album "Poetry" has 1 tracks
flutter: 🔍 [DEBUG] Album "KISSES MAKE SURE" has 1 tracks
flutter: 🔍 [DEBUG] Album "pop ur shit" has 1 tracks
flutter: 🔍 [DEBUG] Album "OK (feat. Young Thug)" has 1 tracks
flutter: 🔍 [DEBUG] Album "From A Man" has 1 tracks
flutter: 🔍 [DEBUG] Album "My wrist (with Young Thug)" has 1 tracks
flutter: 🔍 [DEBUG] Album "Baby Don't Cry (feat. Young Thug)" has 1 tracks
flutter: 🔍 [DEBUG] Album "RUN" has 1 tracks
flutter: 🔍 [DEBUG] Album "Potion (with Dua Lipa & Young Thug)" has 1 tracks
flutter: 🔍 [DEBUG] Album "Tick Tock" has 1 tracks
flutter: 🔍 [DEBUG] Album "Better Believe" has 1 tracks
flutter: 🔍 [DEBUG] Album "PradadaBang" has 1 tracks
flutter: 🔍 [DEBUG] Album "That Go! (feat. T-Shyne)" has 1 tracks
flutter: 🔍 [DEBUG] Album "Bad Boy (with Young Thug)" has 1 tracks
flutter: 🔍 [DEBUG] Album "Take It To Trial (feat. Yak Gotti)" has 1 tracks
flutter: ✅ [DEBUG] Successfully retrieved 90 tracks from albums
flutter: ✅ [AI SEARCH LOG] 21:30:38 | "Young Thug" [AI_ARTIST_BASED] -> 90 results
flutter: ✅ [DEBUG] Album-based search successful: 90 tracks
flutter: 🗄️ [Artist Cache] Got 100 album tracks + 30 hybrid tracks for "Young Thug" (PARALLEL)
flutter: 🗄️ [Artist Cache] Step 3: Deduplicating 130 tracks for "Young Thug"...
flutter: 🗄️ [Artist Cache] ✅ Successfully cached 100 unique tracks for "Young Thug"
flutter: ⏱️ [Artist Cache] Fetched and cached "Young Thug" in 1153ms
flutter: 🎯 [Artist Recs] Similar artist cached search for "Young Thug" returned 20 tracks (1153ms)
flutter: 📍 🔧 Added custom skin for pin 9: pin-skin-9
flutter: 📍 🔧 Updated real pin data for: 9
flutter: 📍 🔧 Processing feature: 28
flutter: ❌ [SpotifyService] No access token found in either storage location
flutter: ❌ [SpotifyService] No access token found in either storage location
flutter: 📀 [SpotifyService] Retrieved 50 albums with tracks
flutter: 🔍 [DEBUG] Multiple albums API returned 50 albums with tracks
flutter: 🔍 [DEBUG] Album "The Nature of Hope Remixes" has 13 tracks
flutter: 🔍 [DEBUG] Album "The Nature of Hope" has 14 tracks
flutter: 🔍 [DEBUG] Album "Pieces (Remixes)" has 13 tracks
flutter: 🔍 [DEBUG] Album "Pieces" has 13 tracks
flutter: 🔍 [DEBUG] Album "Pages (The Remixes)" has 8 tracks
flutter: 🔍 [DEBUG] Album "Pages" has 10 tracks
flutter: 🔍 [DEBUG] Album "Nightmare" has 3 tracks
flutter: 🔍 [DEBUG] Album "Waiting On This Day Forever" has 2 tracks
flutter: 🔍 [DEBUG] Album "Save Me" has 1 tracks
flutter: 🔍 [DEBUG] Album "Wish I Could Forget (William Black Remix)" has 1 tracks
flutter: 🔍 [DEBUG] Album "Different Shit" has 1 tracks
flutter: 🔍 [DEBUG] Album "Collide" has 1 tracks
flutter: 🔍 [DEBUG] Album "All Falls Down" has 1 tracks
flutter: 🔍 [DEBUG] Album "Keep U Warm (ft. Jordan Shaw)" has 1 tracks
flutter: 🔍 [DEBUG] Album "Stay" has 1 tracks
flutter: 🔍 [DEBUG] Album "My Own Advice" has 1 tracks
flutter: 🔍 [DEBUG] Album "Lie" has 1 tracks
flutter: 🔍 [DEBUG] Album "In The Cold" has 1 tracks
flutter: 🔍 [DEBUG] Album "Beautiful Nothing" has 2 tracks
flutter: 🔍 [DEBUG] Album "Last Forever" has 1 tracks
flutter: 🔍 [DEBUG] Album "Would You Even Know" has 1 tracks
flutter: 🔍 [DEBUG] Album "On My Own - REAPER Remix" has 2 tracks
flutter: 🔍 [DEBUG] Album "You're Not Alone" has 1 tracks
flutter: 🔍 [DEBUG] Album "On My Own" has 1 tracks
flutter: 🔍 [DEBUG] Album "Ready (Ray Volpe Remix)" has 1 tracks
flutter: 🔍 [DEBUG] Album "Haven (Gammer Remix)" has 1 tracks
flutter: 🔍 [DEBUG] Album "Only One I Need" has 1 tracks
flutter: 🔍 [DEBUG] Album "Broken - VIP" has 1 tracks
flutter: 🔍 [DEBUG] Album "Broken" has 1 tracks
flutter: 🔍 [DEBUG] Album "Shadow" has 1 tracks
flutter: 🔍 [DEBUG] Album "Remedy Remixes" has 4 tracks
flutter: 🔍 [DEBUG] Album "Only One I Need" has 1 tracks
flutter: 🔍 [DEBUG] Album "Remedy" has 1 tracks
flutter: 🔍 [DEBUG] Album "Deep End (CloudNone Remix)" has 1 tracks
flutter: 🔍 [DEBUG] Album "Deep End" has 1 tracks
flutter: 🔍 [DEBUG] Album "Butterflies Acoustic" has 1 tracks
flutter: 🔍 [DEBUG] Album "Closer Than You (Remixes)" has 4 tracks
flutter: 🔍 [DEBUG] Album "Back To U (Remixes)" has 5 tracks
flutter: 🔍 [DEBUG] Album "Back To U (Topi Remix)" has 1 tracks
flutter: 🔍 [DEBUG] Album "Back To U (Mazare Remix)" has 1 tracks
flutter: 🔍 [DEBUG] Album "Back To U (Matrix & Futurebound Remix)" has 1 tracks
flutter: 🔍 [DEBUG] Album "Back To U (Fairlane Remix)" has 1 tracks
flutter: 🔍 [DEBUG] Album "Back To U (Control Freak Remix)" has 1 tracks
flutter: 🔍 [DEBUG] Album "Closer Than You" has 1 tracks
flutter: 🔍 [DEBUG] Album "Butterflies Remixes" has 5 tracks
flutter: 🔍 [DEBUG] Album "Butterflies Synchronice Remix" has 1 tracks
flutter: 🔍 [DEBUG] Album "Back To U" has 1 tracks
flutter: 🔍 [DEBUG] Album "Potions (William Black Remix)" has 1 tracks
flutter: 🔍 [DEBUG] Album "Take Me Snavs Remix" has 1 tracks
flutter: 🔍 [DEBUG] Album "Butterflies" has 1 tracks
flutter: ✅ [DEBUG] Successfully retrieved 100 tracks from albums
flutter: ✅ [AI SEARCH LOG] 21:30:38 | "William Black" [AI_ARTIST_BASED] -> 100 results
flutter: 📍 🔧 Added custom skin for pin 4: pin-skin-4
flutter: 📍 🔧 Updated real pin data for: 4
flutter: 📍 🔧 Added 5 new features to individual-pins-source memory: 7 → 12
flutter: 📍 ✅ Added 5 pins to individual layer (total: 12)
flutter: 📍 ✅ Processed pin IDs: [2, 30, 21, 29, 4]
flutter: 📍 ✨ Adding incremental glow effects for batch of 5 pins
flutter: 🌟 Added 10 features to glow-polygons-source memory (total: 34)
flutter: 🌟 Added 5 features to border-polygons-source memory (total: 17)
flutter: 🌟 Added 5 features to pulse-polygons-source memory (total: 17)
flutter: 📀 [SpotifyService] Retrieved 50 albums with tracks
flutter: 🔍 [DEBUG] Multiple albums API returned 50 albums with tracks
flutter: 🔍 [DEBUG] Album "The Nature of Hope Remixes" has 13 tracks
flutter: 🔍 [DEBUG] Album "The Nature of Hope" has 14 tracks
flutter: 🔍 [DEBUG] Album "Pieces (Remixes)" has 13 tracks
flutter: 🔍 [DEBUG] Album "Pieces" has 13 tracks
flutter: 🔍 [DEBUG] Album "Pages (The Remixes)" has 8 tracks
flutter: 🔍 [DEBUG] Album "Pages" has 10 tracks
flutter: 🔍 [DEBUG] Album "Nightmare" has 3 tracks
flutter: 🔍 [DEBUG] Album "Waiting On This Day Forever" has 2 tracks
flutter: 🔍 [DEBUG] Album "Save Me" has 1 tracks
flutter: 🔍 [DEBUG] Album "Wish I Could Forget (William Black Remix)" has 1 tracks
flutter: 🔍 [DEBUG] Album "Different Shit" has 1 tracks
flutter: 🔍 [DEBUG] Album "Collide" has 1 tracks
flutter: 🔍 [DEBUG] Album "All Falls Down" has 1 tracks
flutter: 🔍 [DEBUG] Album "Keep U Warm (ft. Jordan Shaw)" has 1 tracks
flutter: 🔍 [DEBUG] Album "Stay" has 1 tracks
flutter: 🔍 [DEBUG] Album "My Own Advice" has 1 tracks
flutter: 🔍 [DEBUG] Album "Lie" has 1 tracks
flutter: 🔍 [DEBUG] Album "In The Cold" has 1 tracks
flutter: 🔍 [DEBUG] Album "Beautiful Nothing" has 2 tracks
flutter: 🔍 [DEBUG] Album "Last Forever" has 1 tracks
flutter: 🔍 [DEBUG] Album "Would You Even Know" has 1 tracks
flutter: 🔍 [DEBUG] Album "On My Own - REAPER Remix" has 2 tracks
flutter: 🔍 [DEBUG] Album "You're Not Alone" has 1 tracks
flutter: 🔍 [DEBUG] Album "On My Own" has 1 tracks
flutter: 🔍 [DEBUG] Album "Ready (Ray Volpe Remix)" has 1 tracks
flutter: 🔍 [DEBUG] Album "Haven (Gammer Remix)" has 1 tracks
flutter: 🔍 [DEBUG] Album "Only One I Need" has 1 tracks
flutter: 🔍 [DEBUG] Album "Broken - VIP" has 1 tracks
flutter: 🔍 [DEBUG] Album "Broken" has 1 tracks
flutter: 🔍 [DEBUG] Album "Shadow" has 1 tracks
flutter: 🔍 [DEBUG] Album "Remedy Remixes" has 4 tracks
flutter: 🔍 [DEBUG] Album "Only One I Need" has 1 tracks
flutter: 🔍 [DEBUG] Album "Remedy" has 1 tracks
flutter: 🔍 [DEBUG] Album "Deep End (CloudNone Remix)" has 1 tracks
flutter: 🔍 [DEBUG] Album "Deep End" has 1 tracks
flutter: 🔍 [DEBUG] Album "Butterflies Acoustic" has 1 tracks
flutter: 🔍 [DEBUG] Album "Closer Than You (Remixes)" has 4 tracks
flutter: 🔍 [DEBUG] Album "Back To U (Remixes)" has 5 tracks
flutter: 🔍 [DEBUG] Album "Back To U (Topi Remix)" has 1 tracks
flutter: 🔍 [DEBUG] Album "Back To U (Mazare Remix)" has 1 tracks
flutter: 🔍 [DEBUG] Album "Back To U (Matrix & Futurebound Remix)" has 1 tracks
flutter: 🔍 [DEBUG] Album "Back To U (Fairlane Remix)" has 1 tracks
flutter: 🔍 [DEBUG] Album "Back To U (Control Freak Remix)" has 1 tracks
flutter: 🔍 [DEBUG] Album "Closer Than You" has 1 tracks
flutter: 🔍 [DEBUG] Album "Butterflies Remixes" has 5 tracks
flutter: 🔍 [DEBUG] Album "Butterflies Synchronice Remix" has 1 tracks
flutter: 🔍 [DEBUG] Album "Back To U" has 1 tracks
flutter: 🔍 [DEBUG] Album "Potions (William Black Remix)" has 1 tracks
flutter: 🔍 [DEBUG] Album "Take Me Snavs Remix" has 1 tracks
flutter: 🔍 [DEBUG] Album "Butterflies" has 1 tracks
flutter: ✅ [DEBUG] Successfully retrieved 90 tracks from albums
flutter: ✅ [AI SEARCH LOG] 21:30:39 | "William Black" [AI_ARTIST_BASED] -> 90 results
flutter: ✅ [DEBUG] Album-based search successful: 90 tracks
flutter: 🗄️ [Artist Cache] Got 100 album tracks + 30 hybrid tracks for "William Black" (PARALLEL)
flutter: 🗄️ [Artist Cache] Step 3: Deduplicating 130 tracks for "William Black"...
flutter: 🗄️ [Artist Cache] ✅ Successfully cached 100 unique tracks for "William Black"
flutter: ⏱️ [Artist Cache] Fetched and cached "William Black" in 1348ms
flutter: 🎯 [Artist Recs] Similar artist cached search for "William Black" returned 20 tracks (1348ms)
flutter: 🎯 [Artist Recs] Similar artist track fetching completed in 1355ms
flutter: 🎯 [Artist Recs] Total tracks before filtering: 260
flutter: 🧹 [Dedup] Starting deduplication of 260 tracks...
flutter: 🧹 Intelligent filtering 260 tracks (attempt: 0)...
flutter: 🧹 Intelligent filtering results (attempt: 0):
flutter:   - ID duplicates filtered: 18
flutter:   - Title-artist duplicates filtered: 28
flutter:   - Excluded tracks: 0 (allowed: false)
flutter:   - Already loaded: 0 (allowed: false)
flutter:   - Personally avoided: 0 (allowed: false)
flutter:   - Final tracks: 214
flutter: 🧹 [Local Filter] AI intelligent filtering: 260 → 214 tracks
flutter: 🧹 [Dedup] Filtering results:
flutter:    📊 Input: 260 → Output: 214
flutter:    🔄 Duplicate IDs: 0
flutter:    🎵 Duplicate title+artist: 0
flutter:    ⏱️ Recent tracks filtered: 0
flutter:    ⏰ Duration filtered: 0
flutter: 🎯 [Artist Recs] Final filtered tracks: 214
flutter: 🎤 [Artist Recs] Generated 214 artist-based recommendations
flutter: 🎭 [Mood Categories] Concurrent category generation completed in 3812ms
flutter: 🎭 [Mood Change] Generated categories: genreBased(0), artistBased(214), discover(0)
flutter: 🎭 [Mood Change] Preserving categories: topTracks(0), likedSongs(0), recentlyPlayed(0)
flutter: 🔄 [AI Service] Synced 100 track IDs with main provider
flutter: 🔄 [Track Management] Added 214 track IDs. Total: 100/100
flutter: ✅ Mood-based recommendations loaded: 214 tracks
flutter: ⏱️ [Performance] _loadArtistBasedRecommendationsOnly completed in 8773ms
flutter: 🌟 Updated existing glow sources with 10 glow features (accumulated across batches)
flutter: 🌟 Added incremental glow effects for 5 pins
flutter: 📍 ✨ Incremental glow effects added for batch
flutter: 📍 ✅ Parallel layer building completed for batch of 5 pins
flutter: 📍 ✨ Incremental display: Refreshed layer visibility after batch (showing clusters)
flutter: 📍 🔧 Added custom skin for pin 26: pin-skin-26
flutter: 📍 🔧 Updated real pin data for: 26
flutter: 📍 🔧 Processing feature: 24
flutter: 📍 🔧 Added custom skin for pin 1: pin-skin-1
flutter: 📍 🔧 Updated real pin data for: 1
flutter: 📍 🔧 Processing feature: 25
flutter: 📍 🔧 Added custom skin for pin 28: pin-skin-28
flutter: 📍 🔧 Updated real pin data for: 28
flutter: 📍 🔧 Added 5 new features to individual-pins-source memory: 12 → 17
flutter: 📍 ✅ Added 5 pins to individual layer (total: 17)
flutter: 📍 ✅ Processed pin IDs: [20, 27, 6, 9, 28]
flutter: 📍 ✨ Adding incremental glow effects for batch of 5 pins
flutter: 🌟 Added 10 features to glow-polygons-source memory (total: 44)
flutter: 🌟 Added 5 features to border-polygons-source memory (total: 22)
flutter: 🌟 Added 5 features to pulse-polygons-source memory (total: 22)
flutter: 🌟 Updated existing glow sources with 10 glow features (accumulated across batches)
flutter: 🌟 Added incremental glow effects for 5 pins
flutter: 📍 ✨ Incremental glow effects added for batch
flutter: 📍 ✅ Parallel layer building completed for batch of 5 pins
flutter: 📍 ✨ Incremental display: Refreshed layer visibility after batch (showing clusters)
flutter: 📍 🔧 Added custom skin for pin 25: pin-skin-25
flutter: 📍 🔧 Updated real pin data for: 25
flutter: 📍 🔧 Added 5 new features to individual-pins-source memory: 17 → 22
flutter: 📍 ✅ Added 5 pins to individual layer (total: 22)
flutter: 📍 ✅ Processed pin IDs: [10, 23, 8, 1, 25]
flutter: 📍 ✨ Adding incremental glow effects for batch of 5 pins
flutter: 🌟 Added 10 features to glow-polygons-source memory (total: 54)
flutter: 🌟 Added 5 features to border-polygons-source memory (total: 27)
flutter: 🌟 Added 5 features to pulse-polygons-source memory (total: 27)
flutter: 🌟 Updated existing glow sources with 10 glow features (accumulated across batches)
flutter: 🌟 Added incremental glow effects for 5 pins
flutter: 📍 ✨ Incremental glow effects added for batch
flutter: 📍 ✅ Parallel layer building completed for batch of 5 pins
flutter: 📍 ✨ Incremental display: Refreshed layer visibility after batch (showing clusters)
flutter: 📍 🔍 Validating current state: LayerState.showingClusters
flutter: 📍 ✅ State validation passed
flutter: 📍 🔧 Added custom skin for pin 24: pin-skin-24
flutter: 📍 🔧 Updated real pin data for: 24
flutter: 📍 🔧 Processing feature: 19
flutter: 📍 🔧 Added custom skin for pin 19: pin-skin-19
flutter: 📍 🔧 Updated real pin data for: 19
flutter: 📍 🔧 Added 5 new features to individual-pins-source memory: 22 → 27
flutter: 📍 ✅ Added 5 pins to individual layer (total: 27)
flutter: 📍 ✅ Processed pin IDs: [18, 15, 26, 24, 19]
flutter: 📍 ✨ Adding incremental glow effects for batch of 5 pins
flutter: 🌟 Added 10 features to glow-polygons-source memory (total: 64)
flutter: 🌟 Added 5 features to border-polygons-source memory (total: 32)
flutter: 🌟 Added 5 features to pulse-polygons-source memory (total: 32)
flutter: 🌟 Updated existing glow sources with 10 glow features (accumulated across batches)
flutter: 🌟 Added incremental glow effects for 5 pins
flutter: 📍 ✨ Incremental glow effects added for batch
flutter: 📍 ✅ Parallel layer building completed for batch of 5 pins
flutter: 📍 ✨ Incremental display: Refreshed layer visibility after batch (showing clusters)
flutter: 📍 🔍 Validating current state: LayerState.showingClusters
flutter: 📍 ✅ State validation passed
flutter: 📍 🔍 Validating current state: LayerState.showingClusters
flutter: 📍 ✅ State validation passed
flutter: 📍 🔍 Validating current state: LayerState.showingClusters
flutter: 📍 ✅ State validation passed
VERBOSE: OneSignal.LiveActivities executing outstanding requests
Message from debugger: killed