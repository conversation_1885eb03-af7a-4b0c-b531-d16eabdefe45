flutter: 📍 🎯 Setting cluster source with 26 features
flutter: 📍 ✅ Cluster layer updated successfully
flutter: 📍 ✅ Updated cluster layer with 3 new pins (total pins: 30, clusters: 4)
flutter: 📍 ✅ Parallel layer building completed for batch of 3 pins
flutter: 📍 ⚡ Batch processed - skipping layer visibility check (3 pins added, 27 total)
flutter: 🎉 No AR-placed pins to animate, skipping animations
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1797
flutter: Error adding skin image: ClientException with SocketException: Failed host lookup: 'via.placeholder.com' (OS Error: nodename nor servname provided, or not known, errno = 8), uri=https://via.placeholder.com/256x256/blue/ffffff?text=Classic+Blue+House
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1805
flutter: 🎯 Touch points changed: 0 → 1
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1786
flutter: 🎯 Touch points changed: 1 → 0
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1779
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1792
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1779
flutter: 🎯 Touch points changed: 0 → 1
flutter: 🎯 Touch points changed: 1 → 2
flutter: 🎯 Starting gesture: GestureType.pinch (was: GestureState.none)
flutter: 🎯 Gesture state changed: GestureState.pinching
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1801
flutter: 📍 🎯 Adding cluster feature: cluster_18_1753504731759 with 2 pins
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1787
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1779
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1800
flutter: 📍 🎯 Adding cluster feature: cluster_9_1753504765388 with 2 pins
flutter: 📍 🎯 Adding cluster feature: cluster_10_1753504765388 with 2 pins
flutter: 📍 🎯 Adding cluster feature: cluster_11_1753504765388 with 2 pins
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1804
flutter: 🎯 Touch points changed: 2 → 1
flutter: 🎯 Ending gesture: GestureType.pinch
flutter: 🎯 Gesture state changed: GestureState.none
flutter: 🎯 Touch points changed: 1 → 0
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1808
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1808
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1800
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1801
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1800
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1808
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1801
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1804
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1808
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1807
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1779
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1801
flutter: 🎯 Touch points changed: 0 → 1
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1786
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1786
flutter: 🎯 Touch points changed: 1 → 0
flutter: 🗺️ ===== GESTURE DETECTOR TAP =====
flutter: 🗺️ Tap at: 246.66665649414062, 398.6666564941406
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1803
flutter: 🎯 Found 4 features at tap point
flutter: 🎯 Feature tapped - ID: 1798, isCluster: false
flutter: 🎯 Individual pin tapped: 1798
flutter: 🎯 Pin 1798 manual distance check: 324.4m (aura: 80.0m) = distant
flutter: 🎯 Pin 1798 in aura list: false, actually nearby: false
flutter: 🎯 Current _pinsInAura count: 0
flutter: 🎯 _pinsInAura IDs: []
flutter: 🎯 Showing distant pin dialog for: 🎵 Feliz Navidad
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1804
flutter: 🖼️ [DistantPinBottomSheet] Profile pic debug:
flutter:   - Full pin data keys: [id, latitude, longitude, title, track_title, track_artist, caption, rarity, aura_radius, created_at, artwork_url, owner, skinDetails, service, upvote_count, downvote_count, cached_at, from_smart_fetch]
flutter:   - Owner data: {username: mtaylor, name: mtaylor, profile_pic: https://i.pravatar.cc/150?u=ec0ccb5e02976994bdf0aa1c18c80794}
flutter:   - Direct profile_pic: null
flutter:   - Final profilePicUrl: https://i.pravatar.cc/150?u=ec0ccb5e02976994bdf0aa1c18c80794
flutter:   - URL is valid: true
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1786
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1786
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1801
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1804
flutter: 📍 🎯 Adding cluster feature: cluster_12_1753504746847 with 2 pins
flutter: 📍 🎯 Adding cluster feature: cluster_13_1753504746847 with 2 pins
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1797
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1786
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1803
flutter: Error adding skin image: ClientException with SocketException: Failed host lookup: 'via.placeholder.com' (OS Error: nodename nor servname provided, or not known, errno = 8), uri=https://via.placeholder.com/256x256/blue/ffffff?text=Classic+Blue+House
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1805
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1801
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1792
flutter: 📍 🔍 Validating current state: LayerState.showingClusters
flutter: 📍 ✅ State validation passed
flutter: 📍 Pin state change: Pins: 24→27, Clusters: 3→4
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1803
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1784
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1804
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1797
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1803
flutter: Error adding skin image: ClientException with SocketException: Failed host lookup: 'via.placeholder.com' (OS Error: nodename nor servname provided, or not known, errno = 8), uri=https://via.placeholder.com/256x256/blue/ffffff?text=Classic+Blue+House
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1805
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1786
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1806
flutter: 📍 🎯 Adding cluster feature: cluster_18_1753504736257 with 2 pins
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1787
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1789
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1803
flutter: 🎯 Touch points changed: 0 → 1
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1781
flutter: 🎯 Touch points changed: 1 → 0
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1808
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1803
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1784
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1804
flutter: 🎯 Touch points changed: 0 → 1
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1789
flutter: 🎯 Touch points changed: 1 → 2
flutter: 🎯 Starting gesture: GestureType.pinch (was: GestureState.none)
flutter: 🎯 Gesture state changed: GestureState.pinching
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1792
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1800
flutter: 🗺️ ===== GESTURE DETECTOR TAP =====
flutter: 🗺️ Tap at: 228.66665649414062, 310.6666564941406
flutter: 🎯 Tap blocked - current gesture: GestureState.pinching
flutter: 🎯 Tap blocked by gesture manager
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1789
flutter: 🎯 Touch points changed: 2 → 1
flutter: 🎯 Ending gesture: GestureType.pinch
flutter: 🎯 Gesture state changed: GestureState.none
flutter: 🎯 Touch points changed: 1 → 0
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1779
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1789
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1789
flutter: 🎯 Touch points changed: 0 → 1
flutter: 🎯 Touch points changed: 1 → 0
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1789
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1789
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1804
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1784
flutter: 🎯 Touch points changed: 0 → 1
flutter: 📍 🎯 Adding cluster feature: cluster_18_1753504730754 with 2 pins
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1787
flutter: 🎯 Touch points changed: 1 → 0
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1806
flutter: 🎯 Touch points changed: 0 → 1
flutter: 🎯 Touch points changed: 1 → 0
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1806
flutter: 📍 🔍 Validating current state: LayerState.showingClusters
flutter: 📍 ✅ State validation passed
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1797
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1806
flutter: Error adding skin image: ClientException with SocketException: Failed host lookup: 'via.placeholder.com' (OS Error: nodename nor servname provided, or not known, errno = 8), uri=https://via.placeholder.com/256x256/blue/ffffff?text=Classic+Blue+House
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1805
flutter: 🎯 Touch points changed: 0 → 1
flutter: 🎯 Touch points changed: 1 → 0
flutter: 🗺️ ===== GESTURE DETECTOR TAP =====
flutter: 🗺️ Tap at: 227.66665649414062, 375.3333282470703
flutter: 🎯 Found 1 features at tap point
flutter: 🎯 Feature tapped - ID: 1788, isCluster: false
flutter: 🎯 Individual pin tapped: 1788
flutter: 🎯 Pin 1788 manual distance check: 4091.0m (aura: 85.0m) = distant
flutter: 🎯 Pin 1788 in aura list: false, actually nearby: false
flutter: 🎯 Current _pinsInAura count: 0
flutter: 🎯 _pinsInAura IDs: []
flutter: 🎯 Showing distant pin dialog for: 🎵 Ain't No Rest for the Wicked
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1808
flutter: 🖼️ [DistantPinBottomSheet] Profile pic debug:
flutter:   - Full pin data keys: [id, latitude, longitude, title, track_title, track_artist, caption, rarity, aura_radius, created_at, artwork_url, owner, skinDetails, service, upvote_count, downvote_count, cached_at, from_smart_fetch]
flutter:   - Owner data: {username: dortiz, name: dortiz, profile_pic: https://i.pravatar.cc/150?u=9ab1d14b3898d3e7087b302460e30ebc}
flutter:   - Direct profile_pic: null
flutter:   - Final profilePicUrl: https://i.pravatar.cc/150?u=9ab1d14b3898d3e7087b302460e30ebc
flutter:   - URL is valid: true
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1806
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1792
flutter: 📍 🎯 Adding cluster feature: cluster_21_1753504725871 with 2 pins
flutter: 📍 🎯 Adding cluster feature: cluster_22_1753504725871 with 2 pins
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1807
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1781
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1781
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1781
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1792
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1806
flutter: 📍 🎯 Adding cluster feature: cluster_9_1753504771358 with 2 pins
flutter: 📍 🎯 Adding cluster feature: cluster_10_1753504771358 with 2 pins
flutter: 📍 🎯 Adding cluster feature: cluster_11_1753504771358 with 2 pins
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1804
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1792
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1792
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1786
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1793
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1797
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1789
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1786
flutter: Error adding skin image: ClientException with SocketException: Failed host lookup: 'via.placeholder.com' (OS Error: nodename nor servname provided, or not known, errno = 8), uri=https://via.placeholder.com/256x256/blue/ffffff?text=Classic+Blue+House
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1805
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1792
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1786
flutter: 📍 🎯 Adding cluster feature: cluster_18_1753504736865 with 2 pins
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1787
flutter: 🎯 Touch points changed: 0 → 1
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1808
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1804
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1784
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1786
flutter: 🎯 Touch points changed: 1 → 0
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1784
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1800
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1779
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1786
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1781
flutter: 🎯 Touch points changed: 0 → 1
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1789
flutter: 🗺️ ===== GESTURE DETECTOR TAP =====
flutter: 🗺️ Tap at: 247.3333282470703, 385.6666564941406
flutter: 🎯 Found 1 features at tap point
flutter: 🎯 Feature tapped - ID: 1781, isCluster: false
flutter: 🎯 Individual pin tapped: 1781
flutter: 🎯 Pin 1781 manual distance check: 1947.0m (aura: 69.0m) = distant
flutter: 🎯 Pin 1781 in aura list: false, actually nearby: false
flutter: 🎯 Current _pinsInAura count: 0
flutter: 🎯 _pinsInAura IDs: []
flutter: 🎯 Showing distant pin dialog for: 🎵 All Girls Are The Same
flutter: 🎯 Touch points changed: 1 → 0
flutter: 🖼️ [DistantPinBottomSheet] Profile pic debug:
flutter:   - Full pin data keys: [id, latitude, longitude, title, track_title, track_artist, caption, rarity, aura_radius, created_at, artwork_url, owner, skinDetails, service, upvote_count, downvote_count, cached_at, from_smart_fetch]
flutter:   - Owner data: {username: epic_ryan61, name: epic_ryan61, profile_pic: https://i.pravatar.cc/150?u=852533e0e6976075197c1f8fef75b373}
flutter:   - Direct profile_pic: null
flutter:   - Final profilePicUrl: https://i.pravatar.cc/150?u=852533e0e6976075197c1f8fef75b373
flutter:   - URL is valid: true
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1792
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1804
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1797
flutter: Error adding skin image: ClientException with SocketException: Failed host lookup: 'via.placeholder.com' (OS Error: nodename nor servname provided, or not known, errno = 8), uri=https://via.placeholder.com/256x256/blue/ffffff?text=Classic+Blue+House
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1805
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1804
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1804
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1779
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1806
flutter: 📍 🎯 Adding cluster feature: cluster_12_1753504752443 with 2 pins
flutter: 📍 🎯 Adding cluster feature: cluster_13_1753504752443 with 2 pins
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1797
flutter: Error adding skin image: ClientException with SocketException: Failed host lookup: 'via.placeholder.com' (OS Error: nodename nor servname provided, or not known, errno = 8), uri=https://via.placeholder.com/256x256/blue/ffffff?text=Classic+Blue+House
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1805
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1807
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1804
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1779
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1779
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1804
flutter: 🎯 Touch points changed: 0 → 1
flutter: 🎯 Touch points changed: 1 → 2
flutter: 🎯 Starting gesture: GestureType.pinch (was: GestureState.none)
flutter: 🎯 Gesture state changed: GestureState.pinching
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1792
flutter: 🗺️ ===== GESTURE DETECTOR TAP =====
flutter: 🗺️ Tap at: 172.66665649414062, 507.0
flutter: 🎯 Tap blocked - current gesture: GestureState.pinching
flutter: 🎯 Tap blocked by gesture manager
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1789
flutter: 📍 🔍 Validating current state: LayerState.showingClusters
flutter: 📍 ✅ State validation passed
flutter: 🎯 Gesture timeout - clearing state
flutter: 🎯 Gesture state changed: GestureState.none
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1808
flutter: 🎯 Touch points changed: 2 → 1
flutter: 🎯 Touch points changed: 1 → 0
flutter: 📍 🎯 Adding cluster feature: cluster_18_1753504737656 with 2 pins
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1787
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1784
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1784
flutter: 🎯 Touch points changed: 0 → 1
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1779
flutter: 🎯 Touch points changed: 1 → 0
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1784
flutter: 🎯 Touch points changed: 0 → 1
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1784
flutter: 🎯 Touch points changed: 1 → 2
flutter: 🎯 Starting gesture: GestureType.pinch (was: GestureState.none)
flutter: 🎯 Gesture state changed: GestureState.pinching
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1784
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1793
flutter: 🎯 Gesture timeout - clearing state
flutter: 🎯 Gesture state changed: GestureState.none
flutter: 📍 🎯 Adding cluster feature: cluster_18_1753504731548 with 3 pins
flutter: 📍 🎯 Adding cluster feature: cluster_19_1753504731548 with 2 pins
flutter: 📍 🎯 Adding cluster feature: cluster_20_1753504731548 with 2 pins
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1781
flutter: 🎯 Touch points changed: 2 → 1
flutter: 🎯 Touch points changed: 1 → 0
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1781
flutter: 📍 🎯 Adding cluster feature: cluster_18_1753504731953 with 2 pins
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1787
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1797
flutter: 📍 🎯 Adding cluster feature: cluster_18_1753504731350 with 3 pins
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1787
flutter: Error adding skin image: ClientException with SocketException: Failed host lookup: 'via.placeholder.com' (OS Error: nodename nor servname provided, or not known, errno = 8), uri=https://via.placeholder.com/256x256/blue/ffffff?text=Classic+Blue+House
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1805
flutter: 🎯 Touch points changed: 0 → 1
flutter: 🎯 Touch points changed: 1 → 2
flutter: 🎯 Starting gesture: GestureType.pinch (was: GestureState.none)
flutter: 🎯 Gesture state changed: GestureState.pinching
flutter: 🗺️ ===== GESTURE DETECTOR TAP =====
flutter: 🗺️ Tap at: 165.3333282470703, 533.3333282470703
flutter: 🎯 Tap blocked - current gesture: GestureState.pinching
flutter: 🎯 Tap blocked by gesture manager
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1800
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1800
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1800
flutter: 🎯 Touch points changed: 2 → 1
flutter: 🎯 Ending gesture: GestureType.pinch
flutter: 🎯 Gesture state changed: GestureState.none
flutter: 🎯 Touch points changed: 1 → 0
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1797
flutter: Error adding skin image: ClientException with SocketException: Failed host lookup: 'via.placeholder.com' (OS Error: nodename nor servname provided, or not known, errno = 8), uri=https://via.placeholder.com/256x256/blue/ffffff?text=Classic+Blue+House
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1805
flutter: 📍 🎯 Adding cluster feature: cluster_18_1753504745642 with 2 pins
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1787
flutter: 🎯 Touch points changed: 0 → 1
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1792
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1797
flutter: Error adding skin image: ClientException with SocketException: Failed host lookup: 'via.placeholder.com' (OS Error: nodename nor servname provided, or not known, errno = 8), uri=https://via.placeholder.com/256x256/blue/ffffff?text=Classic+Blue+House
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1805
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1784
flutter: 🎯 Touch points changed: 1 → 0
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1797
flutter: Error adding skin image: ClientException with SocketException: Failed host lookup: 'via.placeholder.com' (OS Error: nodename nor servname provided, or not known, errno = 8), uri=https://via.placeholder.com/256x256/blue/ffffff?text=Classic+Blue+House
flutter: 📍 🎯 Adding cluster feature: cluster_15_1753504746248 with 2 pins
flutter: 📍 🎯 Adding cluster feature: cluster_16_1753504746248 with 2 pins
flutter: 📍 🎯 Adding cluster feature: cluster_17_1753504746248 with 2 pins
flutter: 📍 🎯 Adding cluster feature: cluster_18_1753504746248 with 2 pins
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1787
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1804
flutter: 📍 🎯 Adding cluster feature: cluster_14_1753504747036 with 2 pins
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1805
flutter: 🎯 Touch points changed: 0 → 1
flutter: 🎯 Touch points changed: 1 → 0
flutter: 🗺️ ===== GESTURE DETECTOR TAP =====
flutter: 🗺️ Tap at: 281.6666564941406, 349.0
flutter: 🎯 Found 3 features at tap point
flutter: 🎯 Feature tapped - ID: 1800, isCluster: false
flutter: 🎯 Individual pin tapped: 1800
flutter: 🎯 Pin 1800 manual distance check: 2402.5m (aura: 82.0m) = distant
flutter: 🎯 Pin 1800 in aura list: false, actually nearby: false
flutter: 🎯 Current _pinsInAura count: 0
flutter: 🎯 _pinsInAura IDs: []
flutter: 🎯 Showing distant pin dialog for: 🎵 The Joker
flutter: 🖼️ [DistantPinBottomSheet] Profile pic debug:
flutter:   - Full pin data keys: [id, latitude, longitude, title, track_title, track_artist, caption, rarity, aura_radius, created_at, artwork_url, owner, skinDetails, service, upvote_count, downvote_count, cached_at, from_smart_fetch]
flutter:   - Owner data: {username: soft_nora, name: soft_nora, profile_pic: https://i.pravatar.cc/150?u=84bb9628f5d6fbfbbb4e576d3c2b82b0}
flutter:   - Direct profile_pic: null
flutter:   - Final profilePicUrl: https://i.pravatar.cc/150?u=84bb9628f5d6fbfbbb4e576d3c2b82b0
flutter:   - URL is valid: true
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1808
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1781
flutter: 🖼️ [DistantPinBottomSheet] Profile pic debug:
flutter:   - Full pin data keys: [id, latitude, longitude, title, track_title, track_artist, caption, rarity, aura_radius, created_at, artwork_url, owner, skinDetails, service, upvote_count, downvote_count, cached_at, from_smart_fetch]
flutter:   - Owner data: {username: soft_nora, name: soft_nora, profile_pic: https://i.pravatar.cc/150?u=84bb9628f5d6fbfbbb4e576d3c2b82b0}
flutter:   - Direct profile_pic: null
flutter:   - Final profilePicUrl: https://i.pravatar.cc/150?u=84bb9628f5d6fbfbbb4e576d3c2b82b0
flutter:   - URL is valid: true
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1792
flutter: Error adding skin image: ClientException with SocketException: Failed host lookup: 'via.placeholder.com' (OS Error: nodename nor servname provided, or not known, errno = 8), uri=https://via.placeholder.com/256x256/blue/ffffff?text=Classic+Blue+House
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1789
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1779
flutter: 🖼️ [DistantPinBottomSheet] Profile pic debug:
flutter:   - Full pin data keys: [id, latitude, longitude, title, track_title, track_artist, caption, rarity, aura_radius, created_at, artwork_url, owner, skinDetails, service, upvote_count, downvote_count, cached_at, from_smart_fetch]
flutter:   - Owner data: {username: soft_nora, name: soft_nora, profile_pic: https://i.pravatar.cc/150?u=84bb9628f5d6fbfbbb4e576d3c2b82b0}
flutter:   - Direct profile_pic: null
flutter:   - Final profilePicUrl: https://i.pravatar.cc/150?u=84bb9628f5d6fbfbbb4e576d3c2b82b0
flutter:   - URL is valid: true
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1789
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1806
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1800
flutter: 🌧️ Checking location and updating weather...
flutter: 📍 🔍 Validating current state: LayerState.showingClusters
flutter: 📍 ✅ State validation passed
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1793
flutter: 🌧️ Found last known position: 36.146643758730306, -86.808037335383 (1.0 minutes old)
flutter: 🌧️ Using recent last known position
flutter: 🌧️ Got position: 36.1466, -86.8080
flutter: 🌧️ Weather update not needed (recent update or minimal movement)
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1804
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1797
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1784
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1784
flutter: Error adding skin image: ClientException with SocketException: Failed host lookup: 'via.placeholder.com' (OS Error: nodename nor servname provided, or not known, errno = 8), uri=https://via.placeholder.com/256x256/blue/ffffff?text=Classic+Blue+House
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1805
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1792
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1784
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1784
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1792
flutter: 📍 🎯 Adding cluster feature: cluster_23_1753504726267 with 2 pins
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1793
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1792
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1806
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1784
flutter: 📍 🎯 Adding cluster feature: cluster_18_1753504736662 with 2 pins
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1787
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1807
flutter: 🎯 Touch points changed: 0 → 1
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1807
flutter: 🎯 Touch points changed: 1 → 0
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1784
flutter: 🎯 Touch points changed: 0 → 1
flutter: 🎯 Touch points changed: 1 → 2
flutter: 🎯 Starting gesture: GestureType.pinch (was: GestureState.none)
flutter: 🎯 Gesture state changed: GestureState.pinching
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1792
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1806
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1806
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1779
flutter: 🎯 Gesture timeout - clearing state
flutter: 🎯 Gesture state changed: GestureState.none
flutter: 📍 🔍 Validating current state: LayerState.showingClusters
flutter: 📍 ✅ State validation passed
flutter: 🎯 Touch points changed: 2 → 1
flutter: 🎯 Touch points changed: 1 → 0
flutter: 🎯 Touch points changed: 0 → 1
flutter: 🎯 Touch points changed: 1 → 0
flutter: 🎯 Touch points changed: 0 → 1
flutter: 🎯 Touch points changed: 1 → 0
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1792
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1797
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1779
flutter: Error adding skin image: ClientException with SocketException: Failed host lookup: 'via.placeholder.com' (OS Error: nodename nor servname provided, or not known, errno = 8), uri=https://via.placeholder.com/256x256/blue/ffffff?text=Classic+Blue+House
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1805
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1804
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1781
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1807
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1808
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1789
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1789
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1806
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1789
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1789
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1789
flutter: 🎯 Touch points changed: 0 → 1
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1800
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1800
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1808
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1808
flutter: 🎯 Touch points changed: 1 → 0
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1808
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1797
flutter: 🎯 Touch points changed: 0 → 1
flutter: Error adding skin image: ClientException with SocketException: Failed host lookup: 'via.placeholder.com' (OS Error: nodename nor servname provided, or not known, errno = 8), uri=https://via.placeholder.com/256x256/blue/ffffff?text=Classic+Blue+House
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1805
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1789
flutter: 🎯 Touch points changed: 1 → 0
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1808
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1792
flutter: 🎯 Touch points changed: 0 → 1
flutter: 🎯 Touch points changed: 1 → 2
flutter: 🎯 Starting gesture: GestureType.pinch (was: GestureState.none)
flutter: 🎯 Gesture state changed: GestureState.pinching
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1781
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1800
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1788
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1797
flutter: Error adding skin image: ClientException with SocketException: Failed host lookup: 'via.placeholder.com' (OS Error: nodename nor servname provided, or not known, errno = 8), uri=https://via.placeholder.com/256x256/blue/ffffff?text=Classic+Blue+House
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1805
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1804
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1806
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1793
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1804
flutter: 📍 🔄 Zoom-triggered layer switch: zoom=16.01682154593397, shouldCluster=false, currently showing clusters=true, layers initialized=true
flutter: 📍 Enhanced layer switching: showClusters=false
flutter: 📍 Current zoom: 16.01682154593397, clustering threshold: 16.0
flutter: 📍 Current layer state: LayerState.showingClusters
flutter: 📍 Progressive rendering check: inProgress=false, enabled=true, pending=0
flutter: 📍 State transition requested: LayerState.showingClusters → LayerState.showingIndividual
flutter: 📍 Layer state changed: LayerState.showingClusters → LayerState.transitioning
flutter: 📍 State updated: LayerState.showingClusters → LayerState.transitioning
flutter: 🎯 Gesture timeout - clearing state
flutter: 🎯 Gesture state changed: GestureState.none
flutter: 📍 🎯 Adding cluster feature: cluster_18_1753504745434 with 2 pins
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1787
flutter: 📍 Layer state changed: LayerState.transitioning → LayerState.showingIndividual
flutter: 📍 State updated: LayerState.transitioning → LayerState.showingIndividual
flutter: 📍 ✅ State transition successful: LayerState.showingIndividual
flutter: 📍 ✅ Showing individual layers, hiding cluster layers
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1792
flutter: 📍 ✅ Showing individual layers, hiding cluster layers
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1789
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1789
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1797
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1779
flutter: 🎯 Touch points changed: 2 → 1
flutter: Error adding skin image: ClientException with SocketException: Failed host lookup: 'via.placeholder.com' (OS Error: nodename nor servname provided, or not known, errno = 8), uri=https://via.placeholder.com/256x256/blue/ffffff?text=Classic+Blue+House
flutter: 📍 🎯 Adding cluster feature: cluster_15_1753504751841 with 2 pins
flutter: 📍 🎯 Adding cluster feature: cluster_16_1753504751841 with 2 pins
flutter: 📍 🎯 Adding cluster feature: cluster_17_1753504751841 with 2 pins
flutter: 📍 🎯 Adding cluster feature: cluster_18_1753504751841 with 2 pins
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1787
flutter: 📍 ✅ Layer switch completed successfully
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1789
flutter: 🎯 Touch points changed: 1 → 2
flutter: 🎯 Starting gesture: GestureType.pinch (was: GestureState.none)
flutter: 🎯 Gesture state changed: GestureState.pinching
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1789
flutter: 📍 🔍 Validating current state: LayerState.showingIndividual
flutter: 📍 ✅ State validation passed
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1797
flutter: Error adding skin image: ClientException with SocketException: Failed host lookup: 'via.placeholder.com' (OS Error: nodename nor servname provided, or not known, errno = 8), uri=https://via.placeholder.com/256x256/blue/ffffff?text=Classic+Blue+House
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1805
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1797
flutter: Error adding skin image: ClientException with SocketException: Failed host lookup: 'via.placeholder.com' (OS Error: nodename nor servname provided, or not known, errno = 8), uri=https://via.placeholder.com/256x256/blue/ffffff?text=Classic+Blue+House
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1805
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1789
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1784
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1808
flutter: 📍 🎯 Adding cluster feature: cluster_18_1753504751241 with 2 pins
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1787
flutter: 📍 🔍 Validating layer visibility: individual=true, cluster=false
flutter: 📍 ✅ Layer visibility validated and corrected if needed
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1784
flutter: 🎯 Gesture timeout - clearing state
flutter: 🎯 Gesture state changed: GestureState.none
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1784
flutter: 🎯 Touch points changed: 2 → 1
flutter: 🎯 Touch points changed: 1 → 0
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1789
flutter: 🎯 Touch points changed: 0 → 1
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1784
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1797
flutter: 🎯 Touch points changed: 1 → 0
flutter: Error adding skin image: ClientException with SocketException: Failed host lookup: 'via.placeholder.com' (OS Error: nodename nor servname provided, or not known, errno = 8), uri=https://via.placeholder.com/256x256/blue/ffffff?text=Classic+Blue+House
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1805
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1784
flutter: 📍 🎯 Adding cluster feature: cluster_18_1753504737459 with 3 pins
flutter: 📍 🎯 Adding cluster feature: cluster_19_1753504737459 with 2 pins
flutter: 📍 🎯 Adding cluster feature: cluster_20_1753504737459 with 2 pins
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1781
flutter: 📍 🎯 Adding cluster feature: cluster_18_1753504737260 with 3 pins
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1787
flutter: 🎯 Touch points changed: 0 → 1
flutter: 📍 🔍 Validating current state: LayerState.showingIndividual
flutter: 📍 ✅ State validation passed
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1806
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1779
flutter: 📍 🎯 Adding cluster feature: cluster_14_1753504752638 with 2 pins
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1805
flutter: 🎯 Touch points changed: 1 → 0
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1806
flutter: Error adding skin image: ClientException with SocketException: Failed host lookup: 'via.placeholder.com' (OS Error: nodename nor servname provided, or not known, errno = 8), uri=https://via.placeholder.com/256x256/blue/ffffff?text=Classic+Blue+House
flutter: 🎯 Touch points changed: 0 → 1
flutter: 🎯 Touch points changed: 1 → 2
flutter: 🎯 Starting gesture: GestureType.pinch (was: GestureState.none)
flutter: 🎯 Gesture state changed: GestureState.pinching
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1792
flutter: 🗺️ ===== GESTURE DETECTOR TAP =====
flutter: 🗺️ Tap at: 153.66665649414062, 545.0
flutter: 🎯 Tap blocked - current gesture: GestureState.pinching
flutter: 🎯 Tap blocked by gesture manager
flutter: 📍 🔄 Zoom-triggered layer switch: zoom=15.758919839469673, shouldCluster=true, currently showing clusters=false, layers initialized=true
flutter: 📍 🔄 Switching to cluster view with 27 progressively rendered pins
flutter: 📍 Enhanced layer switching: showClusters=true
flutter: 📍 Current zoom: 15.758919839469673, clustering threshold: 16.0
flutter: 📍 Current layer state: LayerState.showingIndividual
flutter: 📍 Progressive rendering check: inProgress=false, enabled=true, pending=0
flutter: 📍 State transition requested: LayerState.showingIndividual → LayerState.showingClusters
flutter: 📍 Layer state changed: LayerState.showingIndividual → LayerState.transitioning
flutter: 📍 State updated: LayerState.showingIndividual → LayerState.transitioning
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1807
flutter: 📍 Layer state changed: LayerState.transitioning → LayerState.showingClusters
flutter: 📍 State updated: LayerState.transitioning → LayerState.showingClusters
flutter: 📍 ✅ State transition successful: LayerState.showingClusters
flutter: 📍 ✅ Showing cluster layers, hiding individual layers
flutter: 📍 ✅ Showing cluster layers, hiding individual layers
flutter: 🎯 Gesture timeout - clearing state
flutter: 🎯 Gesture state changed: GestureState.none
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1800
flutter: 📍 ✅ Layer switch completed successfully
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1779
flutter: 🎯 Touch points changed: 2 → 1
flutter: 📍 🔍 Validating current state: LayerState.showingClusters
flutter: 📍 ✅ State validation passed
flutter: 🎯 Touch points changed: 1 → 0
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1793
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1781
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1804
flutter: 📍 🎯 Adding cluster feature: cluster_18_1753504737859 with 2 pins
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1787
flutter: 📍 🔍 Validating layer visibility: individual=false, cluster=true
flutter: 📍 ✅ Layer visibility validated and corrected if needed
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1804
flutter: 📍 🎯 Adding cluster feature: cluster_12_1753504762765 with 2 pins
flutter: 📍 🎯 Adding cluster feature: cluster_13_1753504762765 with 2 pins
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1797
flutter: Error adding skin image: ClientException with SocketException: Failed host lookup: 'via.placeholder.com' (OS Error: nodename nor servname provided, or not known, errno = 8), uri=https://via.placeholder.com/256x256/blue/ffffff?text=Classic+Blue+House
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1805
flutter: 🎯 Touch points changed: 0 → 1
flutter: 🎯 Touch points changed: 1 → 2
flutter: 🎯 Starting gesture: GestureType.pinch (was: GestureState.none)
flutter: 🎯 Gesture state changed: GestureState.pinching
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1804
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1779
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1804
flutter: 📍 🔄 Zoom-triggered layer switch: zoom=16.002870698048135, shouldCluster=false, currently showing clusters=true, layers initialized=true
flutter: 📍 Enhanced layer switching: showClusters=false
flutter: 📍 Current zoom: 16.002870698048135, clustering threshold: 16.0
flutter: 📍 Current layer state: LayerState.showingClusters
flutter: 📍 Progressive rendering check: inProgress=false, enabled=true, pending=0
flutter: 📍 State transition requested: LayerState.showingClusters → LayerState.showingIndividual
flutter: 📍 Layer state changed: LayerState.showingClusters → LayerState.transitioning
flutter: 📍 State updated: LayerState.showingClusters → LayerState.transitioning
flutter: 🎯 Gesture timeout - clearing state
flutter: 🎯 Gesture state changed: GestureState.none
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1788
flutter: 📍 Layer state changed: LayerState.transitioning → LayerState.showingIndividual
flutter: 📍 State updated: LayerState.transitioning → LayerState.showingIndividual
flutter: 📍 ✅ State transition successful: LayerState.showingIndividual
flutter: 📍 ✅ Showing individual layers, hiding cluster layers
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1781
flutter: 📍 ✅ Showing individual layers, hiding cluster layers
flutter: 📍 🎯 Adding cluster feature: cluster_21_1753504731350 with 2 pins
flutter: 📍 🎯 Adding cluster feature: cluster_22_1753504731350 with 2 pins
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1807
flutter: 📍 ✅ Layer switch completed successfully
flutter: 📍 🔍 Validating current state: LayerState.showingIndividual
flutter: 📍 ✅ State validation passed
flutter: 🎯 Touch points changed: 2 → 1
flutter: 🎯 Touch points changed: 1 → 0
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1781
flutter: 📍 🔍 Validating layer visibility: individual=true, cluster=false
flutter: 📍 ✅ Layer visibility validated and corrected if needed
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1804
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1781
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1797
flutter: Error adding skin image: ClientException with SocketException: Failed host lookup: 'via.placeholder.com' (OS Error: nodename nor servname provided, or not known, errno = 8), uri=https://via.placeholder.com/256x256/blue/ffffff?text=Classic+Blue+House
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1805
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1800
flutter: 📍 🎯 Setting cluster source with 26 features
flutter: 📍 ✅ Cluster layer updated successfully
flutter: 📍 ✅ Updated cluster layer with 3 new pins (total pins: 30, clusters: 4)
flutter: 📍 ✅ Parallel layer building completed for batch of 3 pins
flutter: 🎯 Touch points changed: 0 → 1
flutter: 🎯 Touch points changed: 1 → 0
flutter: 🗺️ ===== GESTURE DETECTOR TAP =====
flutter: 🗺️ Tap at: 230.66665649414062, 385.0
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1792
flutter: 🎯 Found 4 features at tap point
flutter: 🎯 Feature tapped - ID: 1792, isCluster: false
flutter: 🎯 Individual pin tapped: 1792
flutter: 🎯 Pin 1792 manual distance check: 2924.2m (aura: 99.0m) = distant
flutter: 🎯 Pin 1792 in aura list: false, actually nearby: false
flutter: 🎯 Current _pinsInAura count: 0
flutter: 🎯 _pinsInAura IDs: []
flutter: 🎯 Showing distant pin dialog for: 🎵 Reelin' In The Years
flutter: 📍 🎯 Adding cluster feature: cluster_18_1753504746041 with 2 pins
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1787
flutter: 🖼️ [DistantPinBottomSheet] Profile pic debug:
flutter:   - Full pin data keys: [id, latitude, longitude, title, track_title, track_artist, caption, rarity, aura_radius, created_at, artwork_url, owner, skinDetails, service, upvote_count, downvote_count, cached_at, from_smart_fetch]
flutter:   - Owner data: {username: camila.ramirez, name: camila.ramirez, profile_pic: https://i.pravatar.cc/150?u=b5776c97be51a9d2d15eab1c824af3b2}
flutter:   - Direct profile_pic: null
flutter:   - Final profilePicUrl: https://i.pravatar.cc/150?u=b5776c97be51a9d2d15eab1c824af3b2
flutter:   - URL is valid: true
flutter: 📍 ⚡ Batch processed - skipping layer visibility check (3 pins added, 27 total)
flutter: 🎉 No AR-placed pins to animate, skipping animations
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1792
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1808
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1797
flutter: Error adding skin image: ClientException with SocketException: Failed host lookup: 'via.placeholder.com' (OS Error: nodename nor servname provided, or not known, errno = 8), uri=https://via.placeholder.com/256x256/blue/ffffff?text=Classic+Blue+House
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1805
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1804
flutter: 📍 🔍 Validating current state: LayerState.showingIndividual
flutter: 📍 ✅ State validation passed
flutter: 📍 Pin state change: Pins: 27→30, Clusters: 4→4
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1804
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1806
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1808
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1804
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1788
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1804
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1779
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1789
flutter: 📍 🎯 Adding cluster feature: cluster_12_1753504766581 with 2 pins
flutter: 📍 🎯 Adding cluster feature: cluster_13_1753504766581 with 2 pins
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1797
flutter: Error adding skin image: ClientException with SocketException: Failed host lookup: 'via.placeholder.com' (OS Error: nodename nor servname provided, or not known, errno = 8), uri=https://via.placeholder.com/256x256/blue/ffffff?text=Classic+Blue+House
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1805
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1781
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1808
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1807
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1807
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1788
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1789
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1807
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1779
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1789
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1804
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1789
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1779
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1789
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1808
flutter: 📍 🎯 Adding cluster feature: cluster_18_1753504746847 with 2 pins
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1787
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1806
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1779
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1800
flutter: 📍 🎯 Adding cluster feature: cluster_18_1753504751032 with 2 pins
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1787
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1793
flutter: 🎯 Touch points changed: 0 → 1
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1797
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1793
flutter: 🎯 Touch points changed: 1 → 0
flutter: 🗺️ ===== GESTURE DETECTOR TAP =====
flutter: 🗺️ Tap at: 113.0, 307.6666564941406
flutter: Error adding skin image: ClientException with SocketException: Failed host lookup: 'via.placeholder.com' (OS Error: nodename nor servname provided, or not known, errno = 8), uri=https://via.placeholder.com/256x256/blue/ffffff?text=Classic+Blue+House
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1805
flutter: 🎯 Found 4 features at tap point
flutter: 🎯 Feature tapped - ID: 1804, isCluster: false
flutter: 🎯 Individual pin tapped: 1804
flutter: 🎯 Pin 1804 manual distance check: 2908.7m (aura: 55.0m) = distant
flutter: 🎯 Pin 1804 in aura list: false, actually nearby: false
flutter: 🎯 Current _pinsInAura count: 0
flutter: 🎯 _pinsInAura IDs: []
flutter: 🎯 Showing distant pin dialog for: 🎵 Can't Stop
flutter: 🖼️ [DistantPinBottomSheet] Profile pic debug:
flutter:   - Full pin data keys: [id, latitude, longitude, title, track_title, track_artist, caption, rarity, aura_radius, created_at, artwork_url, owner, skinDetails, service, upvote_count, downvote_count, cached_at, from_smart_fetch]
flutter:   - Owner data: {username: dortiz, name: dortiz, profile_pic: https://i.pravatar.cc/150?u=9ab1d14b3898d3e7087b302460e30ebc}
flutter:   - Direct profile_pic: null
flutter:   - Final profilePicUrl: https://i.pravatar.cc/150?u=9ab1d14b3898d3e7087b302460e30ebc
flutter:   - URL is valid: true
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1807
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1779
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1806
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1792
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1808
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1800
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1792
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1792
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1806
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1792
flutter: 🎯 Touch points changed: 0 → 1
flutter: 🎯 Touch points changed: 1 → 2
flutter: 🎯 Starting gesture: GestureType.pinch (was: GestureState.none)
flutter: 🎯 Gesture state changed: GestureState.pinching
flutter: 📍 🎯 Setting cluster source with 26 features
flutter: 📍 ✅ Cluster layer updated successfully
flutter: 📍 ✅ Updated cluster layer with 3 new pins (total pins: 30, clusters: 4)
flutter: 📍 ✅ Parallel layer building completed for batch of 3 pins
flutter: 📍 🔄 Zoom-triggered layer switch: zoom=15.768051704572649, shouldCluster=true, currently showing clusters=false, layers initialized=true
flutter: 📍 🔄 Switching to cluster view with 27 progressively rendered pins
flutter: 📍 Enhanced layer switching: showClusters=true
flutter: 📍 Current zoom: 15.768051704572649, clustering threshold: 16.0
flutter: 📍 Current layer state: LayerState.showingIndividual
flutter: 📍 Progressive rendering check: inProgress=false, enabled=true, pending=0
flutter: 📍 State transition requested: LayerState.showingIndividual → LayerState.showingClusters
flutter: 📍 Layer state changed: LayerState.showingIndividual → LayerState.transitioning
flutter: 📍 State updated: LayerState.showingIndividual → LayerState.transitioning
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1800
flutter: 📍 ⚡ Batch processed - ensuring layer visibility: shouldCluster=true, current zoom=15.359240167749192 (first batch: false, state changed: true)
flutter: 🎉 No AR-placed pins to animate, skipping animations
flutter: 📍 Layer state changed: LayerState.transitioning → LayerState.showingClusters
flutter: 📍 State updated: LayerState.transitioning → LayerState.showingClusters
flutter: 📍 ✅ State transition successful: LayerState.showingClusters
flutter: 📍 ✅ Showing cluster layers, hiding individual layers
flutter: 📍 ✅ Showing cluster layers, hiding individual layers
flutter: 🎯 Gesture timeout - clearing state
flutter: 🎯 Gesture state changed: GestureState.none
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1793
flutter: 🎯 Touch points changed: 2 → 1
flutter: 📍 ✅ Layer switch completed successfully
flutter: 🎯 Touch points changed: 1 → 0
flutter: 📍 🔍 Validating current state: LayerState.showingClusters
flutter: 📍 ✅ State validation passed
flutter: 📍 🔍 Validating current state: LayerState.showingClusters
flutter: 📍 ✅ State validation passed
flutter: 📍 Pin state change: Pins: 30→33, Clusters: 4→4
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1779
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1800
flutter: 📍 🔍 Validating layer visibility: individual=false, cluster=true
flutter: 📍 ✅ Layer visibility validated and corrected if needed
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1792
flutter: 🎯 Touch points changed: 0 → 1
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1800
flutter: 🎯 Touch points changed: 1 → 0
flutter: 🎯 Touch points changed: 0 → 1
flutter: 🎯 Touch points changed: 1 → 2
flutter: 🎯 Starting gesture: GestureType.pinch (was: GestureState.none)
flutter: 🎯 Gesture state changed: GestureState.pinching
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1797
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1808
flutter: Error adding skin image: ClientException with SocketException: Failed host lookup: 'via.placeholder.com' (OS Error: nodename nor servname provided, or not known, errno = 8), uri=https://via.placeholder.com/256x256/blue/ffffff?text=Classic+Blue+House
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1805
flutter: 🎯 Touch points changed: 2 → 1
flutter: 🎯 Ending gesture: GestureType.pinch
flutter: 🎯 Gesture state changed: GestureState.none
flutter: 🎯 Touch points changed: 1 → 0
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1797
flutter: Error adding skin image: ClientException with SocketException: Failed host lookup: 'via.placeholder.com' (OS Error: nodename nor servname provided, or not known, errno = 8), uri=https://via.placeholder.com/256x256/blue/ffffff?text=Classic+Blue+House
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1805
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1781
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1788
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1792
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1807
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1792
flutter: 📍 🎯 Adding cluster feature: cluster_18_1753504745843 with 2 pins
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1787
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1781
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1792
flutter: 📍 🎯 Setting cluster source with 26 features
flutter: 📍 ✅ Cluster layer updated successfully
flutter: 📍 ✅ Updated cluster layer with 3 new pins (total pins: 30, clusters: 4)
flutter: 📍 ✅ Parallel layer building completed for batch of 3 pins
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1792
flutter: 📍 🎯 Adding cluster feature: cluster_23_1753504731759 with 2 pins
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1793
flutter: 📍 ⚡ Batch processed - skipping layer visibility check (3 pins added, 27 total)
flutter: 🎉 No AR-placed pins to animate, skipping animations
flutter: 🎯 Touch points changed: 0 → 1
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1806
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1804
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1800
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1781
flutter: 🎯 Touch points changed: 1 → 0
flutter: 📍 🎯 Setting cluster source with 26 features
flutter: 📍 ✅ Cluster layer updated successfully
flutter: 📍 ✅ Updated cluster layer with 3 new pins (total pins: 30, clusters: 4)
flutter: 📍 ✅ Parallel layer building completed for batch of 3 pins
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1804
flutter: 📍 ⚡ Batch processed - skipping layer visibility check (3 pins added, 27 total)
flutter: 🎉 No AR-placed pins to animate, skipping animations
flutter: 🎯 Touch points changed: 0 → 1
flutter: 🎯 Touch points changed: 1 → 2
flutter: 🎯 Starting gesture: GestureType.pinch (was: GestureState.none)
flutter: 🎯 Gesture state changed: GestureState.pinching
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1806
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1804
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1792
flutter: 🎯 Gesture timeout - clearing state
flutter: 🎯 Gesture state changed: GestureState.none
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1779
flutter: 📍 🎯 Adding cluster feature: cluster_12_1753504772552 with 2 pins
flutter: 📍 🎯 Adding cluster feature: cluster_13_1753504772552 with 2 pins
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1797
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1806
flutter: Error adding skin image: ClientException with SocketException: Failed host lookup: 'via.placeholder.com' (OS Error: nodename nor servname provided, or not known, errno = 8), uri=https://via.placeholder.com/256x256/blue/ffffff?text=Classic+Blue+House
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1805
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1804
flutter: 📍 🎯 Adding cluster feature: cluster_21_1753504737260 with 2 pins
flutter: 📍 🎯 Adding cluster feature: cluster_22_1753504737260 with 2 pins
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1807
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1808
flutter: 📍 🎯 Adding cluster feature: cluster_18_1753504751641 with 2 pins
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1787
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1806
flutter: 🎯 Touch points changed: 2 → 1
flutter: 🎯 Touch points changed: 1 → 0
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1807
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1808
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1779
flutter: 📍 🔍 Validating current state: LayerState.showingClusters
flutter: 📍 ✅ State validation passed
flutter: 📍 Pin state change: Pins: 33→39, Clusters: 4→4
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1806
flutter: 📍 🎯 Adding cluster feature: cluster_18_1753504761555 with 2 pins
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1787
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1797
flutter: Error adding skin image: ClientException with SocketException: Failed host lookup: 'via.placeholder.com' (OS Error: nodename nor servname provided, or not known, errno = 8), uri=https://via.placeholder.com/256x256/blue/ffffff?text=Classic+Blue+House
flutter: 📍 🎯 Adding cluster feature: cluster_15_1753504762156 with 2 pins
flutter: 📍 🎯 Adding cluster feature: cluster_16_1753504762156 with 2 pins
flutter: 📍 🎯 Adding cluster feature: cluster_17_1753504762156 with 2 pins
flutter: 📍 🎯 Adding cluster feature: cluster_18_1753504762157 with 2 pins
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1787
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1779
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1781
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1779
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1788
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1797
flutter: Error adding skin image: ClientException with SocketException: Failed host lookup: 'via.placeholder.com' (OS Error: nodename nor servname provided, or not known, errno = 8), uri=https://via.placeholder.com/256x256/blue/ffffff?text=Classic+Blue+House
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1805
flutter: 📍 🎯 Adding cluster feature: cluster_14_1753504762958 with 2 pins
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1805
flutter: 📍 🎯 Adding cluster feature: cluster_18_1753504752443 with 2 pins
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1787
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1793
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1797
flutter: Error adding skin image: ClientException with SocketException: Failed host lookup: 'via.placeholder.com' (OS Error: nodename nor servname provided, or not known, errno = 8), uri=https://via.placeholder.com/256x256/blue/ffffff?text=Classic+Blue+House
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1805
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1779
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1779
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1806
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1797
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1807
flutter: Error adding skin image: ClientException with SocketException: Failed host lookup: 'via.placeholder.com' (OS Error: nodename nor servname provided, or not known, errno = 8), uri=https://via.placeholder.com/256x256/blue/ffffff?text=Classic+Blue+House
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1805
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1781
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1793
flutter: 📍 ✅ Updated individual pins layer with all 27 pins
flutter: 📍 🔄 Creating clusters for 27 pins
flutter: 📍 🎯 _createOptimizedClusters called with 27 pins
flutter: 📍 🎯 Current zoom: 12.088904640869291, max zoom for clustering: 16.0
flutter: 📍 🎯 Starting clustering loop with 27 pins
flutter: 📍 🎯 Processing pin 1791 at (36.1447082, -86.8012017)
flutter: 📍 🎯 Processing pin 1785 at (36.1435356, -86.8173066)
flutter: 📍 🎯 Processing pin 1780 at (36.1393081, -86.80020569999999)
flutter: 📍 🎯 Processing pin 1799 at (36.1452811, -86.8093221)
flutter: 📍 🎯 Processing pin 1782 at (36.1483868, -86.80624069999999)
flutter: 📍 🎯 Processing pin 1798 at (36.144833, -86.81086499999999)
flutter: 📍 🎯 Processing pin 1783 at (36.1566269, -86.807655)
flutter: 📍 🎯 Processing pin 1801 at (36.1510048, -86.7958304)
flutter: 📍 🎯 Processing pin 1803 at (36.1530416, -86.79706829999999)
flutter: 📍 🎯 Processing pin 1786 at (36.1498406, -86.8061267)
flutter: 📍 🎯 Processing pin 1784 at (36.1435524, -86.8137803)
flutter: 📍 🎯 Processing pin 1789 at (36.1431087, -86.8025311)
flutter: 📍 🎯 Processing pin 1804 at (36.16309, -86.7828902)
flutter: 📍 🎯 Processing pin 1792 at (36.16211699999999, -86.781747)
flutter: 📍 🎯 Processing pin 1797 at (36.15993529999999, -86.7759626)
flutter: 📍 🎯 Processing pin 1805 at (36.1521699, -86.79490489999999)
flutter: 📍 🎯 Processing pin 1779 at (36.139382, -86.822069)
flutter: 📍 🎯 Processing pin 1806 at (36.1526125, -86.7887591)
flutter: 📍 🎯 Processing pin 1802 at (36.1582652, -86.7840794)
flutter: 📍 🎯 Processing pin 1787 at (36.15777169999999, -86.7826566)
flutter: 📍 🎯 Processing pin 1808 at (36.1607971, -86.7811329)
flutter: 📍 🎯 Processing pin 1781 at (36.13219439999999, -86.7958333)
flutter: 📍 🎯 Processing pin 1800 at (36.15726490000001, -86.78476979999999)
flutter: 📍 🎯 Processing pin 1807 at (36.1575235, -86.7746006)
flutter: 📍 🎯 Processing pin 1793 at (36.1609385, -86.77575709999999)
flutter: 📍 🎯 Processing pin 1788 at (36.109904, -86.8091029)
flutter: 📍 🎯 Clustering complete:
flutter: 📍 - Total items: 26
flutter: 📍 - Clusters: 1
flutter: 📍 - Individual pins: 25
flutter: 📍 - Individual 0: ID=1791, Position=(36.1447082, -86.8012017)
flutter: 📍 - Individual 1: ID=1785, Position=(36.1435356, -86.8173066)
flutter: 📍 - Individual 2: ID=1780, Position=(36.1393081, -86.80020569999999)
flutter: 📍 - Individual 3: ID=1799, Position=(36.1452811, -86.8093221)
flutter: 📍 - Individual 4: ID=1782, Position=(36.1483868, -86.80624069999999)
flutter: 📍 - Individual 5: ID=1798, Position=(36.144833, -86.81086499999999)
flutter: 📍 - Individual 6: ID=1783, Position=(36.1566269, -86.807655)
flutter: 📍 - Individual 7: ID=1801, Position=(36.1510048, -86.7958304)
flutter: 📍 - Individual 8: ID=1803, Position=(36.1530416, -86.79706829999999)
flutter: 📍 - Individual 9: ID=1786, Position=(36.1498406, -86.8061267)
flutter: 📍 - Individual 10: ID=1784, Position=(36.1435524, -86.8137803)
flutter: 📍 - Individual 11: ID=1789, Position=(36.1431087, -86.8025311)
flutter: 📍 - Individual 12: ID=1804, Position=(36.16309, -86.7828902)
flutter: 📍 - Individual 13: ID=1792, Position=(36.16211699999999, -86.781747)
flutter: 📍 - Individual 14: ID=1797, Position=(36.15993529999999, -86.7759626)
flutter: 📍 - Individual 15: ID=1805, Position=(36.1521699, -86.79490489999999)
flutter: 📍 - Individual 16: ID=1779, Position=(36.139382, -86.822069)
flutter: 📍 - Individual 17: ID=1806, Position=(36.1526125, -86.7887591)
flutter: 📍 - Cluster 18: ID=cluster_18_1753504851423, Count=2, Center=(36.1580812, -86.78396609999999)
flutter: 📍 - Individual 19: ID=1787, Position=(36.15777169999999, -86.7826566)
flutter: 📍 - Individual 20: ID=1808, Position=(36.1607971, -86.7811329)
flutter: 📍 - Individual 21: ID=1781, Position=(36.13219439999999, -86.7958333)
flutter: 📍 - Individual 22: ID=1800, Position=(36.15726490000001, -86.78476979999999)
flutter: 📍 - Individual 23: ID=1807, Position=(36.1575235, -86.7746006)
flutter: 📍 - Individual 24: ID=1793, Position=(36.1609385, -86.77575709999999)
flutter: 📍 - Individual 25: ID=1788, Position=(36.109904, -86.8091029)
flutter: 📍 🔄 Created 26 cluster items (1 actual clusters)
flutter: 📍 🔄 Added cluster icons for sizes: {2}
flutter: 📍 ✅ Both layers populated: 27 pins, 1 clusters
flutter: 📍 Enhanced layer switching: showClusters=true
flutter: 📍 Current zoom: 12.088904640869291, clustering threshold: 16.0
flutter: 📍 Current layer state: LayerState.showingClusters
flutter: 📍 Progressive rendering check: inProgress=false, enabled=true, pending=0
flutter: 📍 State transition requested: LayerState.showingClusters → LayerState.showingClusters
flutter: 📍 🚦 No-op transition: already in LayerState.showingClusters
flutter: 📍 ✅ Showing cluster layers, hiding individual layers
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1779
flutter: 📍 ✅ Showing cluster layers, hiding individual layers
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1800
flutter: 📍 ✅ Progressive rendering layer visibility set: clusters=true
flutter: 📍 📊 Cluster validation: 1 clusters created from 27 pins
flutter: 📍 🎯 Setting cluster source with 26 features
flutter: 📍 ✅ Cluster layer updated successfully
flutter: 📍 ✅ Updated cluster layer with 3 new pins (total pins: 30, clusters: 3)
flutter: 📍 ✅ Parallel layer building completed for batch of 3 pins
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1797
flutter: Error adding skin image: ClientException with SocketException: Failed host lookup: 'via.placeholder.com' (OS Error: nodename nor servname provided, or not known, errno = 8), uri=https://via.placeholder.com/256x256/blue/ffffff?text=Classic+Blue+House
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1805
flutter: 📍 ⚡ Batch processed - skipping layer visibility check (3 pins added, 27 total)
flutter: 🎉 No AR-placed pins to animate, skipping animations
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1797
flutter: Error adding skin image: ClientException with SocketException: Failed host lookup: 'via.placeholder.com' (OS Error: nodename nor servname provided, or not known, errno = 8), uri=https://via.placeholder.com/256x256/blue/ffffff?text=Classic+Blue+House
flutter: 📍 🎯 Adding cluster feature: cluster_15_1753504765986 with 2 pins
flutter: 📍 🎯 Adding cluster feature: cluster_16_1753504765986 with 2 pins
flutter: 📍 🎯 Adding cluster feature: cluster_17_1753504765986 with 2 pins
flutter: 📍 🎯 Adding cluster feature: cluster_18_1753504765986 with 2 pins
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1787
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1800
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1797
flutter: Error adding skin image: ClientException with SocketException: Failed host lookup: 'via.placeholder.com' (OS Error: nodename nor servname provided, or not known, errno = 8), uri=https://via.placeholder.com/256x256/blue/ffffff?text=Classic+Blue+House
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1805
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1808
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1797
flutter: 📍 🔍 Validating layer visibility: individual=false, cluster=true
flutter: Error adding skin image: ClientException with SocketException: Failed host lookup: 'via.placeholder.com' (OS Error: nodename nor servname provided, or not known, errno = 8), uri=https://via.placeholder.com/256x256/blue/ffffff?text=Classic+Blue+House
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1805
flutter: 📍 ✅ Layer visibility validated and corrected if needed
flutter: 📍 🎯 Adding cluster feature: cluster_18_1753504765388 with 2 pins
flutter: 📍 🎯 Adding individual pin feature in cluster view: 1787
Lost connection to device.
the Dart compiler exited unexpectedly.
Mothusos-MacBook-Air-7:BOPMaps mothusomalunga$ 