import 'package:flutter/material.dart';
import 'package:flutter/services.dart';  // Add this import for HapticFeedback
import 'package:flutter/foundation.dart';
import '../../models/music_track.dart';
import '../../config/themes.dart';
import 'package:provider/provider.dart';
import '../../providers/spotify_provider.dart';
import '../../providers/apple_music_provider.dart';
import '../../providers/youtube_provider.dart';
import '../common/scale_on_tap.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../utils/music_fallback_utils.dart';

/// Extension on MusicTrack to add service icon functionality
extension MusicTrackExtension on MusicTrack {
  /// Helper to get service type icon
  IconData get serviceTypeIcon {
    switch (serviceType.toLowerCase()) {
      case 'spotify':
        return Icons.music_note; // Use a built-in icon instead of an asset
      case 'apple':
        return Icons.music_note;
      case 'soundcloud':
        return Icons.music_note;
      default:
        return Icons.music_note;
    }
  }
}

/// A beautiful card for displaying a music track recommendation
class TrackCard extends StatelessWidget {
  final MusicTrack track;
  final String? subtitle;
  final double? confidence;
  final VoidCallback onTap;
  final bool showControls;
  final bool isPlaying;
  final VoidCallback? onTogglePlay;
  final String? title;
  final String? artist;
  final String? albumArt;
  final String? duration;
  final bool isSelected;
  final bool showPlayButton;
  final VoidCallback? onPlay;
  final bool enableSwipeToQueue;
  
  const TrackCard({
    Key? key,
    required this.track,
    this.subtitle,
    this.confidence,
    required this.onTap,
    this.showControls = false,
    this.isPlaying = false,
    this.onTogglePlay,
    this.title,
    this.artist,
    this.albumArt,
    this.duration,
    this.isSelected = false,
    this.showPlayButton = false,
    this.onPlay,
    this.enableSwipeToQueue = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final textTheme = Theme.of(context).textTheme;

    // Get access to SpotifyProvider for better error handling
    final spotifyProvider = Provider.of<SpotifyProvider>(context, listen: false);

    return ScaleOnTap(
      onTap: onTap,
      child: enableSwipeToQueue
        ? Dismissible(
            key: ValueKey('music_track_${track.id}'),
            direction: DismissDirection.startToEnd,
            confirmDismiss: (direction) async {
              // Add to queue and show feedback, but don't actually dismiss
              await _addToQueue(context);
              return false; // Don't dismiss the card
            },
            background: Container(
          alignment: Alignment.centerLeft,
          padding: const EdgeInsets.only(left: 20),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.centerRight,
              end: Alignment.centerLeft,
              colors: [
                Colors.transparent,
                Theme.of(context).colorScheme.primary.withOpacity(0.1),
                Theme.of(context).colorScheme.primary.withOpacity(0.3),
              ],
            ),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.queue_music_rounded,
                color: Theme.of(context).colorScheme.primary,
                size: 24,
              ),
              const SizedBox(height: 4),
              Text(
                'Add to Queue',
                style: TextStyle(
                  color: Theme.of(context).colorScheme.primary,
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
        child: Card(
        elevation: isSelected ? 4 : 1, // Reduce default elevation for a cleaner look
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
          side: isSelected 
              ? BorderSide(color: Theme.of(context).colorScheme.primary, width: 2)
              : BorderSide(color: Colors.grey.withOpacity(0.1)),
        ),
        clipBehavior: Clip.antiAlias,
        color: isSelected 
            ? Theme.of(context).primaryColor.withOpacity(0.05)
            : isDarkMode 
                ? Colors.grey.shade900
                : Theme.of(context).colorScheme.surface, // Use surface color for consistent theming
        margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 6), // Add consistent margins
        child: Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            gradient: isSelected
                ? LinearGradient(
                    colors: [
                      Theme.of(context).colorScheme.primary.withOpacity(0.05),
                      Theme.of(context).colorScheme.primary.withOpacity(0.1),
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  )
                : null,
          ),
          child: Row(
            children: [
              // Album artwork
              _buildAlbumArt(isDarkMode),
              
              const SizedBox(width: 16),
              
              // Track details
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      title ?? track.title,
                      style: textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w500,
                        fontSize: 14,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 2),
                    Text(
                      artist ?? track.artist,
                      style: textTheme.bodyMedium?.copyWith(
                        color: Theme.of(context).textTheme.bodySmall?.color,
                        fontSize: 12,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    
                    if (subtitle != null && subtitle!.isNotEmpty) ...[
                      const SizedBox(height: 4),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                        decoration: BoxDecoration(
                          color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text(
                          subtitle!,
                          style: textTheme.bodyMedium?.copyWith(
                            fontStyle: FontStyle.italic,
                            color: Theme.of(context).colorScheme.primary,
                            fontSize: 10,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                    
                    // Confidence indicator
                    if (confidence != null) ...[
                      const SizedBox(height: 8),
                      _buildConfidenceIndicator(context, isDarkMode),
                    ],
                  ],
                ),
              ),
              
              // Play controls
              if (showControls)
                _buildPlayButton(context)
              else if (showPlayButton && onPlay != null)
                IconButton(
                  icon: Icon(
                    isPlaying ? Icons.pause_circle_filled : Icons.play_circle_filled,
                    size: 40,
                    color: Theme.of(context).colorScheme.primary.withOpacity(0.85),
                  ),
                  onPressed: () async {
                    try {
                      await _playTrackWithFallback(context);
                      HapticFeedback.lightImpact();
                    } catch (e) {
                      // error handling...
                    }
                  },
              padding: const EdgeInsets.only(left: 8),
                )
              else if (isSelected)
                Container(
                  width: 28,
                  height: 28,
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.check,
                    color: Theme.of(context).colorScheme.primary,
                    size: 20,
                  ),
                ),
            ],
          ),
        ),
      ),
    )
        : Card(
        elevation: isSelected ? 4 : 1, // Reduce default elevation for a cleaner look
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
          side: isSelected 
              ? BorderSide(color: Theme.of(context).colorScheme.primary, width: 2)
              : BorderSide(color: Colors.grey.withOpacity(0.1)),
        ),
        clipBehavior: Clip.antiAlias,
        color: isSelected 
            ? Theme.of(context).primaryColor.withOpacity(0.05)
            : isDarkMode 
                ? Colors.grey.shade900
                : Theme.of(context).colorScheme.surface, // Use surface color for consistent theming
        margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 6), // Add consistent margins
        child: Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            gradient: isSelected
                ? LinearGradient(
                    colors: [
                      Theme.of(context).colorScheme.primary.withOpacity(0.05),
                      Theme.of(context).colorScheme.primary.withOpacity(0.1),
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  )
                : null,
          ),
          child: Row(
            children: [
              // Album artwork
              _buildAlbumArt(isDarkMode),
              
              const SizedBox(width: 16),
              
              // Track details
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      title ?? track.title,
                      style: textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w500,
                        fontSize: 14,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 2),
                    Text(
                      artist ?? track.artist,
                      style: textTheme.bodyMedium?.copyWith(
                        color: Theme.of(context).textTheme.bodySmall?.color,
                        fontSize: 12,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    
                    if (subtitle != null && subtitle!.isNotEmpty) ...[
                      const SizedBox(height: 4),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                        decoration: BoxDecoration(
                          color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text(
                          subtitle!,
                          style: textTheme.bodyMedium?.copyWith(
                            fontStyle: FontStyle.italic,
                            color: Theme.of(context).colorScheme.primary,
                            fontSize: 10,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                    
                    // Confidence indicator
                    if (confidence != null) ...[
                      const SizedBox(height: 8),
                      _buildConfidenceIndicator(context, isDarkMode),
                    ],
                  ],
                ),
              ),
              
              // Play controls
              if (showControls)
                _buildPlayButton(context)
              else if (showPlayButton && onPlay != null)
                IconButton(
                  icon: Icon(
                    isPlaying ? Icons.pause_circle_filled : Icons.play_circle_filled,
                    size: 40,
                    color: Theme.of(context).colorScheme.primary.withOpacity(0.85),
                  ),
                  onPressed: () async {
                    try {
                      await _playTrackWithFallback(context);
                      HapticFeedback.lightImpact();
                    } catch (e) {
                      // error handling...
                    }
                  },
              padding: const EdgeInsets.only(left: 8),
                )
              else if (isSelected)
                Container(
                  width: 28,
                  height: 28,
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.check,
                    color: Theme.of(context).colorScheme.primary,
                    size: 20,
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
  
  Widget _buildAlbumArt(bool isDarkMode) {
    return Stack(
      alignment: Alignment.center,
      children: [
        // Album art
        Container(
          width: 64,
          height: 64,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.2),
                spreadRadius: 1,
                blurRadius: 5,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(10),
            child: (() {
              final art = (albumArt != null && albumArt!.isNotEmpty)
                  ? albumArt!
                  : (track.albumArt.isNotEmpty ? track.albumArt : '');
              if (art.isNotEmpty) {
                return Image.network(
                  art,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return _placeholderArt(isDarkMode);
                  },
                  loadingBuilder: (context, child, loadingProgress) {
                    if (loadingProgress == null) return child;
                    return _loadingArt(context, isDarkMode, loadingProgress);
                  },
                );
              } else {
                return _placeholderArt(isDarkMode);
              }
            })(),
          ),
        ),
        
        // Service icon overlay
        Positioned(
          bottom: 4,
          right: 4,
          child: Container(
            padding: const EdgeInsets.all(3),
            decoration: BoxDecoration(
              color: Colors.black.withOpacity(0.7),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Icon(
              track.serviceTypeIcon,
              color: Colors.white,
              size: 14,
            ),
          ),
        ),
        
        // Play indicator if playing
        if (isPlaying)
          Container(
            width: 64,
            height: 64,
            decoration: BoxDecoration(
              color: Colors.black.withOpacity(0.5),
              borderRadius: BorderRadius.circular(10),
            ),
            child: const Icon(
              Icons.pause,
              color: Colors.white,
              size: 32,
            ),
          ),
      ],
    );
  }
  
  Widget _buildPlayButton(BuildContext context) {
    return IconButton(
      icon: Icon(
        isPlaying ? Icons.pause_circle_filled : Icons.play_circle_filled,
        size: 40,
        color: Theme.of(context).colorScheme.primary, // Use theme color
      ),
      onPressed: onTogglePlay,
      constraints: const BoxConstraints(), // Tighter constraints
      padding: const EdgeInsets.only(left: 8), // Add padding
    );
  }
  
  Widget _buildConfidenceIndicator(BuildContext context, bool isDarkMode) {
    // Calculate color based on confidence
    final color = _getConfidenceColor(confidence!, context);
    
    return Row(
      children: [
        Expanded(
          child: ClipRRect(
            borderRadius: BorderRadius.circular(4),
            child: LinearProgressIndicator(
              value: confidence,
              backgroundColor: isDarkMode ? Colors.grey[700] : Colors.grey[300],
              valueColor: AlwaysStoppedAnimation<Color>(color),
              minHeight: 4,
            ),
          ),
        ),
        const SizedBox(width: 8),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
          decoration: BoxDecoration(
            color: color.withOpacity(0.2),
            borderRadius: BorderRadius.circular(4),
          ),
          child: Text(
            '${(confidence! * 100).toInt()}%',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: color,
              fontWeight: FontWeight.bold,
              fontSize: 10,
            ),
            maxLines: 1,
            overflow: TextOverflow.clip,
          ),
        ),
      ],
    );
  }
  
  Color _getConfidenceColor(double confidence, BuildContext context) {
    if (confidence >= 0.8) {
      return Colors.green;
    } else if (confidence >= 0.6) {
      return Colors.orange;
    } else {
      return Colors.red;
    }
  }
  
  // Helper widgets for placeholders
  Widget _placeholderArt(bool isDarkMode) {
    return Container(
      width: 64,
      height: 64,
      color: isDarkMode ? Colors.grey[800] : Colors.grey[300],
      child: Icon(
        Icons.music_note,
        color: isDarkMode ? Colors.grey[600] : Colors.grey[500],
        size: 32,
      ),
    );
  }

  Widget _loadingArt(BuildContext context, bool isDarkMode, ImageChunkEvent loadingProgress) {
    return Container(
      width: 64,
      height: 64,
      color: isDarkMode ? Colors.grey[800] : Colors.grey[300],
      child: Center(
        child: CircularProgressIndicator(
          value: loadingProgress.expectedTotalBytes != null
              ? loadingProgress.cumulativeBytesLoaded / loadingProgress.expectedTotalBytes!
              : null,
          strokeWidth: 2,
          valueColor: AlwaysStoppedAnimation<Color>(Theme.of(context).colorScheme.primary),
        ),
      ),
    );
  }

  /// Play track with Apple Music first, then user-controlled YouTube fallback
  Future<void> _playTrackWithFallback(BuildContext context) async {
    if (!context.mounted) return;

    final appleMusicProvider = Provider.of<AppleMusicProvider>(context, listen: false);
    bool success = false;

    // Check if this is a cross-platform track that needs search
    final needsSearch = track.service != 'apple_music' && track.service != 'apple';

    try {
      if (kDebugMode) {
        print('🎵 [TrackCard] Attempting Apple Music playback for: ${track.title} (needsSearch: $needsSearch)');
      }

      if (needsSearch) {
        // Use the new method that shows snackbar for no exact match
        success = await appleMusicProvider.playTrackBySearchWithFallback(track, context);
      } else {
        // For Apple Music tracks, use queue manager directly
        final queueManager = appleMusicProvider.queueManager;
        success = await queueManager.setQueue(
          tracks: [track],
          collectionType: 'single_track',
          startIndex: 0,
        );
      }

      if (kDebugMode) {
        print('🎵 [TrackCard] Apple Music result: success=$success');
      }
    } catch (e) {
      if (kDebugMode) {
        print('🎵 [TrackCard] Apple Music failed: $e');
      }
      success = false;
    }

    // Only fallback to YouTube automatically if it's not a search scenario
    // For search scenarios, the snackbar will handle user-controlled fallback
    if (!success && !needsSearch) {
      if (kDebugMode) {
        print('🎵 [TrackCard] Apple Music failed, trying YouTube fallback...');
      }
      if (context.mounted) {
        await _tryYouTubeFallback(context);
      }
    } else if (success) {
      if (kDebugMode) {
        print('✅ [TrackCard] Successfully played track on Apple Music: ${track.title}');
      }
    }
  }

  /// Try YouTube fallback
  Future<void> _tryYouTubeFallback(BuildContext context) async {
    if (!context.mounted) return;

    try {
      final youtubeProvider = Provider.of<YouTubeProvider>(context, listen: false);

      if (!youtubeProvider.isInitialized) {
        await youtubeProvider.initialize();
      }

      if (kDebugMode) {
        print('🎵 [TrackCard] Attempting YouTube playback for: ${track.title} by ${track.artist}');
      }

      await youtubeProvider.playTrack(track);

      if (kDebugMode) {
        print('✅ [TrackCard] Successfully played track on YouTube: ${track.title}');
      }

    } catch (e) {
      if (kDebugMode) {
        print('❌ [TrackCard] YouTube fallback failed: $e');
      }

      // Final fallback: try to open in browser
      if (context.mounted) {
        await _tryBrowserFallback(context);
      }
    }
  }

  /// Final fallback: try to open track in browser
  Future<void> _tryBrowserFallback(BuildContext context) async {
    if (!context.mounted) return;

    try {
      // Try to construct a search URL
      final query = '${track.artist} ${track.title}'.replaceAll(' ', '+');
      final url = 'https://www.youtube.com/results?search_query=$query';

      if (await canLaunchUrl(Uri.parse(url))) {
        await launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
        if (kDebugMode) {
          print('✅ [TrackCard] Opened track in browser: ${track.title}');
        }
      } else {
        throw Exception('Could not launch URL');
      }

    } catch (e) {
      if (kDebugMode) {
        print('❌ [TrackCard] Browser fallback failed: $e');
      }

      // Show error to user
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Unable to play "${track.title}". Please try again later.'),
            backgroundColor: Colors.red[600],
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }

  // Add to queue functionality
  Future<void> _addToQueue(BuildContext context) async {
    try {
      final appleMusicProvider = Provider.of<AppleMusicProvider>(context, listen: false);
      final spotifyProvider = Provider.of<SpotifyProvider>(context, listen: false);

      bool success = false;

      // Prioritize Apple Music when connected
      if (appleMusicProvider.isConnected) {
        // Use Apple Music queue manager to add track
        final queueManager = appleMusicProvider.queueManager;
        queueManager.addToQueue(track);
        success = true; // addToQueue doesn't return a value, assume success
      } else if (spotifyProvider.isConnected) {
        // Fallback to Spotify hybrid queue manager
        success = await spotifyProvider.hybridQueueManager.addTrackToQueue(track);
      }

      if (success && context.mounted) {
        // Show success feedback
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(
                  Icons.queue_music_rounded,
                  color: Colors.white,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Added "${track.title}" to queue',
                    style: const TextStyle(color: Colors.white),
                  ),
                ),
              ],
            ),
            backgroundColor: Theme.of(context).colorScheme.primary,
            behavior: SnackBarBehavior.floating,
            duration: const Duration(seconds: 2),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        );
      } else if (context.mounted) {
        // Show error feedback
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(
                  Icons.error_outline_rounded,
                  color: Colors.white,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Failed to add "${track.title}" to queue',
                    style: const TextStyle(color: Colors.white),
                  ),
                ),
              ],
            ),
            backgroundColor: Colors.red[600],
            behavior: SnackBarBehavior.floating,
            duration: const Duration(seconds: 3),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error adding to queue: $e'),
            backgroundColor: Colors.red[600],
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }
}