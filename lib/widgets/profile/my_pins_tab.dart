import 'package:bop_maps/providers/youtube_provider.dart';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/scheduler.dart';
import 'package:provider/provider.dart';
import '../../providers/spotify_provider.dart';
import '../../providers/apple_music_provider.dart';
import '../../services/api/pins_service.dart';
import '../../models/music_track.dart';
import '../../models/pin.dart';
import 'components/pin_card.dart';
import 'components/track_card.dart';
import 'components/artist_card.dart';
import 'components/playlist_card.dart';
import 'components/playlist_detail_view.dart';
import 'components/track_engagement_bottom_sheet.dart';
import 'components/track_card_skeleton.dart';
import 'components/pin_card_skeleton.dart';
import 'components/artist_card_skeleton.dart';
import 'components/playlist_card_skeleton.dart';
import 'components/suggested_songs_widget.dart';
import '../../providers/suggested_songs_provider.dart';

class MyPinsTab extends StatefulWidget {
  final String currentFilter;
  final ValueChanged<String> onFilterSelected;
  const MyPinsTab({Key? key, required this.currentFilter, required this.onFilterSelected}) : super(key: key);

  @override
  State<MyPinsTab> createState() => _MyPinsTabState();
}

class _MyPinsTabState extends State<MyPinsTab> with AutomaticKeepAliveClientMixin {
  Map<String, dynamic>? _selectedPlaylist;
  
  // Static cache for persistent data storage across widget rebuilds
  static final Map<String, List<Pin>> _cachedPinData = {};
  static final Map<String, List<MusicTrack>> _cachedTrackData = {};
  static final Map<String, List<Map<String, dynamic>>> _cachedMapData = {};
  static final PageStorageBucket _bucket = PageStorageBucket();
  
  // Data lists for different filters
  List<Pin> _pins = [];
  List<Pin> _collectedPins = [];
  List<Pin> _upvotedPins = [];
  List<MusicTrack> _likedSongs = [];
  List<MusicTrack> _topTracks = [];
  List<MusicTrack> _recentlyPlayed = [];
  List<Map<String, dynamic>> _topArtists = [];
  List<Map<String, dynamic>> _playlists = [];
  // Suggested songs now handled by shared provider
  
  // Loading states
  bool _isLoadingPins = false;
  bool _isLoadingCollected = false;
  bool _isLoadingUpvoted = false;
  bool _isLoadingLiked = false;
  bool _isLoadingTop = false;
  bool _isLoadingRecent = false;
  bool _isLoadingArtists = false;
  bool _isLoadingPlaylists = false;
  // Suggested songs loading state handled by shared provider
  
  // Pagination states for pin tabs
  Map<String, int> _currentPages = {
    'My Pins': 1,
    'Collected': 1,
    'Upvoted': 1,
  };
  
  Map<String, bool> _hasMoreData = {
    'My Pins': true,
    'Collected': true,
    'Upvoted': true,
  };
  
  Map<String, bool> _isLoadingMore = {
    'My Pins': false,
    'Collected': false,
    'Upvoted': false,
  };
  
  // Error states
  String? _errorMessage;
  
  // Reference to SpotifyProvider for listening to changes
  SpotifyProvider? _spotifyProvider;
  bool _wasSpotifyConnected = false;
  
  // Track last filter to detect changes
  String? _lastFilter;
  
  @override
  bool get wantKeepAlive => true;

  /// Convert Pin object to Map format for PinCard widget
  Map<String, dynamic> _pinToMap(Pin pin) {
    return {
      'id': pin.id,
      'songTitle': pin.trackTitle,
      'artist': pin.trackArtist,
      'location': pin.locationName ?? 'Unknown Location',
      'date': _formatDate(pin.createdAt),
      'genre': pin.album ?? 'Music',
      'image': pin.artworkUrl,
      'service': pin.service,
      'likes': pin.interactionCount['like'] ?? 0,
      'comments': pin.interactionCount['comment'] ?? 0,
      'plays': pin.interactionCount['view'] ?? 0,
      // Include the new engagement_counts structure from the backend
      'engagement_counts': pin.engagementCounts,
      'uri': pin.trackUrl,
      'caption': pin.caption,
      'title': pin.title,
      'isPrivate': pin.isPrivate,
      'rarity': pin.rarity,
      'skin': pin.skin,
      'auraRadius': pin.auraRadius,
      // Add duration explicitly
      'duration_ms': pin.durationMs,
    };
  }

  /// Format date for display
  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);
    
    if (difference.inDays > 0) {
      return '${difference.inDays} day${difference.inDays > 1 ? 's' : ''} ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} hour${difference.inHours > 1 ? 's' : ''} ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} minute${difference.inMinutes > 1 ? 's' : ''} ago';
    } else {
      return 'Just now';
    }
  }

  @override
  void initState() {
    super.initState();
    _lastFilter = widget.currentFilter;
    
    // Restore cached data first
    _restoreCachedData();
    
    SchedulerBinding.instance.addPostFrameCallback((_) {
      if (!mounted) return;
      // Obtain SpotifyProvider and set up a listener to react to connection/data changes
      _spotifyProvider = Provider.of<SpotifyProvider>(context, listen: false);
      _wasSpotifyConnected = _spotifyProvider!.isConnected;
      _spotifyProvider!.addListener(_onSpotifyProviderUpdated);

      _loadInitialData();
    });
  }

  /// Restore cached data from static storage
  void _restoreCachedData() {
    setState(() {
      // Restore pin data
      _pins = List<Pin>.from(_cachedPinData['My Pins'] ?? []);
      _collectedPins = List<Pin>.from(_cachedPinData['Collected'] ?? []);
      _upvotedPins = List<Pin>.from(_cachedPinData['Upvoted'] ?? []);
      
      // Restore track data
      _likedSongs = List<MusicTrack>.from(_cachedTrackData['Liked'] ?? []);
      _topTracks = List<MusicTrack>.from(_cachedTrackData['Top Tracks'] ?? []);
      _recentlyPlayed = List<MusicTrack>.from(_cachedTrackData['Recent'] ?? []);
      // Suggested songs now handled by shared provider
      
      // Restore map data
      _topArtists = List<Map<String, dynamic>>.from(_cachedMapData['Top Artists'] ?? []);
      _playlists = List<Map<String, dynamic>>.from(_cachedMapData['Playlists'] ?? []);
    });
    
    print('📦 Restored cached data - Pins: ${_pins.length}, Collected: ${_collectedPins.length}, Upvoted: ${_upvotedPins.length}, Suggested: managed by shared provider');
  }

  /// Save current data to cache
  void _saveToCacheForFilter(String filter) {
    switch (filter) {
      case 'My Pins':
        _cachedPinData['My Pins'] = List<Pin>.from(_pins);
        break;
      case 'Collected':
        _cachedPinData['Collected'] = List<Pin>.from(_collectedPins);
        break;
      case 'Upvoted':
        _cachedPinData['Upvoted'] = List<Pin>.from(_upvotedPins);
        break;
      case 'Liked':
        _cachedTrackData['Liked'] = List<MusicTrack>.from(_likedSongs);
        break;
      case 'Top Tracks':
        _cachedTrackData['Top Tracks'] = List<MusicTrack>.from(_topTracks);
        break;
      case 'Recent':
        _cachedTrackData['Recent'] = List<MusicTrack>.from(_recentlyPlayed);
        break;
      case 'Suggested Songs':
        // Suggested songs now handled by shared provider
        break;
      case 'Top Artists':
        _cachedMapData['Top Artists'] = List<Map<String, dynamic>>.from(_topArtists);
        break;
      case 'Playlists':
        _cachedMapData['Playlists'] = List<Map<String, dynamic>>.from(_playlists);
        break;
    }
  }

  @override
  void didUpdateWidget(covariant MyPinsTab oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    // Check if filter changed or if the widget was rebuilt (indicating potential service change)
    if (oldWidget.currentFilter != widget.currentFilter || _lastFilter != widget.currentFilter) {
      _lastFilter = widget.currentFilter;
      _selectedPlaylist = null; // Clear selected playlist when changing filters
      
      // Reload data for the new filter
      SchedulerBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          _loadDataForCurrentFilter();
        }
      });
    }
  }

  void _loadInitialData() async {
    _loadPins();
    
    final spotifyProvider = Provider.of<SpotifyProvider>(context, listen: false);
    
    // Wait a bit for provider initialization if needed
    if (!spotifyProvider.isInitialized) {
      await Future.delayed(const Duration(milliseconds: 500));
    }
    
    if (spotifyProvider.isConnected) {
      _loadLikedSongs();
      _loadTopTracks();
    } else {
      // If not connected, still try to connect in case tokens are available
      final connected = await spotifyProvider.connect();
      if (connected) {
        _loadLikedSongs();
        _loadTopTracks();
      }
    }
    
    // Initialize shared suggested songs provider
    SuggestedSongsProvider.instance.initialize(context);
  }

  void _loadDataForCurrentFilter() {
    switch (widget.currentFilter) {
      case 'My Pins':
        if (_pins.isEmpty) _loadPins();
        break;
      case 'Collected':
        if (_collectedPins.isEmpty) _loadCollectedPins();
        break;
      case 'Upvoted':
        if (_upvotedPins.isEmpty) _loadUpvotedPins();
        break;
      case 'Liked':
        if (_likedSongs.isEmpty) _loadLikedSongs();
        break;
      case 'Top Tracks':
        if (_topTracks.isEmpty) _loadTopTracks();
        break;
      case 'Recent':
        if (_recentlyPlayed.isEmpty) _loadRecentlyPlayed();
        break;
      case 'Top Artists':
        if (_topArtists.isEmpty) _loadTopArtists();
        break;
      case 'Playlists':
        if (_playlists.isEmpty) _loadPlaylists();
        break;
      case 'Suggested Songs':
        // Use pre-fetched suggestions from background loading if available
        // Only load fresh if suggestions are empty or if we need to refresh
        if (SuggestedSongsProvider.instance.suggestedSongs.isEmpty && !SuggestedSongsProvider.instance.isLoading) {
          SuggestedSongsProvider.instance.initialize(context);
        }
        break;
    }
  }

  Future<void> _loadPins({bool isLoadingMore = false}) async {
    final filter = 'My Pins';
    if (_isLoadingPins || (_isLoadingMore[filter] == true && isLoadingMore)) return;
    
    // If not loading more and we have cached data, use it
    if (!isLoadingMore && _pins.isNotEmpty && _cachedPinData.containsKey(filter)) {
      print('📦 Using cached data for $filter (${_pins.length} items)');
      return;
    }
    
    setState(() {
      if (isLoadingMore) {
        _isLoadingMore[filter] = true;
      } else {
        _isLoadingPins = true;
        _errorMessage = null;
        _pins.clear();
        _currentPages[filter] = 1;
        _hasMoreData[filter] = true;
      }
    });
    
    try {
      final pinsService = PinsService();
      final page = isLoadingMore ? _currentPages[filter]! + 1 : 1;
      
      final userPins = await pinsService.getMyPins(
        page: page,
        pageSize: 20,
      );
      
      setState(() {
        if (isLoadingMore) {
          _pins.addAll(userPins);
          _currentPages[filter] = page;
        } else {
          _pins = userPins;
          _currentPages[filter] = 1;
        }
        _hasMoreData[filter] = userPins.length >= 20; // Has more if we got a full page
        _isLoadingPins = false;
        _isLoadingMore[filter] = false;
      });
      
      // Save to cache after successful loading
      _saveToCacheForFilter(filter);
      
      if (userPins.isNotEmpty) {
        print('✅ Loaded ${userPins.length} user pins from API (page $page) - cached');
      } else {
        print('ℹ️ No user pins found on page $page');
      }
    } catch (e) {
      print('❌ Error loading user pins: $e');
      setState(() {
        _errorMessage = 'Error loading pins: $e';
        _isLoadingPins = false;
        _isLoadingMore[filter] = false;
      });
    }
  }

  Future<void> _loadCollectedPins({bool isLoadingMore = false}) async {
    final filter = 'Collected';
    if (_isLoadingCollected || (_isLoadingMore[filter] == true && isLoadingMore)) return;
    
    // If not loading more and we have cached data, use it
    if (!isLoadingMore && _collectedPins.isNotEmpty && _cachedPinData.containsKey(filter)) {
      print('📦 Using cached data for $filter (${_collectedPins.length} items)');
      return;
    }
    
    setState(() {
      if (isLoadingMore) {
        _isLoadingMore[filter] = true;
      } else {
        _isLoadingCollected = true;
        _errorMessage = null;
        _collectedPins.clear();
        _currentPages[filter] = 1;
        _hasMoreData[filter] = true;
      }
    });
    
    try {
      final pinsService = PinsService();
      final page = isLoadingMore ? _currentPages[filter]! + 1 : 1;
      
      final collectedPins = await pinsService.getCollectedPins(
        page: page,
        pageSize: 20,
      );
      
      setState(() {
        if (isLoadingMore) {
          _collectedPins.addAll(collectedPins);
          _currentPages[filter] = page;
        } else {
          _collectedPins = collectedPins;
          _currentPages[filter] = 1;
        }
        _hasMoreData[filter] = collectedPins.length >= 20; // Has more if we got a full page
        _isLoadingCollected = false;
        _isLoadingMore[filter] = false;
      });
      
      // Save to cache after successful loading
      _saveToCacheForFilter(filter);
      
      if (collectedPins.isNotEmpty) {
        print('✅ Loaded ${collectedPins.length} collected pins from API (page $page) - cached');
      } else {
        print('ℹ️ No collected pins found on page $page');
      }
    } catch (e) {
      print('❌ Error loading collected pins: $e');
      setState(() {
        _errorMessage = 'Error loading collected pins: $e';
        _isLoadingCollected = false;
        _isLoadingMore[filter] = false;
      });
    }
  }

  Future<void> _loadUpvotedPins({bool isLoadingMore = false}) async {
    final filter = 'Upvoted';
    if (_isLoadingUpvoted || (_isLoadingMore[filter] == true && isLoadingMore)) return;
    
    // If not loading more and we have cached data, use it
    if (!isLoadingMore && _upvotedPins.isNotEmpty && _cachedPinData.containsKey(filter)) {
      print('📦 Using cached data for $filter (${_upvotedPins.length} items)');
      return;
    }
    
    setState(() {
      if (isLoadingMore) {
        _isLoadingMore[filter] = true;
      } else {
        _isLoadingUpvoted = true;
        _errorMessage = null;
        _upvotedPins.clear();
        _currentPages[filter] = 1;
        _hasMoreData[filter] = true;
      }
    });
    
    try {
      final pinsService = PinsService();
      final page = isLoadingMore ? _currentPages[filter]! + 1 : 1;
      
      final upvotedPins = await pinsService.getUpvotedPins(
        page: page,
        pageSize: 20,
      );
      
      setState(() {
        if (isLoadingMore) {
          _upvotedPins.addAll(upvotedPins);
          _currentPages[filter] = page;
        } else {
          _upvotedPins = upvotedPins;
          _currentPages[filter] = 1;
        }
        _hasMoreData[filter] = upvotedPins.length >= 20; // Has more if we got a full page
        _isLoadingUpvoted = false;
        _isLoadingMore[filter] = false;
      });
      
      // Save to cache after successful loading
      _saveToCacheForFilter(filter);
      
      if (upvotedPins.isNotEmpty) {
        print('✅ Loaded ${upvotedPins.length} upvoted pins from API (page $page) - cached');
      } else {
        print('ℹ️ No upvoted pins found on page $page');
      }
    } catch (e) {
      print('❌ Error loading upvoted pins: $e');
      setState(() {
        _errorMessage = 'Error loading upvoted pins: $e';
        _isLoadingUpvoted = false;
        _isLoadingMore[filter] = false;
      });
    }
  }

  Future<void> _loadLikedSongs() async {
    if (_isLoadingLiked) return;
    
    final filter = 'Liked';
    // If we have cached data, use it (but still sync with provider later)
    final spotifyProvider = Provider.of<SpotifyProvider>(context, listen: false);
    if (_likedSongs.isNotEmpty && _cachedTrackData.containsKey(filter) && !spotifyProvider.isConnected) {
      print('📦 Using cached data for $filter (${_likedSongs.length} items)');
      return;
    }
    
    setState(() {
      _isLoadingLiked = true;
      _errorMessage = null;
    });
    
    try {
      // If provider is not initialized yet, wait for it
      if (!spotifyProvider.isInitialized) {
        await Future.delayed(const Duration(milliseconds: 1000));
      }
      
      // Try to connect if not connected
      if (!spotifyProvider.isConnected) {
        final connected = await spotifyProvider.connect();
        if (!connected) {
          // Check if Apple Music is available as fallback
          final appleMusicProvider = Provider.of<AppleMusicProvider>(context, listen: false);
          if (!appleMusicProvider.isConnected) {
            setState(() {
              _errorMessage = 'Spotify not connected. Please authenticate first.';
              _isLoadingLiked = false;
            });
            return;
          } else {
            // Apple Music is available, don't show error
            setState(() {
              _isLoadingLiked = false;
            });
            return;
          }
        }
      }
      
      // Force refresh liked songs when loading
      await spotifyProvider.loadLikedSongs();
      
      setState(() {
        _likedSongs = spotifyProvider.likedSongs;
        _isLoadingLiked = false;
      });
      
      // Save to cache after successful loading
      _saveToCacheForFilter(filter);
      print('✅ Loaded ${_likedSongs.length} liked songs - cached');
      
    } catch (e) {
      print('Error loading liked songs: $e');
      setState(() {
        _errorMessage = 'Error loading liked songs: $e';
        _isLoadingLiked = false;
      });
    }
  }

  Future<void> _loadMoreLikedSongs() async {
    if (_isLoadingLiked) return;
    
    final spotifyProvider = Provider.of<SpotifyProvider>(context, listen: false);
    if (spotifyProvider.isLoadingLikedSongs || !spotifyProvider.hasMoreLikedSongs) return;
    
    setState(() => _isLoadingLiked = true);
    
    try {
      await spotifyProvider.loadMoreLikedSongs();
      setState(() {
        _likedSongs = spotifyProvider.likedSongs;
        _isLoadingLiked = false;
      });
    } catch (e) {
      print('Error loading more liked songs: $e');
      setState(() => _isLoadingLiked = false);
    }
  }

  void _handleScroll(ScrollNotification notification) {
    if (!mounted) return;
    
    // Check if we're near the bottom (80% of scroll extent)
    final isNearBottom = notification.metrics.pixels > notification.metrics.maxScrollExtent * 0.8;
    
    if (isNearBottom) {
      switch (widget.currentFilter) {
        case 'My Pins':
          if (_hasMoreData['My Pins'] == true && _isLoadingMore['My Pins'] != true) {
            _loadPins(isLoadingMore: true);
          }
          break;
        case 'Collected':
          if (_hasMoreData['Collected'] == true && _isLoadingMore['Collected'] != true) {
            _loadCollectedPins(isLoadingMore: true);
          }
          break;
        case 'Upvoted':
          if (_hasMoreData['Upvoted'] == true && _isLoadingMore['Upvoted'] != true) {
            _loadUpvotedPins(isLoadingMore: true);
          }
          break;
        case 'Liked':
          _loadMoreLikedSongs();
          break;
        case 'Recent':
          _loadMoreRecentlyPlayed();
          break;
        case 'Suggested Songs':
          print('📜 Scroll detected for Suggested Songs at ${(notification.metrics.pixels / notification.metrics.maxScrollExtent * 100).toStringAsFixed(1)}%');
          _loadMoreSuggestedSongs();
          break;
      }
    }
  }

  @override
  void dispose() {
    // Save current state to cache before disposal
    if (widget.currentFilter == 'My Pins' && _pins.isNotEmpty) {
      _cachedPinData['My Pins'] = List<Pin>.from(_pins);
    }
    if (widget.currentFilter == 'Collected' && _collectedPins.isNotEmpty) {
      _cachedPinData['Collected'] = List<Pin>.from(_collectedPins);
    }
    if (widget.currentFilter == 'Upvoted' && _upvotedPins.isNotEmpty) {
      _cachedPinData['Upvoted'] = List<Pin>.from(_upvotedPins);
    }
    if (widget.currentFilter == 'Suggested Songs' && SuggestedSongsProvider.instance.suggestedSongs.isNotEmpty) {
      // Suggested songs now handled by shared provider
    }
    
    // Remove provider listeners to avoid memory leaks
    _spotifyProvider?.removeListener(_onSpotifyProviderUpdated);
    super.dispose();
  }

  /// Clear all cached data (useful for refresh or logout)
  static void clearCache() {
    _cachedPinData.clear();
    _cachedTrackData.clear();
    _cachedMapData.clear();
    print('🗑️ Cleared all MyPinsTab cache');
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 360;
    return RefreshIndicator(
      onRefresh: () async {
        // Refresh data based on current filter
        switch (widget.currentFilter) {
          case 'My Pins':
            await _loadPins();
            break;
          case 'Collected':
            await _loadCollectedPins();
            break;
          case 'Upvoted':
            await _loadUpvotedPins();
            break;
          case 'Liked':
            await _loadLikedSongs();
            break;
          case 'Top Tracks':
            await _loadTopTracks();
            break;
          case 'Recent':
            await _loadRecentlyPlayed();
            break;
          case 'Top Artists':
            await _loadTopArtists();
            break;
          case 'Playlists':
            await _loadPlaylists();
            break;
          case 'Suggested Songs':
            await SuggestedSongsProvider.instance.initialize(context);
            break;
        }
      },
      child: NotificationListener<ScrollNotification>(
        onNotification: (notification) {
          _handleScroll(notification);
          return false;
        },
        child: CustomScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          slivers: [
            SliverPadding(
              padding: EdgeInsets.fromLTRB(
                isSmallScreen ? 8 : 12,
                isSmallScreen ? 6 : 8,
                isSmallScreen ? 8 : 12,
                MediaQuery.of(context).padding.bottom + (isSmallScreen ? 8 : 12),
              ),
              sliver: _buildContent(context, isSmallScreen),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContent(BuildContext context, bool isSmallScreen) {
    // Use shared widget for suggested songs
    if (widget.currentFilter == 'Suggested Songs') {
      return SliverFillRemaining(
        child: SuggestedSongsWidget(
          currentFilter: widget.currentFilter,
          isSmallScreen: isSmallScreen,
          onRefresh: () async {
            // Refresh logic for suggested songs
            setState(() {
              _errorMessage = null;
            });
          },
          onTrackPlay: (track) => _playSuggestedTrack(track),
        ),
      );
    }

    if (widget.currentFilter == 'Playlists' && _selectedPlaylist != null) {
      return SliverFillRemaining(
        child: PlaylistDetailView(
          playlist: _selectedPlaylist!,
          isSmallScreen: isSmallScreen,
          onBack: () => setState(() => _selectedPlaylist = null),
        ),
      );
    }

    if (_isLoading && _currentData.isEmpty) {
      return _buildSkeletonLoading(context, isSmallScreen);
    }
    
    if (_errorMessage != null) {
      return SliverFillRemaining(
        child: _buildErrorState(),
      );
    }
    
    if (_currentData.isEmpty) {
      return SliverFillRemaining(
        child: _buildEmptyState(),
      );
    }

    return SliverList(
      delegate: SliverChildBuilderDelegate(
        (context, index) {
          // Add shuffle header at the top for Suggested Songs
          if (widget.currentFilter == 'Suggested Songs' && index == 0 && SuggestedSongsProvider.instance.suggestedSongs.isNotEmpty) {
            return Container(
              margin: const EdgeInsets.only(bottom: 8), // Reduced from 12
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      'BOP AI Curated',
                      style: TextStyle(
                        fontSize: 14, // Reduced from 16
                        fontWeight: FontWeight.w600,
                        color: Theme.of(context).textTheme.titleMedium?.color,
                      ),
                    ),
                  ),
                  // Shuffle button with same design as playlist shuffle
                  Container(
                    margin: const EdgeInsets.only(left: 8),
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: IconButton(
                      icon: Icon(
                        Icons.shuffle_rounded,
                        size: 18, // Reduced from 20
                        color: Theme.of(context).colorScheme.primary,
                      ),
                      onPressed: SuggestedSongsProvider.instance.suggestedSongs.isNotEmpty ? _shuffleSuggestedSongs : null,
                      tooltip: 'Shuffle suggested songs',
                    ),
                  ),
                ],
              ),
            );
          }
          
          // Adjust index for Suggested Songs to account for header
          final dataIndex = widget.currentFilter == 'Suggested Songs' && SuggestedSongsProvider.instance.suggestedSongs.isNotEmpty ? index - 1 : index;
          
          if (dataIndex == _currentData.length) {
            return _buildBottomLoadingIndicator(context, isSmallScreen);
          }
          
          // Don't show items for negative indices (header case)
          if (dataIndex < 0) {
            return const SizedBox.shrink();
          }

          final item = _currentData[dataIndex];
          if (item is MusicTrack) {
            return TrackCard(
              track: item, 
              isSmallScreen: isSmallScreen,
              onPlay: widget.currentFilter == 'Suggested Songs' ? () => _playSuggestedTrack(item) : null,
            );
          } else if (item is Pin) {
            return PinCard(
              pin: _pinToMap(item), 
              isSmallScreen: isSmallScreen,
              onEngagementTap: (widget.currentFilter == 'My Pins' || widget.currentFilter == 'Collected' || widget.currentFilter == 'Upvoted')
                  ? () => _showEngagementBottomSheet(context, _pinToMap(item))
                  : null,
            );
          } else if (item is Map<String, dynamic>) {
            switch (widget.currentFilter) {
              case 'Top Artists':
                return ArtistCard(artist: item, isSmallScreen: isSmallScreen);
              case 'Playlists':
                return PlaylistCard(
                  playlist: item,
                  isSmallScreen: isSmallScreen,
                  onTap: () => setState(() => _selectedPlaylist = item),
                );
              default:
                return const SizedBox.shrink();
            }
          }
          return const SizedBox.shrink();
        },
        childCount: _currentData.length + (_shouldShowBottomLoading ? 1 : 0) + (widget.currentFilter == 'Suggested Songs' && SuggestedSongsProvider.instance.suggestedSongs.isNotEmpty ? 1 : 0), // Add 1 for shuffle header in Suggested Songs
      ),
    );
  }

  Widget _buildSkeletonLoading(BuildContext context, bool isSmallScreen) {
    return SliverList(
      delegate: SliverChildBuilderDelegate(
        (context, index) {
          switch (widget.currentFilter) {
            case 'My Pins':
            case 'Collected':
            case 'Upvoted':
              return PinCardSkeleton(isSmallScreen: isSmallScreen);
            case 'Liked':
            case 'Top Tracks':
            case 'Recent':
              return TrackCardSkeleton(isSmallScreen: isSmallScreen);
            case 'Top Artists':
              return ArtistCardSkeleton(isSmallScreen: isSmallScreen);
            case 'Playlists':
              return PlaylistCardSkeleton(isSmallScreen: isSmallScreen);
            case 'Suggested Songs':
              return TrackCardSkeleton(isSmallScreen: isSmallScreen);
            default:
              return TrackCardSkeleton(isSmallScreen: isSmallScreen);
          }
        },
        childCount: 8, // Show 8 skeleton items
      ),
    );
  }

  Widget _buildBottomLoadingIndicator(BuildContext context, bool isSmallScreen) {
    // Check if we should show loading indicator for current filter
    final shouldShowLoading = _isLoadingMore[widget.currentFilter] == true;
    final hasMore = _hasMoreData[widget.currentFilter] == true;
    
    if (!shouldShowLoading && !hasMore) {
      return const Padding(
        padding: EdgeInsets.all(16.0),
        child: Center(
          child: Text(
            'No more items to load',
            style: TextStyle(
              color: Colors.grey,
              fontSize: 14,
            ),
          ),
        ),
      );
    }
    
    if (!shouldShowLoading) {
      return const SizedBox.shrink();
    }
    
    switch (widget.currentFilter) {
      case 'My Pins':
      case 'Collected':
      case 'Upvoted':
        return PinCardSkeleton(isSmallScreen: isSmallScreen);
      case 'Liked':
      case 'Top Tracks':
      case 'Recent':
        return TrackCardSkeleton(isSmallScreen: isSmallScreen);
      case 'Top Artists':
        return ArtistCardSkeleton(isSmallScreen: isSmallScreen);
      case 'Playlists':
        return PlaylistCardSkeleton(isSmallScreen: isSmallScreen);
      case 'Suggested Songs':
        return TrackCardSkeleton(isSmallScreen: isSmallScreen);
      default:
        return TrackCardSkeleton(isSmallScreen: isSmallScreen);
    }
  }

  bool get _isLoading {
    return widget.currentFilter == 'My Pins' && _isLoadingPins ||
           widget.currentFilter == 'Collected' && _isLoadingCollected ||
           widget.currentFilter == 'Upvoted' && _isLoadingUpvoted ||
           widget.currentFilter == 'Liked' && _isLoadingLiked ||
           widget.currentFilter == 'Top Tracks' && _isLoadingTop ||
           widget.currentFilter == 'Recent' && _isLoadingRecent ||
           widget.currentFilter == 'Top Artists' && _isLoadingArtists ||
           widget.currentFilter == 'Playlists' && _isLoadingPlaylists ||
           widget.currentFilter == 'Suggested Songs' && (SuggestedSongsProvider.instance.isLoading || SuggestedSongsProvider.instance.isLoadingMore);
  }

  bool get _shouldShowBottomLoading {
    final currentFilter = widget.currentFilter;
    return _isLoadingMore[currentFilter] == true || _hasMoreData[currentFilter] == true;
  }

  dynamic get _currentData {
    switch (widget.currentFilter) {
      case 'My Pins': return _pins;
      case 'Collected': return _collectedPins;
      case 'Upvoted': return _upvotedPins;
      case 'Liked': return _likedSongs;
      case 'Top Tracks': return _topTracks;
      case 'Recent': return _recentlyPlayed;
      case 'Top Artists': return _topArtists;
      case 'Playlists': return _playlists;
      case 'Suggested Songs': return SuggestedSongsProvider.instance.suggestedSongs;
      default: return [];
    }
  }

  Widget _buildErrorState() {
    final isSpotifyError = _errorMessage!.toLowerCase().contains('spotify') || 
                          _errorMessage!.toLowerCase().contains('not connected') ||
                          _errorMessage!.toLowerCase().contains('authenticate');
    
    // Check if Apple Music is available as fallback
    final appleMusicProvider = Provider.of<AppleMusicProvider>(context, listen: false);
    final hasAppleMusicFallback = appleMusicProvider.isConnected;
    
    // If it's a Spotify error but Apple Music is available, don't show error
    if (isSpotifyError && hasAppleMusicFallback) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.info_outline,
              size: 48,
              color: Colors.blue.withOpacity(0.8),
            ),
            const SizedBox(height: 16),
            Text(
              'Using Apple Music for playback',
              style: TextStyle(
                color: Colors.blue,
                fontSize: 16,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              'Tracks will play through Apple Music',
              style: TextStyle(
                color: Colors.grey.withOpacity(0.7),
                fontSize: 14,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }
    
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            isSpotifyError ? Icons.warning_amber_rounded : Icons.error_outline,
            size: 48,
            color: isSpotifyError ? Colors.orange.withOpacity(0.8) : Colors.red.withOpacity(0.8),
          ),
          const SizedBox(height: 16),
          Text(
            _errorMessage!,
            style: TextStyle(
              color: isSpotifyError ? Colors.orange : Colors.red,
              fontSize: 16,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: _retryLoading,
            child: const Text('Retry'),
          ),
          if (isSpotifyError && !hasAppleMusicFallback) ...[
            const SizedBox(height: 12),
            TextButton(
              onPressed: _reconnectToSpotify,
              style: TextButton.styleFrom(
                foregroundColor: Colors.green,
              ),
              child: const Text('Reconnect to Spotify'),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            _getEmptyStateIcon(),
            size: 48,
            color: Colors.grey.withOpacity(0.5),
          ),
          const SizedBox(height: 16),
          Text(
            _getEmptyStateMessage(),
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey.withOpacity(0.8),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  void _retryLoading() async {
    final spotifyProvider = Provider.of<SpotifyProvider>(context, listen: false);
    
    // Clear any existing error first
    setState(() => _errorMessage = null);
    
    // If Spotify is not connected, try to connect first
    if (!spotifyProvider.isConnected) {
      final connected = await spotifyProvider.connect();
      if (!connected) {
        // Check if Apple Music is available as fallback
        final appleMusicProvider = Provider.of<AppleMusicProvider>(context, listen: false);
        if (!appleMusicProvider.isConnected) {
          setState(() => _errorMessage = 'Failed to connect to Spotify. Please authenticate first.');
          return;
        }
        // Apple Music is available, don't show error - continue with retry
      }
    }
    
    // Now retry loading the current filter's data
    switch (widget.currentFilter) {
      case 'My Pins':
        _loadPins();
        break;
      case 'Collected':
        _loadCollectedPins();
        break;
      case 'Upvoted':
        _loadUpvotedPins();
        break;
      case 'Liked':
        _loadLikedSongs();
        break;
      case 'Top Tracks':
        _loadTopTracks();
        break;
      case 'Recent':
        _loadRecentlyPlayed();
        break;
      case 'Top Artists':
        _loadTopArtists();
        break;
      case 'Playlists':
        _loadPlaylists();
        break;
      case 'Suggested Songs':
        SuggestedSongsProvider.instance.initialize(context);
        break;
    }
  }

  void _reconnectToSpotify() async {
    final spotifyProvider = Provider.of<SpotifyProvider>(context, listen: false);
    
    // Clear error and show connecting state
    setState(() => _errorMessage = null);
    
    try {
      // Attempt to authenticate with Spotify
      final authenticated = await spotifyProvider.authenticate();
      
      if (authenticated) {
        // If authentication successful, reload current filter's data
        _loadDataForCurrentFilter();
      } else {
        setState(() => _errorMessage = 'Failed to authenticate with Spotify. Please try again.');
      }
    } catch (e) {
      setState(() => _errorMessage = 'Error connecting to Spotify: $e');
    }
  }

  IconData _getEmptyStateIcon() {
    switch (widget.currentFilter) {
      case 'My Pins': return Icons.push_pin;
      case 'Collected': return Icons.collections;
      case 'Upvoted': return Icons.thumb_up;
      case 'Liked': return Icons.favorite;
      case 'Top Tracks': return Icons.trending_up;
      case 'Recent': return Icons.history;
      case 'Top Artists': return Icons.person;
      case 'Playlists': return Icons.playlist_play;
      case 'Suggested Songs': return Icons.psychology;
      default: return Icons.music_note;
    }
  }

  String _getEmptyStateMessage() {
    switch (widget.currentFilter) {
      case 'My Pins':
        return 'No pins yet.\nStart dropping some music pins!';
      case 'Collected':
        return 'No collected pins yet.\nStart collecting pins from the map!';
      case 'Upvoted':
        return 'No upvoted pins yet.\nStart upvoting pins you like!';
      case 'Liked':
        return 'No liked songs yet.\nStart liking some tracks!';
      case 'Top Tracks':
        return 'No top tracks yet.\nListen to more music!';
      case 'Recent':
        return 'No recently played tracks.\nStart listening!';
      case 'Top Artists':
        return 'No top artists yet.\nExplore more artists!';
      case 'Playlists':
        return 'No playlists yet.\nCreate your first playlist!';
      case 'Suggested Songs':
        return 'No suggestions yet.\nConnect your music service to get personalized recommendations!';
      default:
        return 'No items to display';
    }
  }

  // Load methods for other filters
  Future<void> _loadTopTracks() async {
    if (_isLoadingTop) return;
    setState(() {
      _isLoadingTop = true;
      _errorMessage = null;
    });
    
    try {
      final spotifyProvider = Provider.of<SpotifyProvider>(context, listen: false);
      
      // If provider is not initialized yet, wait for it
      if (!spotifyProvider.isInitialized) {
        await Future.delayed(const Duration(milliseconds: 1000));
      }
      
      // Try to connect if not connected
      if (!spotifyProvider.isConnected) {
        final connected = await spotifyProvider.connect();
        if (!connected) {
          // Check if Apple Music is available as fallback
          final appleMusicProvider = Provider.of<AppleMusicProvider>(context, listen: false);
          if (!appleMusicProvider.isConnected) {
            setState(() {
              _errorMessage = 'Spotify not connected. Please authenticate first.';
              _isLoadingTop = false;
            });
            return;
          } else {
            // Apple Music is available, don't show error
            setState(() {
              _isLoadingTop = false;
            });
            return;
          }
        }
      }
      
      // Use the SpotifyProvider method that delegates to SpotifyWebApiService
      final tracks = await spotifyProvider.getTopTracks(limit: 20);
      setState(() {
        _topTracks = tracks;
        _isLoadingTop = false;
      });
    } catch (e) {
      print('Error loading top tracks: $e');
      setState(() {
        _errorMessage = 'Error loading top tracks: $e';
        _isLoadingTop = false;
      });
    }
  }

  Future<void> _loadRecentlyPlayed() async {
    if (_isLoadingRecent) return;
    setState(() {
      _isLoadingRecent = true;
      _errorMessage = null;
    });
    
    try {
      final spotifyProvider = Provider.of<SpotifyProvider>(context, listen: false);
      
      // If provider is not initialized yet, wait for it
      if (!spotifyProvider.isInitialized) {
        await Future.delayed(const Duration(milliseconds: 1000));
      }
      
      // Try to connect if not connected
      if (!spotifyProvider.isConnected) {
        final connected = await spotifyProvider.connect();
        if (!connected) {
          // Check if Apple Music is available as fallback
          final appleMusicProvider = Provider.of<AppleMusicProvider>(context, listen: false);
          if (!appleMusicProvider.isConnected) {
            setState(() {
              _errorMessage = 'Spotify not connected. Please authenticate first.';
              _isLoadingRecent = false;
            });
            return;
          } else {
            // Apple Music is available, don't show error
            setState(() {
              _isLoadingRecent = false;
            });
            return;
          }
        }
      }
      
      // Load recently played tracks with pagination support
      await spotifyProvider.loadRecentlyPlayed(refresh: true);
      
      setState(() {
        _recentlyPlayed = spotifyProvider.recentlyPlayedTracks;
        _isLoadingRecent = false;
      });
    } catch (e) {
      print('Error loading recently played tracks: $e');
      setState(() {
        _errorMessage = 'Error loading recently played tracks: $e';
        _isLoadingRecent = false;
      });
    }
  }

  Future<void> _loadMoreRecentlyPlayed() async {
    if (_isLoadingRecent) return;
    
    final spotifyProvider = Provider.of<SpotifyProvider>(context, listen: false);
    if (spotifyProvider.isLoadingRecentlyPlayed || !spotifyProvider.hasMoreRecentlyPlayed) return;
    
    setState(() => _isLoadingRecent = true);
    
    try {
      await spotifyProvider.loadMoreRecentlyPlayed();
      setState(() {
        _recentlyPlayed = spotifyProvider.recentlyPlayedTracks;
        _isLoadingRecent = false;
      });
    } catch (e) {
      print('Error loading more recently played tracks: $e');
      setState(() => _isLoadingRecent = false);
    }
  }

  Future<void> _loadMoreSuggestedSongs() async {

    
    await SuggestedSongsProvider.instance.loadMore(context);
  }

  Future<void> _loadTopArtists() async {
    if (_isLoadingArtists) return;
    setState(() {
      _isLoadingArtists = true;
      _errorMessage = null;
    });
    
    try {
      final spotifyProvider = Provider.of<SpotifyProvider>(context, listen: false);
      
      // If provider is not initialized yet, wait for it
      if (!spotifyProvider.isInitialized) {
        await Future.delayed(const Duration(milliseconds: 1000));
      }
      
      // Try to connect if not connected
      if (!spotifyProvider.isConnected) {
        final connected = await spotifyProvider.connect();
        if (!connected) {
          // Check if Apple Music is available as fallback
          final appleMusicProvider = Provider.of<AppleMusicProvider>(context, listen: false);
          if (!appleMusicProvider.isConnected) {
            setState(() {
              _errorMessage = 'Spotify not connected. Please authenticate first.';
              _isLoadingArtists = false;
            });
            return;
          } else {
            // Apple Music is available, don't show error
            setState(() {
              _isLoadingArtists = false;
            });
            return;
          }
        }
      }
      
      final artists = await spotifyProvider.getTopArtists();
      setState(() {
        _topArtists = artists;
        _isLoadingArtists = false;
      });
    } catch (e) {
      print('Error loading top artists: $e');
      setState(() {
        _errorMessage = 'Error loading top artists: $e';
        _isLoadingArtists = false;
      });
    }
  }

  // Old suggested songs methods removed - now handled by shared provider

  Future<void> _loadPlaylists() async {
    if (_isLoadingPlaylists) return;
    setState(() {
      _isLoadingPlaylists = true;
      _errorMessage = null;
    });
    
    try {
      final spotifyProvider = Provider.of<SpotifyProvider>(context, listen: false);
      
      // If provider is not initialized yet, wait for it
      if (!spotifyProvider.isInitialized) {
        await Future.delayed(const Duration(milliseconds: 1000));
      }
      
      // Try to connect if not connected
      if (!spotifyProvider.isConnected) {
        final connected = await spotifyProvider.connect();
        if (!connected) {
          setState(() {
            _errorMessage = 'Spotify not connected. Please authenticate first.';
            _isLoadingPlaylists = false;
          });
          return;
        }
      }
      
      final playlists = await spotifyProvider.getUserPlaylistsFromApi();
      
      // Get current user's profile to determine ownership
      final userProfile = spotifyProvider.userProfile;
      final currentUserId = userProfile?['id'] as String?;
      
      // Enhanced sorting logic to identify and prioritize "pinned" playlists
      final sortedPlaylists = List<Map<String, dynamic>>.from(playlists);
      sortedPlaylists.sort((a, b) {
        final aOwnerId = a['owner']?['id'] as String?;
        final bOwnerId = b['owner']?['id'] as String?;
        
        final aIsOwned = currentUserId != null && aOwnerId == currentUserId;
        final bIsOwned = currentUserId != null && bOwnerId == currentUserId;
        
        // Check for "pinned" indicators
        final aIsCollaborative = a['collaborative'] == true;
        final bIsCollaborative = b['collaborative'] == true;
        
        final aIsPublic = a['public'] == true;
        final bIsPublic = b['public'] == true;
        
        // Get track counts for activity indication
        final aTrackCount = a['tracks']?['total'] as int? ?? 0;
        final bTrackCount = b['tracks']?['total'] as int? ?? 0;
        
        // Check for recently modified playlists (if snapshot_id suggests recent activity)
        final aSnapshot = a['snapshot_id'] as String? ?? '';
        final bSnapshot = b['snapshot_id'] as String? ?? '';
        
        // Priority 1: Collaborative playlists (these are often "pinned" by users)
        if (aIsCollaborative && !bIsCollaborative) return -1;
        if (!aIsCollaborative && bIsCollaborative) return 1;
        
        // Priority 2: Owned playlists before followed playlists
        if (aIsOwned && !bIsOwned) return -1;
        if (!aIsOwned && bIsOwned) return 1;
        
        // Priority 3: For owned playlists, prioritize by activity and type
        if (aIsOwned && bIsOwned) {
          // 3a: Public playlists first (often more curated/important)
          if (aIsPublic && !bIsPublic) return -1;
          if (!aIsPublic && bIsPublic) return 1;
          
          // 3b: Playlists with more tracks (suggests more curation/importance)
          if (aTrackCount > 10 && bTrackCount <= 10) return -1;
          if (aTrackCount <= 10 && bTrackCount > 10) return 1;
          
          // 3c: More recent snapshots (suggests recent activity)
          if (aSnapshot.isNotEmpty && bSnapshot.isNotEmpty) {
            final comparison = bSnapshot.compareTo(aSnapshot); // Reverse for newer first
            if (comparison != 0) return comparison;
          }
        }
        
        // Priority 4: For followed playlists, prioritize collaborative ones
        if (!aIsOwned && !bIsOwned) {
          if (aIsCollaborative && !bIsCollaborative) return -1;
          if (!aIsCollaborative && bIsCollaborative) return 1;
        }
        
        // Priority 5: Sort by name alphabetically within each category
        final aName = a['name'] as String? ?? '';
        final bName = b['name'] as String? ?? '';
        return aName.toLowerCase().compareTo(bName.toLowerCase());
      });
      
      setState(() {
        _playlists = sortedPlaylists;
        _isLoadingPlaylists = false;
      });
    } catch (e) {
      print('Error loading playlists: $e');
      setState(() {
        _errorMessage = 'Error loading playlists: $e';
        _isLoadingPlaylists = false;
      });
    }
  }

  /// Callback when SpotifyProvider notifies listeners. Ensures that once the
  /// user finishes connecting to Spotify, the liked songs (and other data)
  /// refresh automatically without requiring manual pull-to-refresh.
  void _onAIProviderUpdated() {
    return;
    // AI provider callback removed - now using shared provider
  }

  void _onSpotifyProviderUpdated() {
    if (!mounted || _spotifyProvider == null) return;

    // Detect connection state change from false -> true
    if (_spotifyProvider!.isConnected && !_wasSpotifyConnected) {
      _wasSpotifyConnected = true;

      // Reload data relevant to Spotify now that we have a connection
      if (widget.currentFilter == 'Liked') {
        _loadLikedSongs();
      } else if (widget.currentFilter == 'Top Tracks') {
        _loadTopTracks();
      } else if (widget.currentFilter == 'Recent') {
        _loadRecentlyPlayed();
      } else if (widget.currentFilter == 'Top Artists') {
        _loadTopArtists();
      } else if (widget.currentFilter == 'Playlists') {
        _loadPlaylists();
      }
    }

    // If liked songs list in provider has changed and currently showing Liked filter,
    // update local copy so UI refreshes automatically.
    if (widget.currentFilter == 'Liked' &&
        _likedSongs.length != _spotifyProvider!.likedSongs.length) {
      setState(() {
        _likedSongs = List<MusicTrack>.from(_spotifyProvider!.likedSongs);
      });
    }
    
    // If recently played tracks list in provider has changed and currently showing Recent filter,
    // update local copy so UI refreshes automatically.
    if (widget.currentFilter == 'Recent' &&
        _recentlyPlayed.length != _spotifyProvider!.recentlyPlayedTracks.length) {
      setState(() {
        _recentlyPlayed = List<MusicTrack>.from(_spotifyProvider!.recentlyPlayedTracks);
      });
    }
  }

  void _playSuggestedTrack(MusicTrack track) async {
    try {

      final appleMusicProvider = Provider.of<AppleMusicProvider>(context, listen: false);
      final youtubeProvider = Provider.of<YouTubeProvider>(context, listen: false);
      
      // Create a mutable map from track data
      final trackData = {
        ...track.toJson(), // Convert track to map
        // Explicitly set duration_ms if available
        'duration_ms': track.durationMs,
      };
      
      // Always try Apple Music first (bypass connection check like collection_detail_screen.dart)
      bool success = false;

      try {
        if (kDebugMode) {
          print('🎵 [MyPinsTab] Attempting Apple Music playback for: ${track.title}');
        }

        // Use queue manager directly like the working implementation
        final queueManager = appleMusicProvider.queueManager;
        success = await queueManager.setQueue(
          tracks: [track],
          collectionType: 'single_track',
          startIndex: 0,
        );

        if (kDebugMode) {
          print('🎵 [MyPinsTab] Apple Music queue result: success=$success');
        }
      } catch (e) {
        if (kDebugMode) {
          print('🎵 [MyPinsTab] Apple Music queue failed: $e');
        }
        success = false;
      }

      // Fallback to YouTube if Apple Music failed
      if (!success) {
        if (kDebugMode) {
          print('🎵 [MyPinsTab] Apple Music failed, trying YouTube fallback...');
        }
        if(!youtubeProvider.isInitialized) await youtubeProvider.initialize();
        await youtubeProvider.playTrack(track);
      } else {
        if (kDebugMode) {
          print('✅ [MyPinsTab] Successfully played track on Apple Music: ${track.title}');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error playing track: $e');
      }
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error playing track: $e'),
            backgroundColor: Colors.red[600],
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }

  /// Shuffle the suggested songs and start playing
  Future<void> _shuffleSuggestedSongs() async {
    final suggestedSongs = SuggestedSongsProvider.instance.suggestedSongs;
    if (suggestedSongs.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('No suggested songs to shuffle')),
      );
      return;
    }

    try {
      // Create a shuffled copy of the suggested songs
      final shuffledTracks = SuggestedSongsProvider.instance.getShuffledSongs();

      // Play the first shuffled track
      if (shuffledTracks.isNotEmpty) {
        _playSuggestedTrack(shuffledTracks.first);
        
        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Shuffling ${shuffledTracks.length} suggested songs'),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      print('❌ Error shuffling suggested songs: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to shuffle songs: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _showEngagementBottomSheet(BuildContext context, Map<String, dynamic> pin) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      barrierColor: Colors.black.withOpacity(0.5),
      builder: (context) => TrackEngagementBottomSheet(pin: pin),
    );
  }
}