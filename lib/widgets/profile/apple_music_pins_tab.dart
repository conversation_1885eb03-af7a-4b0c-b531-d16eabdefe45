import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:provider/provider.dart';
import 'package:flutter/foundation.dart';
import 'package:music_kit/music_kit.dart';
import '../../providers/apple_music_provider.dart';
import '../../providers/spotify_provider.dart';
import '../../services/music/apple_music_service.dart';
import '../../services/api/pins_service.dart';
import '../../services/api_service.dart';
import '../../services/auth_service.dart';
import '../../models/music_track.dart';
import '../../models/pin.dart';
import 'components/pin_card.dart';
import 'components/track_card.dart';
import 'components/playlist_card.dart';
import 'components/playlist_detail_view.dart';
import 'components/album_card.dart';
import 'components/album_detail_view.dart';
import 'components/track_card_skeleton.dart';
import 'components/pin_card_skeleton.dart';
import 'components/playlist_card_skeleton.dart';
import 'components/track_engagement_bottom_sheet.dart';
import 'components/suggested_songs_widget.dart';
import '../../providers/suggested_songs_provider.dart';
import '../../services/ai/global_ai_provider_service.dart';
import 'dart:async';

class AppleMusicPinsTab extends StatefulWidget {
  final String currentFilter;
  final ValueChanged<String> onFilterSelected;

  const AppleMusicPinsTab(
      {Key? key, required this.currentFilter, required this.onFilterSelected})
      : super(key: key);

  @override
  State<AppleMusicPinsTab> createState() => _AppleMusicPinsTabState();
}

class _AppleMusicPinsTabState extends State<AppleMusicPinsTab>
    with AutomaticKeepAliveClientMixin, TickerProviderStateMixin {
  Map<String, dynamic>? _selectedPlaylist;
  Map<String, dynamic>? _selectedAlbum;

  // Static cache for persistent data storage across widget rebuilds
  static final Map<String, List<Pin>> _cachedPinData = {};
  static final Map<String, List<MusicTrack>> _cachedTrackData = {};
  static final Map<String, List<Map<String, dynamic>>> _cachedMapData = {};
  static final PageStorageBucket _bucket = PageStorageBucket();

  // Add scroll controller for managing scroll position
  late final ScrollController _scrollController;

  // Animation controllers for smooth transitions
  late final AnimationController _fadeAnimationController;
  late final AnimationController _slideAnimationController;
  late final Animation<double> _fadeAnimation;
  late final Animation<Offset> _slideAnimation;

  // Track the current filter for animation purposes
  String _currentAnimatedFilter = '';

  // Data lists for different filters
  List<Pin> _pins = [];
  List<Pin> _collectedPins = [];
  List<Pin> _upvotedPins = [];
  List<MusicTrack> _librarySongs = [];
  List<Map<String, dynamic>> _recentlyAdded = [];
  List<Map<String, dynamic>> _heavyRotation = [];
  List<MusicTrack> _recentlyPlayed = [];
  List<Map<String, dynamic>> _playlists = [];
  // Suggested songs now handled by shared provider

  // Loading states
  bool _isLoadingPins = false;
  bool _isLoadingCollected = false;
  bool _isLoadingUpvoted = false;
  bool _isLoadingLibrary = false;
  bool _isLoadingRecentlyAdded = false;
  bool _isLoadingHeavyRotation = false;
  bool _isLoadingRecentlyPlayed = false;
  bool _isLoadingPlaylists = false;
  bool _isLoadingSuggested = false;

  // Pagination states for each filter
  Map<String, String?> _nextUrls = {
    'Pins': null,
    'Collected': null,
    'Upvoted': null,
    'Liked Songs': null,
    'Recently Added': null,
    'Heavy Rotation': null,
    'Recently Played': null,
    'Playlists': null,
    'Suggested Songs': null,
  };

  Map<String, bool> _hasMoreData = {
    'Pins': true,
    'Collected': true,
    'Upvoted': true,
    'Liked Songs': true,
    'Recently Added': true,
    'Heavy Rotation': true,
    'Recently Played': true,
    'Playlists': true,
    'Suggested Songs': true,
  };

  Map<String, bool> _isLoadingMore = {
    'Pins': false,
    'Collected': false,
    'Upvoted': false,
    'Liked Songs': false,
    'Recently Added': false,
    'Heavy Rotation': false,
    'Recently Played': false,
    'Playlists': false,
    'Suggested Songs': false,
  };

  // Add page tracking for pin tabs (similar to my_pins_tab.dart)
  Map<String, int> _currentPages = {
    'Pins': 1,
    'Collected': 1,
    'Upvoted': 1,
  };

  // Error states
  String? _errorMessage;

  // Apple Music service
  late final AppleMusicService _appleMusicService;

  // MusicKit instance and subscription states
  final MusicKit _musicKitPlugin = MusicKit();
  MusicAuthorizationStatus _authStatus =
      MusicAuthorizationStatusNotDetermined();
  String? _developerToken = '';
  String _userToken = '';
  String _countryCode = '';

  MusicSubscription _musicSubscription = const MusicSubscription();
  late StreamSubscription<MusicSubscription>
      _musicSubscriptionStreamSubscription;

  MusicPlayerState? _playerState;
  late StreamSubscription<MusicPlayerState> _playerStateStreamSubscription;

  MusicPlayerQueue? _playerQueue;
  late StreamSubscription<MusicPlayerQueue> _playerQueueStreamSubscription;

  @override
  bool get wantKeepAlive => true;

  /// Scroll to top of the list with smooth animation
  void _scrollToTop() {
    if (_scrollController.hasClients) {
      _scrollController.animateTo(
        0.0,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOutCubic,
      );
    }
  }

  /// Convert Pin object to Map format for PinCard widget
  Map<String, dynamic> _pinToMap(Pin pin) {
    return {
      'id': pin.id,
      'songTitle': pin.trackTitle,
      'artist': pin.trackArtist,
      'location': pin.locationName ?? 'Unknown Location',
      'date': _formatDate(pin.createdAt),
      'genre': pin.album ?? 'Music',
      'image': pin.artworkUrl,
      'service': pin.service,
      'likes': pin.interactionCount['like'] ?? 0,
      'comments': pin.interactionCount['comment'] ?? 0,
      'plays': pin.interactionCount['view'] ?? 0,
      // Include the new engagement_counts structure from the backend
      'engagement_counts': pin.engagementCounts,
      'uri': pin.trackUrl,
      'caption': pin.caption,
      'title': pin.title,
      'isPrivate': pin.isPrivate,
      'rarity': pin.rarity,
      'skin': pin.skin,
      'auraRadius': pin.auraRadius,
    };
  }

  /// Format date for display
  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays > 0) {
      return '${difference.inDays} day${difference.inDays > 1 ? 's' : ''} ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} hour${difference.inHours > 1 ? 's' : ''} ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} minute${difference.inMinutes > 1 ? 's' : ''} ago';
    } else {
      return 'Just now';
    }
  }

  @override
  void initState() {
    super.initState();

    // Initialize scroll controller
    _scrollController = ScrollController();

    // Restore cached data first
    _restoreCachedData();

    _fadeAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _slideAnimationController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeAnimationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0.3, 0.0),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideAnimationController,
      curve: Curves.easeOutCubic,
    ));

    // Start with the current filter
    _currentAnimatedFilter = widget.currentFilter;
    _fadeAnimationController.value = 1.0;
    _slideAnimationController.value = 1.0;

    // Load initial data after a delay to prevent blocking UI
    SchedulerBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        // Initialize the service with required dependencies
        _appleMusicService = AppleMusicService(
          ApiService(),
          AuthService(ApiService()),
        );

        // Set up MusicKit subscription listeners
        _setupMusicKitSubscriptions();

        // Check provider connection state and sync authorization status
        _syncAuthorizationState();

        _loadInitialData();
      }
    });
  }

  /// Restore cached data from static storage
  void _restoreCachedData() {
    setState(() {
      // Restore pin data
      _pins = List<Pin>.from(_cachedPinData['Pins'] ?? []);
      _collectedPins = List<Pin>.from(_cachedPinData['Collected'] ?? []);
      _upvotedPins = List<Pin>.from(_cachedPinData['Upvoted'] ?? []);

      // Restore track data
      _librarySongs =
          List<MusicTrack>.from(_cachedTrackData['Liked Songs'] ?? []);
      _recentlyPlayed =
          List<MusicTrack>.from(_cachedTrackData['Recently Played'] ?? []);
      // Suggested songs now handled by shared provider

      // Restore map data
      _recentlyAdded = List<Map<String, dynamic>>.from(
          _cachedMapData['Recently Added'] ?? []);
      _heavyRotation = List<Map<String, dynamic>>.from(
          _cachedMapData['Heavy Rotation'] ?? []);
      _playlists =
          List<Map<String, dynamic>>.from(_cachedMapData['Playlists'] ?? []);
    });

    print(
        '📦 [Apple Music] Restored cached data - Pins: ${_pins.length}, Collected: ${_collectedPins.length}, Library: ${_librarySongs.length}, Suggested: managed by shared provider');
  }

  /// Save current data to cache
  void _saveToCacheForFilter(String filter) {
    switch (filter) {
      case 'Pins':
        _cachedPinData['Pins'] = List<Pin>.from(_pins);
        break;
      case 'Collected':
        _cachedPinData['Collected'] = List<Pin>.from(_collectedPins);
        break;
      case 'Upvoted':
        _cachedPinData['Upvoted'] = List<Pin>.from(_upvotedPins);
        break;
      case 'Liked Songs':
        _cachedTrackData['Liked Songs'] = List<MusicTrack>.from(_librarySongs);
        break;
      case 'Recently Played':
        _cachedTrackData['Recently Played'] =
            List<MusicTrack>.from(_recentlyPlayed);
        break;
      case 'Suggested Songs':
        // Suggested songs now handled by shared provider
        break;
      case 'Recently Added':
        _cachedMapData['Recently Added'] =
            List<Map<String, dynamic>>.from(_recentlyAdded);
        break;
      case 'Heavy Rotation':
        _cachedMapData['Heavy Rotation'] =
            List<Map<String, dynamic>>.from(_heavyRotation);
        break;
      case 'Playlists':
        _cachedMapData['Playlists'] =
            List<Map<String, dynamic>>.from(_playlists);
        break;
    }
  }

  /// Set up MusicKit subscriptions
  void _setupMusicKitSubscriptions() {
    if (kDebugMode) {
      print('🍎 [AppleMusicPinsTab] Setting up MusicKit subscriptions...');
    }

    _musicSubscriptionStreamSubscription =
        _musicKitPlugin.onSubscriptionUpdated.listen((event) {
      if (kDebugMode) {
        print('🍎 [AppleMusicPinsTab] Music subscription updated: $event');
      }
      setState(() {
        _musicSubscription = event;
      });
    });

    _playerStateStreamSubscription =
        _musicKitPlugin.onMusicPlayerStateChanged.listen((event) {
      if (kDebugMode) {
        print(
            '🍎 [AppleMusicPinsTab] Player state changed: ${event.playbackStatus.toString()}');
      }
      setState(() {
        _playerState = event;
      });
    });

    _playerQueueStreamSubscription =
        _musicKitPlugin.onPlayerQueueChanged.listen((event) {
      if (kDebugMode) {
        print('🍎 [AppleMusicPinsTab] Player queue changed');
      }
      setState(() {
        _playerQueue = event;
      });
    });
  }

  /// Sync authorization state with Apple Music provider
  Future<void> _syncAuthorizationState() async {
    try {
      final appleMusicProvider = Provider.of<AppleMusicProvider>(context, listen: false);

      if (kDebugMode) {
        print('🍎 [AppleMusicPinsTab] Syncing authorization state...');
        print('🍎 [AppleMusicPinsTab] Provider connected: ${appleMusicProvider.isConnected}');
      }

      // If provider is already connected, check the actual MusicKit authorization status
      if (appleMusicProvider.isConnected) {
        final status = await _musicKitPlugin.authorizationStatus;
        if (kDebugMode) {
          print('🍎 [AppleMusicPinsTab] MusicKit authorization status: ${status.toString()}');
        }

        setState(() {
          _authStatus = status;
        });

        // If authorized, complete the platform state initialization
        if (status is MusicAuthorizationStatusAuthorized) {
          await _initializePlatformState();
        }
      } else {
        // Provider not connected, check if we have stored authorization
        final status = await _musicKitPlugin.authorizationStatus;
        if (kDebugMode) {
          print('🍎 [AppleMusicPinsTab] Provider not connected, MusicKit status: ${status.toString()}');
        }

        setState(() {
          _authStatus = status;
        });

        // If we have authorization but provider isn't connected, try to connect
        if (status is MusicAuthorizationStatusAuthorized) {
          if (kDebugMode) {
            print('🍎 [AppleMusicPinsTab] Have authorization but provider not connected, attempting to connect...');
          }

          final connected = await appleMusicProvider.connect();
          if (connected && mounted) {
            await _initializePlatformState();
          }
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ [AppleMusicPinsTab] Error syncing authorization state: $e');
      }
      setState(() {
        _errorMessage = 'Failed to sync Apple Music authorization: $e';
      });
    }
  }

  void _initializeTab() {
    SchedulerBinding.instance.addPostFrameCallback((_) {
      if (!mounted) return;
      _initializePlatformState();
      _loadInitialData();
      _applyFilter();

      // Add listener to the Apple Music provider for automatic updates
      final appleMusicProvider =
          Provider.of<AppleMusicProvider>(context, listen: false);
      appleMusicProvider.addListener(_onProviderUpdate);

      if (kDebugMode) {
        print('🍎 [AppleMusicPinsTab] Tab initialization completed');
      }
    });
  }

  /// Initialize platform state
  Future<void> _initializePlatformState() async {
    if (kDebugMode) {
      print('🍎 [AppleMusicPinsTab] Initializing platform state...');
    }

    try {
      final status = await _musicKitPlugin.authorizationStatus;
      if (kDebugMode) {
        print(
            '🍎 [AppleMusicPinsTab] Authorization status: ${status.toString()}');
      }

      switch (status) {
        case MusicAuthorizationStatusInitial():
        case MusicAuthorizationStatusDenied():
        case MusicAuthorizationStatusNotDetermined():
        case MusicAuthorizationStatusRestricted():
          setState(() {
            _authStatus = status;
          });
          return;
        case MusicAuthorizationStatusAuthorized():
          break;
      }

      final developerToken = await _musicKitPlugin.requestDeveloperToken();

      if (_musicSubscription.canBecomeSubscriber == true) {
        return;
      }

      final userToken = await _musicKitPlugin.requestUserToken(developerToken);

      // Send the user token to the backend
      if (userToken.isNotEmpty) {
        bool tokenSentSuccessfully =
            await _appleMusicService.sendUserTokenToBackend(userToken);
        if (kDebugMode) {
          print(
              '🍎 [AppleMusicPinsTab] Token sent to backend: $tokenSentSuccessfully');
        }
      }

      final countryCode = await _musicKitPlugin.currentCountryCode;

      if (!mounted) return;

      setState(() {
        _authStatus = status;
        _developerToken = developerToken;
        _userToken = userToken;
        _countryCode = countryCode;
      });

      if (kDebugMode) {
        print(
            '🍎 [AppleMusicPinsTab] Platform state initialization completed successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ [AppleMusicPinsTab] Error initializing platform state: $e');
      }
      setState(() {
        _errorMessage = 'Failed to initialize Apple Music: $e';
      });
    }
  }

  Future<void> _loadInitialData() async {
    if (kDebugMode) {
      print('🍎 [AppleMusicPinsTab] Loading initial data...');
    }

    await _loadPins();
    await _loadCollectedPins();
    await _loadUpvotedPins();

    // Initialize suggested songs provider with proper initialization order
    await _initializeSuggestedSongs();

    if (!mounted) return;

    final appleMusicProvider =
        Provider.of<AppleMusicProvider>(context, listen: false);

    if (appleMusicProvider.isConnected) {
      _loadLibrarySongs();
      _loadPlaylists();
    } else {
      try {
        final connected = await appleMusicProvider.connect();
        if (connected && mounted) {
          _loadLibrarySongs();
          _loadPlaylists();
        }
      } catch (e) {
        setState(() {
          _errorMessage = 'Failed to connect to Apple Music: ${e.toString()}';
        });
      }
    }
  }

  /// Initialize suggested songs with proper order
  Future<void> _initializeSuggestedSongs() async {
    try {
      if (kDebugMode) {
        print('🍎 [AppleMusicPinsTab] Initializing suggested songs...');
      }

      // First ensure the global AI provider is initialized
      await GlobalAIProviderService.instance.initializeIfAuthenticated(context);

      if (!mounted) return;

      // Get the AI provider and ensure it has artist-based data
      final aiProvider = GlobalAIProviderService.instance.aiProvider;
      if (aiProvider != null) {
        // If the provider doesn't have artist-based data, trigger loading
        if (aiProvider.currentRecommendations.isEmpty || aiProvider.currentCategory != 'artistBased') {
          if (kDebugMode) {
            print('🍎 [AppleMusicPinsTab] Triggering artist-based recommendations loading...');
          }

          // Switch to artist-based category to trigger loading
          aiProvider.onCategoryChanged('artistBased', context);

          // Wait for the data to load
          await Future.delayed(const Duration(milliseconds: 2000));

          if (!mounted) return;
        }
      }

      // Then initialize the suggested songs provider
      await SuggestedSongsProvider.instance.initialize(context);

      if (kDebugMode) {
        print('✅ [AppleMusicPinsTab] Suggested songs initialization completed');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ [AppleMusicPinsTab] Error initializing suggested songs: $e');
      }
    }
  }

  /// Reset pagination state for a filter
  void _resetPaginationForFilter(String filter) {
    _nextUrls[filter] = null;
    _hasMoreData[filter] = true;
    _isLoadingMore[filter] = false;
    // Reset page tracking for pin tabs
    if (_currentPages.containsKey(filter)) {
      _currentPages[filter] = 1;
    }
  }

  Future<void> _loadPins({bool isLoadingMore = false}) async {
    final filter = 'Pins';
    if (_isLoadingPins || (_isLoadingMore[filter] == true && isLoadingMore))
      return;

    // If not loading more and we have cached data, use it
    if (!isLoadingMore &&
        _pins.isNotEmpty &&
        _cachedPinData.containsKey(filter)) {
      print(
          '📦 [Apple Music] Using cached data for $filter (${_pins.length} items)');
      return;
    }

    setState(() {
      if (isLoadingMore) {
        _isLoadingMore[filter] = true;
      } else {
        _isLoadingPins = true;
        _errorMessage = null;
        _pins.clear();
        _resetPaginationForFilter(filter);
      }
    });

    try {
      final pinsService = PinsService();
      final page = isLoadingMore ? _currentPages[filter]! + 1 : 1;

      // Load all user pins without filtering by service
      final userPins = await pinsService.getMyPins(
        page: page,
        pageSize: 20,
      );

      setState(() {
        if (isLoadingMore) {
          _pins.addAll(userPins);
          _currentPages[filter] = page;
        } else {
          _pins = userPins;
          _currentPages[filter] = 1;
        }
        _hasMoreData[filter] =
            userPins.length >= 20; // Has more if we got a full page
        _isLoadingPins = false;
        _isLoadingMore[filter] = false;
      });

      // Save to cache after successful loading
      _saveToCacheForFilter(filter);

      if (userPins.isNotEmpty) {
        if (kDebugMode) {
          print(
              '✅ [Apple Music] Loaded ${userPins.length} user pins from API (page $page) - cached');
        }
      } else {
        if (kDebugMode) {
          print('ℹ️ No user pins found on page $page');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error loading user pins: $e');
      }
      setState(() {
        _errorMessage = 'Error loading pins: $e';
        _isLoadingPins = false;
        _isLoadingMore[filter] = false;
      });
    }
  }

  Future<void> _loadCollectedPins({bool isLoadingMore = false}) async {
    final filter = 'Collected';
    if (_isLoadingCollected ||
        (_isLoadingMore[filter] == true && isLoadingMore)) return;

    setState(() {
      if (isLoadingMore) {
        _isLoadingMore[filter] = true;
      } else {
        _isLoadingCollected = true;
        _errorMessage = null;
        _collectedPins.clear();
        _resetPaginationForFilter(filter);
      }
    });

    try {
      final pinsService = PinsService();
      final page = isLoadingMore ? _currentPages[filter]! + 1 : 1;

      // Load collected pins
      final collectedPins = await pinsService.getCollectedPins(
        page: page,
        pageSize: 20,
      );

      setState(() {
        if (isLoadingMore) {
          _collectedPins.addAll(collectedPins);
          _currentPages[filter] = page;
        } else {
          _collectedPins = collectedPins;
          _currentPages[filter] = 1;
        }
        _hasMoreData[filter] =
            collectedPins.length >= 20; // Has more if we got a full page
        _isLoadingCollected = false;
        _isLoadingMore[filter] = false;
      });

      if (collectedPins.isNotEmpty) {
        if (kDebugMode) {
          print(
              '✅ Loaded ${collectedPins.length} collected pins from API (page $page)');
        }
      } else {
        if (kDebugMode) {
          print('ℹ️ No collected pins found on page $page');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error loading collected pins: $e');
      }
      setState(() {
        _errorMessage = 'Error loading collected pins: $e';
        _isLoadingCollected = false;
        _isLoadingMore[filter] = false;
      });
    }
  }

  Future<void> _loadUpvotedPins({bool isLoadingMore = false}) async {
    final filter = 'Upvoted';
    if (_isLoadingUpvoted || (_isLoadingMore[filter] == true && isLoadingMore))
      return;

    setState(() {
      if (isLoadingMore) {
        _isLoadingMore[filter] = true;
      } else {
        _isLoadingUpvoted = true;
        _errorMessage = null;
        _upvotedPins.clear();
        _resetPaginationForFilter(filter);
      }
    });

    try {
      final pinsService = PinsService();
      final page = isLoadingMore ? _currentPages[filter]! + 1 : 1;

      // Load upvoted pins
      final upvotedPins = await pinsService.getUpvotedPins(
        page: page,
        pageSize: 20,
      );

      setState(() {
        if (isLoadingMore) {
          _upvotedPins.addAll(upvotedPins);
          _currentPages[filter] = page;
        } else {
          _upvotedPins = upvotedPins;
          _currentPages[filter] = 1;
        }
        _hasMoreData[filter] =
            upvotedPins.length >= 20; // Has more if we got a full page
        _isLoadingUpvoted = false;
        _isLoadingMore[filter] = false;
      });

      if (upvotedPins.isNotEmpty) {
        if (kDebugMode) {
          print(
              '✅ Loaded ${upvotedPins.length} upvoted pins from API (page $page)');
        }
      } else {
        if (kDebugMode) {
          print('ℹ️ No upvoted pins found on page $page');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error loading upvoted pins: $e');
      }
      setState(() {
        _errorMessage = 'Error loading upvoted pins: $e';
        _isLoadingUpvoted = false;
        _isLoadingMore[filter] = false;
      });
    }
  }

  Future<void> _loadLibrarySongs({bool isLoadingMore = false}) async {
    final filter = 'Liked Songs';
    if (_isLoadingLibrary || (_isLoadingMore[filter] == true && isLoadingMore))
      return;

    setState(() {
      if (isLoadingMore) {
        _isLoadingMore[filter] = true;
      } else {
        _isLoadingLibrary = true;
        _errorMessage = null;
        _librarySongs.clear();
        _resetPaginationForFilter(filter);
      }
    });

    try {
      final response = await _appleMusicService.getLibrarySongsPaginated(
        nextUrl: isLoadingMore ? _nextUrls[filter] : null,
        limit: 25,
      );

      setState(() {
        if (isLoadingMore) {
          _librarySongs.addAll(response.data);
        } else {
          _librarySongs = response.data;
        }
        _nextUrls[filter] = response.nextUrl;
        _hasMoreData[filter] = response.hasMore;
        _isLoadingLibrary = false;
        _isLoadingMore[filter] = false;
      });
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error loading library songs: $e');
      }
      setState(() {
        _errorMessage = 'Error loading library songs: $e';
        _isLoadingLibrary = false;
        _isLoadingMore[filter] = false;
      });
    }
  }

  Future<void> _loadPlaylists({bool isLoadingMore = false}) async {
    final filter = 'Playlists';
    if (_isLoadingPlaylists ||
        (_isLoadingMore[filter] == true && isLoadingMore)) return;

    setState(() {
      if (isLoadingMore) {
        _isLoadingMore[filter] = true;
      } else {
        _isLoadingPlaylists = true;
        _errorMessage = null;
        _playlists.clear();
        _resetPaginationForFilter(filter);
      }
    });

    try {
      final response = await _appleMusicService.getPlaylistsPaginated(
        nextUrl: isLoadingMore ? _nextUrls[filter] : null,
        limit: 25,
      );

      // Filter out unknown or deleted playlists
      final validPlaylists = response.data.where((playlist) {
        final name = playlist['name'] as String?;
        final id = playlist['id'] as String?;

        // Filter out playlists that might be deleted or unknown
        return name != null &&
            name.isNotEmpty &&
            name != 'Unknown Playlist' &&
            id != null &&
            id.isNotEmpty;
      }).toList();

      setState(() {
        if (isLoadingMore) {
          _playlists.addAll(validPlaylists);
        } else {
          _playlists = validPlaylists;
        }
        _nextUrls[filter] = response.nextUrl;
        _hasMoreData[filter] = response.hasMore;
        _isLoadingPlaylists = false;
        _isLoadingMore[filter] = false;
      });
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error loading playlists: $e');
      }
      setState(() {
        _errorMessage = 'Error loading playlists: $e';
        _isLoadingPlaylists = false;
        _isLoadingMore[filter] = false;
      });
    }
  }

  Future<void> _loadRecentlyAdded({bool isLoadingMore = false}) async {
    final filter = 'Recently Added';
    if (_isLoadingRecentlyAdded ||
        (_isLoadingMore[filter] == true && isLoadingMore)) return;

    setState(() {
      if (isLoadingMore) {
        _isLoadingMore[filter] = true;
      } else {
        _isLoadingRecentlyAdded = true;
        _errorMessage = null;
        _recentlyAdded.clear();
        _resetPaginationForFilter(filter);
      }
    });

    try {
      final response = await _appleMusicService.getRecentlyAddedPaginated(
        nextUrl: isLoadingMore ? _nextUrls[filter] : null,
        limit: 25,
      );

      setState(() {
        if (isLoadingMore) {
          _recentlyAdded.addAll(response.data);
        } else {
          _recentlyAdded = response.data;
        }
        _nextUrls[filter] = response.nextUrl;
        _hasMoreData[filter] = response.hasMore;
        _isLoadingRecentlyAdded = false;
        _isLoadingMore[filter] = false;
      });
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error loading recently added: $e');
      }
      setState(() {
        _errorMessage = 'Error loading recently added: $e';
        _isLoadingRecentlyAdded = false;
        _isLoadingMore[filter] = false;
      });
    }
  }

  Future<void> _loadHeavyRotation({bool isLoadingMore = false}) async {
    final filter = 'Heavy Rotation';
    if (_isLoadingHeavyRotation ||
        (_isLoadingMore[filter] == true && isLoadingMore)) return;

    setState(() {
      if (isLoadingMore) {
        _isLoadingMore[filter] = true;
      } else {
        _isLoadingHeavyRotation = true;
        _errorMessage = null;
        _heavyRotation.clear();
        _resetPaginationForFilter(filter);
      }
    });

    try {
      final response = await _appleMusicService.getHeavyRotationPaginated(
        nextUrl: isLoadingMore ? _nextUrls[filter] : null,
        limit: 25,
      );

      setState(() {
        if (isLoadingMore) {
          _heavyRotation.addAll(response.data);
        } else {
          _heavyRotation = response.data;
        }
        _nextUrls[filter] = response.nextUrl;
        _hasMoreData[filter] = response.hasMore;
        _isLoadingHeavyRotation = false;
        _isLoadingMore[filter] = false;
      });
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error loading heavy rotation: $e');
      }
      setState(() {
        // Don't show error for empty data, just log it
        if (e.toString().contains('empty data')) {
          if (kDebugMode) {
            print(
                'ℹ️ Heavy rotation is empty - this is normal for users with limited listening history');
          }
        } else {
          _errorMessage = 'Error loading heavy rotation: $e';
        }
        _isLoadingHeavyRotation = false;
        _isLoadingMore[filter] = false;
      });
    }
  }

  Future<void> _loadRecentlyPlayed({bool isLoadingMore = false}) async {
    final filter = 'Recently Played';
    if (_isLoadingRecentlyPlayed ||
        (_isLoadingMore[filter] == true && isLoadingMore)) return;

    setState(() {
      if (isLoadingMore) {
        _isLoadingMore[filter] = true;
      } else {
        _isLoadingRecentlyPlayed = true;
        _errorMessage = null;
        _recentlyPlayed.clear();
        _resetPaginationForFilter(filter);
      }
    });

    try {
      final response = await _appleMusicService.getRecentlyPlayedPaginated(
        nextUrl: isLoadingMore ? _nextUrls[filter] : null,
        limit: 25,
      );

      setState(() {
        if (isLoadingMore) {
          _recentlyPlayed.addAll(response.data);
        } else {
          _recentlyPlayed = response.data;
        }
        _nextUrls[filter] = response.nextUrl;
        _hasMoreData[filter] = response.hasMore;
        _isLoadingRecentlyPlayed = false;
        _isLoadingMore[filter] = false;
      });
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error loading recently played: $e');
      }
      setState(() {
        _errorMessage = 'Error loading recently played: $e';
        _isLoadingRecentlyPlayed = false;
        _isLoadingMore[filter] = false;
      });
    }
  }

  // Suggested songs methods removed - now handled by shared provider

  Future<void> _loadMoreSuggestedSongs() async {
    await SuggestedSongsProvider.instance.loadMore(context);
  }

  /// Shuffle the suggested songs and start playing
  Future<void> _shuffleSuggestedSongs() async {
    final suggestedSongs = SuggestedSongsProvider.instance.suggestedSongs;
    if (suggestedSongs.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('No suggested songs to shuffle')),
      );
      return;
    }

    try {
      // Create a shuffled copy of the suggested songs
      final shuffledTracks = SuggestedSongsProvider.instance.getShuffledSongs();

      // Play the shuffled tracks via Apple Music using queue manager
      if (shuffledTracks.isNotEmpty) {
        final appleMusicProvider =
            Provider.of<AppleMusicProvider>(context, listen: false);
        if (appleMusicProvider.isConnected) {
          // Use queue manager for consistent behavior
          final queueManager = appleMusicProvider.queueManager;
          await queueManager.setQueue(
            tracks: shuffledTracks,
            collectionType: 'suggested_songs_shuffle',
            startIndex: 0,
            collectionMetadata: {
              'name': 'Suggested Songs Shuffle',
              'shuffled': true,
            },
          );
        }

        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Shuffling ${shuffledTracks.length} suggested songs'),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      print('❌ Error shuffling suggested songs: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to shuffle songs: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  // Scroll notification handler for pagination
  bool _handleScrollNotification(ScrollNotification notification) {
    if (!mounted) return false;
    // Load more data when reaching 80% of scroll extent
    if (notification.metrics.pixels >
        notification.metrics.maxScrollExtent * 0.8) {
      _loadMoreData();
    }
    return false;
  }

  Future<void> _loadMoreData() async {
    final currentFilter = widget.currentFilter;

    // Check if we can load more data for current filter
    if (_hasMoreData[currentFilter] == false ||
        _isLoadingMore[currentFilter] == true) {
      return;
    }

    // Load more data based on current filter
    switch (currentFilter) {
      case 'Pins':
        await _loadPins(isLoadingMore: true);
        break;
      case 'Collected':
        await _loadCollectedPins(isLoadingMore: true);
        break;
      case 'Upvoted':
        await _loadUpvotedPins(isLoadingMore: true);
        break;
      case 'Liked Songs':
        await _loadLibrarySongs(isLoadingMore: true);
        break;
      case 'Heavy Rotation':
        await _loadHeavyRotation(isLoadingMore: true);
        break;
      case 'Recently Added':
        await _loadRecentlyAdded(isLoadingMore: true);
        break;
      case 'Recently Played':
        await _loadRecentlyPlayed(isLoadingMore: true);
        break;
      case 'Playlists':
        await _loadPlaylists(isLoadingMore: true);
        break;
      case 'Suggested Songs':
        await _loadMoreSuggestedSongs();
        break;
    }
  }

  void _onProviderUpdate() {
    if (!mounted) return;

    final appleMusicProvider =
        Provider.of<AppleMusicProvider>(context, listen: false);

    // Update library songs if they've changed and we're not actively managing pagination for that filter
    if (appleMusicProvider.likedSongs != _librarySongs &&
        widget.currentFilter != 'Liked Songs' &&
        !_isLoadingLibrary) {
      setState(() {
        _librarySongs = appleMusicProvider.likedSongs;
        _isLoadingLibrary = false;
      });
    }

    // Update playlists if they've changed and we're not actively managing pagination for that filter
    if (appleMusicProvider.playlists != _playlists &&
        widget.currentFilter != 'Playlists' &&
        !_isLoadingPlaylists) {
      setState(() {
        _playlists = appleMusicProvider.playlists;
        _isLoadingPlaylists = false;
      });
    }

    // If provider connection state changed, sync authorization state
    if (appleMusicProvider.isConnected &&
        (_authStatus is MusicAuthorizationStatusNotDetermined ||
         _authStatus is MusicAuthorizationStatusDenied ||
         _authStatus is MusicAuthorizationStatusRestricted)) {
      if (kDebugMode) {
        print('🍎 [AppleMusicPinsTab] Provider connected but auth status outdated, syncing...');
      }
      _syncAuthorizationState();
    }
  }

  /// Play a track using Apple Music with collection context
  Future<void> _playAppleMusicTrack(
    MusicTrack track, {
    String? collectionType,
    String? collectionId,
    List<MusicTrack>? collectionTracks,
    int? trackIndex,
  }) async {
    try {
      final appleMusicProvider =
          Provider.of<AppleMusicProvider>(context, listen: false);

      // Check if provider is connected, if not try to reconnect
      if (!appleMusicProvider.isConnected) {
        if (kDebugMode) {
          print(
              '🍎 [AppleMusicPinsTab] Apple Music not connected, attempting to connect...');
        }

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Row(
              children: [
                SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(
                      strokeWidth: 2, color: Colors.white),
                ),
                SizedBox(width: 12),
                Text('Connecting to Apple Music...'),
              ],
            ),
            backgroundColor: Colors.orange,
            duration: Duration(seconds: 3),
          ),
        );

        final connected = await appleMusicProvider.connect();
        if (!connected) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text(
                  'Failed to connect to Apple Music. Please check your settings.'),
              backgroundColor: Colors.red,
              duration: Duration(seconds: 4),
            ),
          );
          return;
        }
      }

      if (kDebugMode) {
        print(
            '🍎 [AppleMusicPinsTab] User clicked on track: ${track.title} by ${track.artist}');
        if (collectionType != null) {
          print(
              '🍎 [AppleMusicPinsTab] Collection context: $collectionType${collectionId != null ? ' ($collectionId)' : ''}');
          if (trackIndex != null) {
            print(
                '🍎 [AppleMusicPinsTab] Starting at track index: $trackIndex');
          }
        }
        if (appleMusicProvider.isPlaying) {
          print(
              '🍎 [AppleMusicPinsTab] Something is currently playing - will replace with selected track');
        }
      }

      bool success = false;

      // Play the entire collection if context is provided
      if (collectionType != null &&
          collectionTracks != null &&
          trackIndex != null) {
        switch (collectionType) {
          case 'playlist':
            if (collectionId != null) {
              success = await appleMusicProvider.playPlaylist(
                playlistId: collectionId,
                startIndex: trackIndex,
              );
            }
            break;
          case 'album':
            if (collectionId != null) {
              success = await appleMusicProvider.playAlbum(
                albumId: collectionId,
                startIndex: trackIndex,
              );
            }
            break;
          case 'liked_songs':
            success = await appleMusicProvider.playLikedSongs(
              startIndex: trackIndex,
            );
            break;
          case 'recommendations':
            success = await appleMusicProvider.playRecommendations(
              tracks: collectionTracks,
              startIndex: trackIndex,
            );
            break;
          case 'recently_played':
          case 'heavy_rotation':
          case 'recently_added':
            success = await appleMusicProvider.playCollection(
              tracks: collectionTracks,
              collectionType: collectionType,
              startIndex: trackIndex,
            );
            break;
          case 'pins':
          case 'collected_pins':
          case 'upvoted_pins':
            // Use queue manager for pin collections
            final queueManager = appleMusicProvider.queueManager;
            success = await queueManager.setQueue(
              tracks: collectionTracks,
              collectionType: collectionType,
              startIndex: trackIndex,
            );
            break;
          default:
            // Fallback to single track playback using queue manager
            final queueManager = appleMusicProvider.queueManager;
            success = await queueManager.setQueue(
              tracks: [track],
              collectionType: 'single_track',
              startIndex: 0,
            );
        }
      } else {
        // Single track playback using queue manager
        final queueManager = appleMusicProvider.queueManager;
        success = await queueManager.setQueue(
          tracks: [track],
          collectionType: 'single_track',
          startIndex: 0,
        );
      }

      // If Apple Music failed, try Spotify fallback
      if (!success && mounted) {
        if (kDebugMode) {
          print(
              '❌ [AppleMusicPinsTab] Apple Music failed, trying Spotify fallback...');
        }

        success = await _trySpotifyFallback(track,
            appleMusicProvider.errorMessage ?? 'Apple Music playback failed');
      }

      if (!success && mounted) {
        // Both services failed, show appropriate error
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
                'Failed to play "${track.title}" on any available service'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 4),
          ),
        );
      } else if (mounted && success) {
        // Don't show duplicate success message - the fallback method handles this
        if (kDebugMode) {
          print('✅ [AppleMusicPinsTab] Successfully started playback');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print(
            '❌ [AppleMusicPinsTab] Error playing Apple Music track: ${track.title}');
        print('❌ [AppleMusicPinsTab] Error details: $e');
        print('❌ [AppleMusicPinsTab] Collection type: $collectionType');
        print('❌ [AppleMusicPinsTab] Track index: $trackIndex');
      }

      if (mounted) {
        // Provide more specific error messages
        String errorMessage = 'Error playing track';

        if (e.toString().toLowerCase().contains('unauthorized') ||
            e.toString().toLowerCase().contains('401')) {
          errorMessage =
              'Apple Music authentication expired. Please reconnect in Settings.';
        } else if (e.toString().toLowerCase().contains('subscription')) {
          errorMessage = 'Apple Music subscription required to play this track';
        } else if (e.toString().toLowerCase().contains('not found')) {
          errorMessage = 'Track not available on Apple Music';
        } else {
          errorMessage =
              'Error playing track: ${e.toString().split(':').last.trim()}';
        }

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(errorMessage),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 4),
          ),
        );
      }
    }
  }

  /// Get the collection context for the current filter
  Map<String, dynamic> _getCollectionContext(int trackIndex) {
    switch (widget.currentFilter) {
      case 'Liked Songs':
        return {
          'collectionType': 'liked_songs',
          'collectionTracks': _librarySongs,
          'trackIndex': trackIndex,
        };
      case 'Heavy Rotation':
        // Convert heavy rotation items to tracks for collection playback
        final tracks = _heavyRotation
            .where((item) => item['type'] == 'song' || item['type'] == 'songs')
            .map((item) => _convertItemToTrack(item))
            .where((track) => track != null)
            .cast<MusicTrack>()
            .toList();
        return {
          'collectionType': 'heavy_rotation',
          'collectionTracks': tracks,
          'trackIndex': trackIndex,
        };
      case 'Recently Added':
        // Convert recently added items to tracks for collection playback
        final tracks = _recentlyAdded
            .where((item) => item['type'] == 'song' || item['type'] == 'songs')
            .map((item) => _convertItemToTrack(item))
            .where((track) => track != null)
            .cast<MusicTrack>()
            .toList();
        return {
          'collectionType': 'recently_added',
          'collectionTracks': tracks,
          'trackIndex': trackIndex,
        };
      case 'Recently Played':
        return {
          'collectionType': 'recently_played',
          'collectionTracks': _recentlyPlayed,
          'trackIndex': trackIndex,
        };
      case 'Suggested Songs':
        return {
          'collectionType': 'suggested_songs',
          'collectionTracks': SuggestedSongsProvider.instance.suggestedSongs,
          'trackIndex': trackIndex,
        };
      case 'Pins':
        // Convert pins to tracks for collection playback
        final tracks = _pins
            .map((pin) => _convertPinToMusicTrack(pin))
            .where((track) => track != null)
            .cast<MusicTrack>()
            .toList();
        return {
          'collectionType': 'pins',
          'collectionTracks': tracks,
          'trackIndex': trackIndex,
        };
      case 'Collected':
        // Convert collected pins to tracks for collection playback
        final tracks = _collectedPins
            .map((pin) => _convertPinToMusicTrack(pin))
            .where((track) => track != null)
            .cast<MusicTrack>()
            .toList();
        return {
          'collectionType': 'collected_pins',
          'collectionTracks': tracks,
          'trackIndex': trackIndex,
        };
      case 'Upvoted':
        // Convert upvoted pins to tracks for collection playback
        final tracks = _upvotedPins
            .map((pin) => _convertPinToMusicTrack(pin))
            .where((track) => track != null)
            .cast<MusicTrack>()
            .toList();
        return {
          'collectionType': 'upvoted_pins',
          'collectionTracks': tracks,
          'trackIndex': trackIndex,
        };

      default:
        return {};
    }
  }

  /// Convert a dynamic item to MusicTrack for collection playback
  MusicTrack? _convertItemToTrack(Map<String, dynamic> item) {
    try {
      // Handle standardized format
      if (item.containsKey('name') && item.containsKey('artist')) {
        return MusicTrack(
          id: item['id'] ?? '',
          title: item['name'] ?? 'Unknown Title',
          artist: item['artist'] ?? 'Unknown Artist',
          album: item['album'] ?? '',
          albumArt: item['image_url'] ?? '',
          url: '',
          service: 'apple_music',
          serviceType: 'apple',
          genres: [],
          durationMs: 0,
          popularity: 80,
          uri: 'apple:track:${item['id']}',
          isLibrary: true,
        );
      }

      // Handle old format
      final trackData = item['data'] ?? item;
      if (trackData is Map<String, dynamic>) {
        return MusicTrack.fromJson(trackData);
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error converting item to track: $e');
      }
    }
    return null;
  }

  /// Convert a Pin object to MusicTrack for collection playback
  MusicTrack? _convertPinToMusicTrack(Pin pin) {
    try {
      return MusicTrack(
        id: pin.id.toString(),
        title: pin.trackTitle,
        artist: pin.trackArtist,
        album: pin.album ?? '',
        albumArt: pin.artworkUrl ?? '',
        url: pin.trackUrl,
        service: pin.service,
        serviceType: pin.service == 'apple_music' || pin.service == 'apple' ? 'apple' : 'spotify',
        genres: pin.genre != null ? [pin.genre!] : [],
        durationMs: pin.durationMs ?? 0,
        popularity: 80,
        uri: _constructPinTrackUri(pin),
      );
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error converting pin to track: $e');
      }
      return null;
    }
  }

  /// Construct proper track URI from pin data
  String _constructPinTrackUri(Pin pin) {
    final service = pin.service.toLowerCase();

    if (service.contains('apple')) {
      // For Apple Music, try to extract ID from URL or use pin ID
      if (pin.trackUrl.contains('music.apple.com')) {
        final parts = pin.trackUrl.split('/');
        final idIndex = parts.indexWhere((part) => part == 'album' || part == 'song');
        if (idIndex != -1 && idIndex + 1 < parts.length) {
          final id = parts[idIndex + 1].split('?').first;
          return 'apple:track:$id';
        }
      }
      return 'apple:track:${pin.id}';
    } else if (service.contains('spotify')) {
      // For Spotify, try to extract ID from URL
      if (pin.trackUrl.contains('spotify.com/track/')) {
        final id = pin.trackUrl.split('/track/').last.split('?').first;
        return 'spotify:track:$id';
      } else if (pin.trackUrl.startsWith('spotify:track:')) {
        return pin.trackUrl;
      }
      return 'spotify:track:${pin.id}';
    }

    return pin.trackUrl;
  }

  @override
  void dispose() {
    // Save current state to cache before disposal
    if (widget.currentFilter == 'Pins' && _pins.isNotEmpty) {
      _cachedPinData['Pins'] = List<Pin>.from(_pins);
    }
    if (widget.currentFilter == 'Collected' && _collectedPins.isNotEmpty) {
      _cachedPinData['Collected'] = List<Pin>.from(_collectedPins);
    }
    if (widget.currentFilter == 'Upvoted' && _upvotedPins.isNotEmpty) {
      _cachedPinData['Upvoted'] = List<Pin>.from(_upvotedPins);
    }
    if (widget.currentFilter == 'Suggested Songs' &&
        SuggestedSongsProvider.instance.suggestedSongs.isNotEmpty) {
      // Suggested songs now handled by shared provider
    }
    if (widget.currentFilter == 'Liked Songs' && _librarySongs.isNotEmpty) {
      _cachedTrackData['Liked Songs'] = List<MusicTrack>.from(_librarySongs);
    }

    _fadeAnimationController.dispose();
    _slideAnimationController.dispose();
    _scrollController.dispose();

    // Cancel any active subscriptions to prevent memory leaks
    _musicSubscriptionStreamSubscription.cancel();
    _playerStateStreamSubscription.cancel();
    _playerQueueStreamSubscription.cancel();

    super.dispose();
  }

  /// Clear all cached data (useful for refresh or logout)
  static void clearCache() {
    _cachedPinData.clear();
    _cachedTrackData.clear();
    _cachedMapData.clear();
    print('🗑️ [Apple Music] Cleared all AppleMusicPinsTab cache');
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Consumer<AppleMusicProvider>(
      builder: (context, appleMusicProvider, child) {
        return _buildContent();
      },
    );
  }

  void _applyFilter() {
    setState(() {
      _selectedPlaylist = null;
      _selectedAlbum = null;
    });

    // Scroll to top when switching tabs
    _scrollToTop();

    // Animate the filter change
    _animateFilterChange();

    SchedulerBinding.instance.addPostFrameCallback((_) {
      if (!mounted) return;

      switch (widget.currentFilter) {
        case 'Pins':
          if (_pins.isEmpty || _nextUrls['Pins'] == null) _loadPins();
          break;
        case 'Collected':
          if (_collectedPins.isEmpty || _nextUrls['Collected'] == null)
            _loadCollectedPins();
          break;
        case 'Upvoted':
          if (_upvotedPins.isEmpty || _nextUrls['Upvoted'] == null)
            _loadUpvotedPins();
          break;
        case 'Liked Songs':
          if (_librarySongs.isEmpty || _nextUrls['Liked Songs'] == null)
            _loadLibrarySongs();
          break;
        case 'Heavy Rotation':
          if (_heavyRotation.isEmpty || _nextUrls['Heavy Rotation'] == null)
            _loadHeavyRotation();
          break;
        case 'Recently Added':
          if (_recentlyAdded.isEmpty || _nextUrls['Recently Added'] == null)
            _loadRecentlyAdded();
          break;
        case 'Playlists':
          if (_playlists.isEmpty || _nextUrls['Playlists'] == null)
            _loadPlaylists();
          break;
        case 'Recently Played':
          if (_recentlyPlayed.isEmpty || _nextUrls['Recently Played'] == null)
            _loadRecentlyPlayed();
          break;
        case 'Suggested Songs':
          // Use pre-fetched suggestions from background loading if available
          // Only load fresh if suggestions are empty or if we need to refresh
          if (SuggestedSongsProvider.instance.suggestedSongs.isEmpty &&
              !SuggestedSongsProvider.instance.isLoading) {
            _initializeSuggestedSongs();
          }
          break;
      }
    });
  }

  Widget _buildContent() {
    // Check both provider connection state and authorization status
    final appleMusicProvider = Provider.of<AppleMusicProvider>(context, listen: false);

    // Show authorization content if:
    // 1. Provider is not connected AND authorization status indicates no access
    // 2. OR authorization status explicitly denies access
    bool shouldShowAuth = false;

    if (!appleMusicProvider.isConnected) {
      // If provider not connected, check authorization status
      if (_authStatus is MusicAuthorizationStatusNotDetermined ||
          _authStatus is MusicAuthorizationStatusDenied ||
          _authStatus is MusicAuthorizationStatusRestricted) {
        shouldShowAuth = true;
      }
    } else {
      // If provider is connected but authorization status is denied/restricted, still show auth
      if (_authStatus is MusicAuthorizationStatusDenied ||
          _authStatus is MusicAuthorizationStatusRestricted) {
        shouldShowAuth = true;
      }
    }

    if (shouldShowAuth) {
      return _buildAuthorizationContent();
    }

    // If a playlist is selected, show the playlist detail view with slide animation
    if (_selectedPlaylist != null) {
      return SlideTransition(
        position: _slideAnimation,
        child: PlaylistDetailView(
          playlist: _selectedPlaylist!,
          isSmallScreen: MediaQuery.of(context).size.width < 360,
          onBack: () async {
            await _animateDetailViewClose();
            setState(() {
              _selectedPlaylist = null;
            });
          },
        ),
      );
    }

    // If an album is selected, show the album detail view with slide animation
    if (_selectedAlbum != null) {
      return SlideTransition(
        position: _slideAnimation,
        child: AlbumDetailView(
          album: _selectedAlbum!,
          isSmallScreen: MediaQuery.of(context).size.width < 360,
          service: 'apple_music',
          onBack: () async {
            await _animateDetailViewClose();
            setState(() {
              _selectedAlbum = null;
            });
          },
        ),
      );
    }

    // Main content with fade animation for category switching
    return FadeTransition(
      opacity: _fadeAnimation,
      child: AnimatedSwitcher(
        duration: const Duration(milliseconds: 250),
        transitionBuilder: (child, animation) {
          return FadeTransition(
            opacity: animation,
            child: SlideTransition(
              position: Tween<Offset>(
                begin: const Offset(0.1, 0.0),
                end: Offset.zero,
              ).animate(CurvedAnimation(
                parent: animation,
                curve: Curves.easeOutCubic,
              )),
              child: child,
            ),
          );
        },
        child: _buildMainContent(),
      ),
    );
  }

  Widget _buildMainContent() {
    // Use filter as key to trigger AnimatedSwitcher
    return Container(
      key: ValueKey(_currentAnimatedFilter),
      child: _buildScrollableContent(),
    );
  }

  Widget _buildScrollableContent() {
    if (_isLoading && _currentData.isEmpty) {
      return _buildSkeletonLoading();
    }

    if (_errorMessage != null) {
      return _buildErrorContent();
    }

    if (_currentData.isEmpty) {
      return _buildEmptyContent(_getEmptyStateMessage());
    }

    // Use shared widget for suggested songs
    if (widget.currentFilter == 'Suggested Songs') {
      return SuggestedSongsWidget(
        currentFilter: widget.currentFilter,
        isSmallScreen: MediaQuery.of(context).size.width < 360,
        onRefresh: () async {
          _applyFilter();
        },
        onTrackPlay: (track) async {
          // Handle track play for Apple Music using queue manager
          final appleMusicProvider =
              Provider.of<AppleMusicProvider>(context, listen: false);
          if (appleMusicProvider.isConnected) {
            final queueManager = appleMusicProvider.queueManager;
            await queueManager.setQueue(
              tracks: [track],
              collectionType: 'single_track',
              startIndex: 0,
            );
          }
        },
      );
    }

    return RefreshIndicator(
      onRefresh: () async {
        _applyFilter();
      },
      child: NotificationListener<ScrollNotification>(
        onNotification: _handleScrollNotification,
        child: ListView.builder(
          controller: _scrollController,
          padding: const EdgeInsets.symmetric(vertical: 8.0),
          itemCount: _currentData.length +
              (_shouldShowBottomLoading ? 1 : 0) +
              (widget.currentFilter == 'Suggested Songs' &&
                      SuggestedSongsProvider.instance.suggestedSongs.isNotEmpty
                  ? 1
                  : 0), // Add 1 for shuffle header in Suggested Songs
          itemBuilder: (context, index) {
            // Add shuffle header at the top for Suggested Songs
            if (widget.currentFilter == 'Suggested Songs' &&
                index == 0 &&
                SuggestedSongsProvider.instance.suggestedSongs.isNotEmpty) {
              return Container(
                margin: const EdgeInsets.fromLTRB(
                    16, 8, 16, 8), // Reduced bottom margin from 12 to 8
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        'BOP AI Curated',
                        style: TextStyle(
                          fontSize: 14, // Reduced from 16
                          fontWeight: FontWeight.w600,
                          color: Theme.of(context).textTheme.titleMedium?.color,
                        ),
                      ),
                    ),
                    // Shuffle button with same design as playlist shuffle
                    Container(
                      margin: const EdgeInsets.only(left: 8),
                      decoration: BoxDecoration(
                        color: Theme.of(context)
                            .colorScheme
                            .primary
                            .withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: IconButton(
                        icon: Icon(
                          Icons.shuffle_rounded,
                          size: 18, // Reduced from 20
                          color: Theme.of(context).colorScheme.primary,
                        ),
                        onPressed: SuggestedSongsProvider
                                .instance.suggestedSongs.isNotEmpty
                            ? _shuffleSuggestedSongs
                            : null,
                        tooltip: 'Shuffle suggested songs',
                      ),
                    ),
                  ],
                ),
              );
            }

            // Adjust index for Suggested Songs to account for header
            final dataIndex = widget.currentFilter == 'Suggested Songs' &&
                    SuggestedSongsProvider.instance.suggestedSongs.isNotEmpty
                ? index - 1
                : index;

            // Show loading indicator at the end if loading more
            if (dataIndex == _currentData.length) {
              if (_isLoadingMore[widget.currentFilter] == true) {
                return _buildBottomSkeletonLoading();
              } else if (_hasMoreData[widget.currentFilter] == false) {
                return const Padding(
                  padding: EdgeInsets.all(16.0),
                  child: Center(
                    child: Text(
                      'No more items to load',
                      style: TextStyle(
                        color: Colors.grey,
                        fontSize: 14,
                      ),
                    ),
                  ),
                );
              }
              return const SizedBox.shrink();
            }

            // Don't show items for negative indices (header case)
            if (dataIndex < 0) {
              return const SizedBox.shrink();
            }

            final item = _currentData[dataIndex];
            return _buildItemCard(item);
          },
        ),
      ),
    );
  }

  Widget _buildItemCard(dynamic item) {
    final isSmallScreen = MediaQuery.of(context).size.width < 360;

    if (item is Pin) {
      // For pin tabs, we need to handle collection playback
      if (widget.currentFilter == 'Pins' ||
          widget.currentFilter == 'Collected' ||
          widget.currentFilter == 'Upvoted') {
        // Find the index of this pin in the current data for collection playback
        final pinIndex = _currentData.indexOf(item);
        final collectionContext = _getCollectionContext(pinIndex);

        return PinCard(
          pin: _pinToMap(item),
          isSmallScreen: isSmallScreen,
          onEngagementTap: () => _showEngagementBottomSheet(context, _pinToMap(item)),
          onPlay: () {
            // Convert the pin to a track and play with collection context
            final track = _convertPinToMusicTrack(item);
            if (track != null) {
              if (kDebugMode) {
                print('🎵 [AppleMusicPinsTab] Playing pin with collection context:');
                print('  - Pin: "${track.title}" by "${track.artist}" (service: ${track.service})');
                print('  - Collection Type: ${collectionContext['collectionType']}');
                print('  - Collection Size: ${collectionContext['collectionTracks']?.length ?? 0}');
                print('  - Track Index: ${collectionContext['trackIndex']}');
              }
              _playAppleMusicTrack(
                track,
                collectionType: collectionContext['collectionType'],
                collectionTracks: collectionContext['collectionTracks'],
                trackIndex: collectionContext['trackIndex'],
              );
            }
          },
        );
      } else {
        return PinCard(
          pin: _pinToMap(item),
          isSmallScreen: isSmallScreen,
          onEngagementTap: null,
        );
      }
    } else if (item is MusicTrack) {
      // Find the index of this track in the current data for collection playback
      final trackIndex = _currentData.indexOf(item);
      final collectionContext = _getCollectionContext(trackIndex);

      return TrackCard(
        track: item,
        isSmallScreen: isSmallScreen,
        onTap: () => _playAppleMusicTrack(
          item,
          collectionType: collectionContext['collectionType'],
          collectionTracks: collectionContext['collectionTracks'],
          trackIndex: collectionContext['trackIndex'],
        ),
      );
    } else if (item is Map<String, dynamic>) {
      final itemType = item['type'] as String?;

      switch (widget.currentFilter) {
        case 'Pins':
          return PinCard(pin: item, isSmallScreen: isSmallScreen);
        case 'Playlists':
          return PlaylistCard(
            playlist: item,
            isSmallScreen: isSmallScreen,
            onTap: () => _openPlaylist(item),
          );
        case 'Recently Added':
        case 'Heavy Rotation':

          // Handle different item types from heavy rotation and recently added
          if (itemType == 'song' || itemType == 'songs') {
            // For transformed song data, create MusicTrack from the standardized format
            if (item.containsKey('name') && item.containsKey('artist')) {
              final track = MusicTrack(
                id: item['id'] ?? '',
                title: item['name'] ?? 'Unknown Title',
                artist: item['artist'] ?? 'Unknown Artist',
                album: item['album'] ?? '',
                albumArt: item['image_url'] ?? '',
                url: '',
                service: 'apple_music',
                serviceType: 'apple',
                genres: [],
                durationMs: 0,
                popularity: 80,
                uri: 'apple:track:${item['id']}',
                isLibrary: true,
              );

              // Find the index of this track for collection playback
              final trackIndex = _currentData.indexOf(item);
              final collectionContext = _getCollectionContext(trackIndex);

              return TrackCard(
                track: track,
                isSmallScreen: isSmallScreen,
                onTap: () => _playAppleMusicTrack(
                  track,
                  collectionType: collectionContext['collectionType'],
                  collectionTracks: collectionContext['collectionTracks'],
                  trackIndex: collectionContext['trackIndex'],
                ),
              );
            } else {
              // Fallback to old format
              final trackData = item['data'] ?? item;
              if (trackData is Map<String, dynamic>) {
                final track = MusicTrack.fromJson(trackData);

                // Find the index of this track for collection playback
                final trackIndex = _currentData.indexOf(item);
                final collectionContext = _getCollectionContext(trackIndex);

                return TrackCard(
                  track: track,
                  isSmallScreen: isSmallScreen,
                  onTap: () => _playAppleMusicTrack(
                    track,
                    collectionType: collectionContext['collectionType'],
                    collectionTracks: collectionContext['collectionTracks'],
                    trackIndex: collectionContext['trackIndex'],
                  ),
                );
              }
            }
          } else if (itemType == 'album' || itemType == 'albums') {
            // Use AlbumCard for albums
            return AlbumCard(
              album: item,
              isSmallScreen: isSmallScreen,
              onTap: () => _openAlbum(item),
            );
          } else if (itemType == 'playlist' || itemType == 'playlists') {
            // Handle playlist items - use the standardized format we created
            return PlaylistCard(
              playlist: item, // Use the transformed item directly
              isSmallScreen: isSmallScreen,
              onTap: () => _openPlaylist(item),
            );
          }

          // Fallback for any unhandled types
          return Container(
            padding: const EdgeInsets.all(8.0),
            child: Text('Unsupported item type: $itemType'),
          );
          break;
      }
    }

    return const SizedBox.shrink();
  }

  void _openPlaylist(Map<String, dynamic> playlist) async {
    // Reset scroll position and animate detail view opening
    _resetScrollToTop();

    setState(() {
      _selectedPlaylist = playlist;
      _selectedAlbum = null;
    });

    await _animateDetailViewOpen();
  }

  void _openAlbum(Map<String, dynamic> album) async {
    // Reset scroll position and animate detail view opening
    _resetScrollToTop();

    setState(() {
      _selectedAlbum = album;
      _selectedPlaylist = null;
    });

    await _animateDetailViewOpen();
  }

  bool get _isLoading {
    switch (widget.currentFilter) {
      case 'Pins':
        return _isLoadingPins;
      case 'Collected':
        return _isLoadingCollected;
      case 'Upvoted':
        return _isLoadingUpvoted;
      case 'Liked Songs':
        return _isLoadingLibrary;
      case 'Heavy Rotation':
        return _isLoadingHeavyRotation;
      case 'Recently Added':
        return _isLoadingRecentlyAdded;
      case 'Recently Played':
        return _isLoadingRecentlyPlayed;
      case 'Playlists':
        return _isLoadingPlaylists;
      case 'Suggested Songs':
        return SuggestedSongsProvider.instance.isLoading ||
            SuggestedSongsProvider.instance.isLoadingMore;
      default:
        return false;
    }
  }

  bool get _shouldShowBottomLoading {
    final currentFilter = widget.currentFilter;
    return _isLoadingMore[currentFilter] == true ||
        _hasMoreData[currentFilter] == true;
  }

  dynamic get _currentData {
    switch (widget.currentFilter) {
      case 'Pins':
        return _pins;
      case 'Collected':
        return _collectedPins;
      case 'Upvoted':
        return _upvotedPins;
      case 'Liked Songs':
        return _librarySongs;
      case 'Heavy Rotation':
        return _heavyRotation;
      case 'Recently Added':
        return _recentlyAdded;
      case 'Recently Played':
        return _recentlyPlayed;
      case 'Playlists':
        return _playlists;
      case 'Suggested Songs':
        return SuggestedSongsProvider.instance.suggestedSongs;
      default:
        return [];
    }
  }

  Widget _buildErrorContent() {
    final isAppleMusicError =
        _errorMessage!.toLowerCase().contains('apple music') ||
            _errorMessage!.toLowerCase().contains('not connected') ||
            _errorMessage!.toLowerCase().contains('authenticate') ||
            _errorMessage!.toLowerCase().contains('authorization');

    // Check if Spotify is available as fallback
    final spotifyProvider =
        Provider.of<SpotifyProvider>(context, listen: false);
    final hasSpotifyFallback = spotifyProvider.isConnected;

    // If it's an Apple Music error but Spotify is available, don't show error
    if (isAppleMusicError && hasSpotifyFallback) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.info_outline,
              size: 48,
              color: Colors.green.withOpacity(0.8),
            ),
            const SizedBox(height: 16),
            Text(
              'Using Spotify for playback',
              style: TextStyle(
                color: Colors.green,
                fontSize: 16,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              'Tracks will play through Spotify',
              style: TextStyle(
                color: Colors.grey.withOpacity(0.7),
                fontSize: 14,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 48,
            color: Colors.red.withOpacity(0.8),
          ),
          const SizedBox(height: 16),
          Text(
            _errorMessage!,
            style: const TextStyle(
              color: Colors.red,
              fontSize: 16,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: _retryLoading,
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildSkeletonLoading() {
    final isSmallScreen = MediaQuery.of(context).size.width < 360;

    return ListView.builder(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      itemCount: 8, // Show 8 skeleton items
      itemBuilder: (context, index) {
        switch (widget.currentFilter) {
          case 'Pins':
          case 'Collected':
          case 'Upvoted':
            return PinCardSkeleton(isSmallScreen: isSmallScreen);
          case 'Liked Songs':
          case 'Recently Played':
          case 'Suggested Songs':
            return TrackCardSkeleton(isSmallScreen: isSmallScreen);
          case 'Playlists':
            return PlaylistCardSkeleton(isSmallScreen: isSmallScreen);
          case 'Recently Added':
          case 'Heavy Rotation':
            // For these filters, items can be different types, so use a generic skeleton
            return TrackCardSkeleton(isSmallScreen: isSmallScreen);
          default:
            return TrackCardSkeleton(isSmallScreen: isSmallScreen);
        }
      },
    );
  }

  Widget _buildBottomSkeletonLoading() {
    final isSmallScreen = MediaQuery.of(context).size.width < 360;

    switch (widget.currentFilter) {
      case 'Pins':
      case 'Collected':
      case 'Upvoted':
        return PinCardSkeleton(isSmallScreen: isSmallScreen);
      case 'Liked Songs':
      case 'Recently Played':
      case 'Suggested Songs':
        return TrackCardSkeleton(isSmallScreen: isSmallScreen);
      case 'Playlists':
        return PlaylistCardSkeleton(isSmallScreen: isSmallScreen);
      case 'Recently Added':
      case 'Heavy Rotation':
        // For these filters, items can be different types, so use a generic skeleton
        return TrackCardSkeleton(isSmallScreen: isSmallScreen);
      default:
        return TrackCardSkeleton(isSmallScreen: isSmallScreen);
    }
  }

  Widget _buildEmptyContent(String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            _getEmptyStateIcon(),
            size: 48,
            color: Colors.grey.withOpacity(0.5),
          ),
          const SizedBox(height: 16),
          Text(
            message,
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey.withOpacity(0.8),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  void _retryLoading() {
    setState(() {
      _errorMessage = null;
    });

    _applyFilter();
  }

  IconData _getEmptyStateIcon() {
    switch (widget.currentFilter) {
      case 'Pins':
        return Icons.push_pin;
      case 'Collected':
        return Icons.collections;
      case 'Upvoted':
        return Icons.thumb_up;
      case 'Liked Songs':
        return Icons.favorite;
      case 'Heavy Rotation':
        return Icons.trending_up;
      case 'Recently Added':
        return Icons.new_releases;
      case 'Recently Played':
        return Icons.history;
      case 'Playlists':
        return Icons.playlist_play;
      case 'Suggested Songs':
        return Icons.auto_awesome;
      default:
        return Icons.music_note;
    }
  }

  String _getEmptyStateMessage() {
    switch (widget.currentFilter) {
      case 'Pins':
        return 'No pins yet.\nStart dropping some music pins!';
      case 'Collected':
        return 'No collected pins yet.\nStart collecting pins from the map!';
      case 'Upvoted':
        return 'No upvoted pins yet.\nStart upvoting pins you like!';
      case 'Liked Songs':
        return 'No liked songs yet.\nStart liking some tracks in Apple Music!';
      case 'Heavy Rotation':
        return 'No heavy rotation data available.\nApple Music needs more listening history to show your most played content.';
      case 'Recently Added':
        return 'No recently added items.\nAdd some music to your Apple Music library!';
      case 'Recently Played':
        return 'No recently played tracks.\nStart listening to music in Apple Music!';
      case 'Playlists':
        return 'No playlists found.\nCreate playlists in Apple Music to see them here!';
      case 'Suggested Songs':
        return 'No suggestions available.\nTry listening to more music to get personalized recommendations!';
      default:
        return 'No items to display';
    }
  }

  /// Request authorization following the MusicKit example pattern
  Future<void> _requestAuthorization() async {
    try {
      if (kDebugMode) {
        print('🍎 [AppleMusicPinsTab] Requesting Apple Music authorization...');
      }

      final status = await _musicKitPlugin.requestAuthorizationStatus();

      setState(() {
        _authStatus = status;
      });

      if (kDebugMode) {
        print('🍎 [AppleMusicPinsTab] Authorization result: ${status.toString()}');
      }

      // If authorized, initialize platform state and connect provider
      if (status is MusicAuthorizationStatusAuthorized) {
        await _initializePlatformState();

        if (!mounted) return;

        // Connect the provider as well
        final appleMusicProvider =
            Provider.of<AppleMusicProvider>(context, listen: false);
        if (!appleMusicProvider.isConnected) {
          if (kDebugMode) {
            print('🍎 [AppleMusicPinsTab] Connecting Apple Music provider...');
          }

          final connected = await appleMusicProvider.connect();
          if (connected && mounted) {
            if (kDebugMode) {
              print('✅ [AppleMusicPinsTab] Provider connected successfully');
            }
            _loadInitialData();
          } else {
            if (kDebugMode) {
              print('❌ [AppleMusicPinsTab] Failed to connect provider');
            }
          }
        } else {
          if (kDebugMode) {
            print('✅ [AppleMusicPinsTab] Provider already connected');
          }
          _loadInitialData();
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ [AppleMusicPinsTab] Authorization failed: $e');
      }
      setState(() {
        _errorMessage = 'Authorization failed: $e';
      });
    }
  }

  /// Build authorization request widget
  Widget _buildAuthorizationContent() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.music_note,
            size: 64,
            color: Colors.grey.withOpacity(0.5),
          ),
          const SizedBox(height: 24),
          Text(
            'Apple Music Access Required',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          Text(
            'To display your Apple Music library, playlists, and recently played songs, we need permission to access your Apple Music account.',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey[600],
                ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 32),
          ElevatedButton(
            onPressed: _requestAuthorization,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(
                horizontal: 32,
                vertical: 16,
              ),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: const Text(
              'Grant Access to Apple Music',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          const SizedBox(height: 16),
          if (_authStatus is MusicAuthorizationStatusDenied)
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 32),
              child: Text(
                'Access was denied. Please go to Settings > Privacy & Security > Apple Music to enable access.',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.red[600],
                    ),
                textAlign: TextAlign.center,
              ),
            ),
        ],
      ),
    );
  }

  /// Reset scroll position to top
  void _resetScrollToTop() {
    // Scroll to top when opening album/playlist details
    if (_scrollController.hasClients) {
      _scrollController.animateTo(
        0.0,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOutCubic,
      );
      if (kDebugMode) {
        print('🍎 [AppleMusicPinsTab] Scrolled to top for detail view');
      }
    }
  }

  /// Animate filter change
  Future<void> _animateFilterChange() async {
    if (_currentAnimatedFilter != widget.currentFilter) {
      // Fade out current content
      await _fadeAnimationController.reverse();

      // Update filter and reset scroll
      _currentAnimatedFilter = widget.currentFilter;
      _resetScrollToTop();

      // Fade in new content
      await _fadeAnimationController.forward();
    }
  }

  /// Animate detail view opening
  Future<void> _animateDetailViewOpen() async {
    await _slideAnimationController.forward();
  }

  /// Animate detail view closing
  Future<void> _animateDetailViewClose() async {
    await _slideAnimationController.reverse();
  }

  /// Try Spotify as a fallback using the automatic system
  Future<bool> _trySpotifyFallback(
      MusicTrack track, String appleMusicReason) async {
    try {
      if (kDebugMode) {
        print(
            '🎵 [AppleMusicPinsTab] Using automatic Spotify fallback for: ${track.title}');
        print('🎵 [AppleMusicPinsTab] Apple Music reason: $appleMusicReason');
      }

      // Get Spotify provider
      final spotifyProvider =
          Provider.of<SpotifyProvider>(context, listen: false);

      // Simply use the SpotifyProvider's automatic fallback system
      // This will handle connection, premium checks, and Apple Music fallback internally
      final success = await spotifyProvider.playTrack(track, context: context);

      if (success) {
        if (kDebugMode) {
          print(
              '✅ [AppleMusicPinsTab] Successfully played track via automatic fallback: ${track.title}');
        }

        // Note: SpotifyProvider will show its own success/fallback messages,
        // so we don't need to show a duplicate message here
        return true;
      } else {
        // Both services failed (SpotifyProvider handles the error messages)
        if (kDebugMode) {
          print(
              '❌ [AppleMusicPinsTab] Automatic fallback failed for: ${track.title}');
        }
        return false;
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ [AppleMusicPinsTab] Error in automatic fallback: $e');
      }
      return false;
    }
  }

  @override
  void didUpdateWidget(covariant AppleMusicPinsTab oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.currentFilter != widget.currentFilter) {
      _selectedPlaylist = null;
      _selectedAlbum = null;
      _applyFilter(); // This will handle the animation
    }
  }

  void _showEngagementBottomSheet(
      BuildContext context, Map<String, dynamic> pin) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      barrierColor: Colors.black.withOpacity(0.5),
      builder: (context) => TrackEngagementBottomSheet(pin: pin),
    );
  }
}
