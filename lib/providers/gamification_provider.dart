import 'package:flutter/material.dart';
import '../models/achievement.dart';
import '../models/pin_skin.dart';
import '../models/user_rank.dart';
import '../services/gamification_service.dart';
import '../screens/gamification/components/rank_preview_screen.dart';
import '../services/api/rank_service.dart';
import '../services/achievement_websocket_service.dart';
import '../widgets/notifications/achievement_notification_overlay.dart';
import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/rendering.dart';
import 'dart:io';

class GamificationProvider extends ChangeNotifier {
  final GamificationService _gamificationService;
  final RankService _rankService;
  final AchievementWebSocketService _webSocketService;
  
  // Cache keys
  static const String _achievementsCacheKey = 'achievements_cache';
  static const String _categoryCacheKeyPrefix = 'category_achievements_';
  static const String _lastLoadTimeKey = 'achievements_last_load_time';
  
  // Cache keys for user progress
  static const String _userLevelCacheKey = 'user_level_cache';
  static const String _userXpCacheKey = 'user_xp_cache';
  static const String _userProgressCacheKey = 'user_progress_cache';
  static const String _lastXpUpdateTimeKey = 'last_xp_update_time';
  
  // Cache timeout constants
  static const Duration _xpCacheTimeout = Duration(minutes: 15); // Fresh data
  static const Duration _xpCacheStaleTimeout = Duration(hours: 24); // Stale but usable
  
  // Add timestamp tracking
  DateTime? _lastDataLoad;
  static const Duration _cacheTimeout = Duration(minutes: 5);
  static const Duration _persistentCacheTimeout = Duration(days: 1);

  // Store current context for notifications
  BuildContext? _currentContext;

  // NEW: Store pending challenge completions to show on map screen
  List<Map<String, dynamic>> _pendingCompletions = [];
  int _pendingTotalXp = 0;
  bool _pendingLevelChanged = false;
  Map<String, dynamic>? _pendingNewLevel;

  GamificationProvider(this._gamificationService, this._rankService, this._webSocketService) {
    // Load cached data immediately on construction
    _loadInitialCachedData();
    _setupWebSocketListeners();
  }

  // Loading states
  bool _isLoading = false;
  bool _isLoadingAchievements = false;
  bool get isLoading => _isLoading;
  bool get isLoadingAchievements => _isLoadingAchievements;

  // Check if we need to reload data
  bool _shouldReloadData() {
    if (_lastDataLoad == null) return true;
    return DateTime.now().difference(_lastDataLoad!) > _cacheTimeout;
  }

  // Check if persistent cache is valid
  Future<bool> _isPersistentCacheValid() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final lastLoadTimeStr = prefs.getString(_lastLoadTimeKey);
      if (lastLoadTimeStr == null) return false;
      
      final lastLoadTime = DateTime.parse(lastLoadTimeStr);
      return DateTime.now().difference(lastLoadTime) <= _persistentCacheTimeout;
    } catch (e) {
      debugPrint('Error checking persistent cache validity: $e');
      return false;
    }
  }

  // Load data from persistent cache
  Future<void> _loadCachedData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // Load achievements cache
      final achievementsJson = prefs.getString(_achievementsCacheKey);
      if (achievementsJson != null) {
        final List<dynamic> achievementsData = json.decode(achievementsJson);
        _achievements = achievementsData.map((json) => Achievement.fromJson(json)).toList();
      }
      
      // Load category caches
      for (final category in ['artist', 'genre', 'location', 'social']) {
        final categoryJson = prefs.getString('${_categoryCacheKeyPrefix}$category');
        if (categoryJson != null) {
          final List<dynamic> categoryData = json.decode(categoryJson);
          _categoryAchievements[category] = categoryData.map((json) => Achievement.fromJson(json)).toList();
        }
      }
      
      // Load timestamp
      final lastLoadTimeStr = prefs.getString(_lastLoadTimeKey);
      if (lastLoadTimeStr != null) {
        _lastDataLoad = DateTime.parse(lastLoadTimeStr);
      }
      
      notifyListeners();
    } catch (e) {
      debugPrint('Error loading cached data: $e');
    }
  }

  // Save data to persistent cache
  Future<void> _saveToPersistentCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // Save achievements
      final achievementsJson = json.encode(
        _achievements.map((a) => a.toJson()).toList()
      );
      await prefs.setString(_achievementsCacheKey, achievementsJson);
      
      // Save categories
      for (final entry in _categoryAchievements.entries) {
        final categoryJson = json.encode(
          entry.value.map((a) => a.toJson()).toList()
        );
        await prefs.setString('${_categoryCacheKeyPrefix}${entry.key}', categoryJson);
      }
      
      // Save timestamp
      await prefs.setString(_lastLoadTimeKey, DateTime.now().toIso8601String());
    } catch (e) {
      debugPrint('Error saving to persistent cache: $e');
    }
  }

  // Achievement categories - REMOVED hard-coded data, will load from API
  Map<String, List<Achievement>> _categoryAchievements = {
    'artist': [],
    'genre': [],
    'location': [],
    'social': [],
  };
  
  Map<String, bool> _categoryLoadingStates = {};
  
  List<Achievement> get artistAchievements => 
      _categoryAchievements['artist'] ?? [];
  
  List<Achievement> get genreAchievements => 
      _categoryAchievements['genre'] ?? [];
  
  List<Achievement> get locationAchievements => 
      _categoryAchievements['location'] ?? [];
  
  List<Achievement> get socialAchievements => 
      _categoryAchievements['social'] ?? [];

  bool isCategoryLoading(String categoryId) => _categoryLoadingStates[categoryId] ?? false;

  // NEW: Get completed achievements by category
  List<Achievement> getCompletedAchievementsByCategory(String categoryId) {
    final achievements = _categoryAchievements[categoryId] ?? [];
    return achievements.where((a) => a.isCompleted).toList();
  }

  // NEW: Get in-progress achievements by category
  List<Achievement> getInProgressAchievementsByCategory(String categoryId) {
    final achievements = _categoryAchievements[categoryId] ?? [];
    return achievements.where((a) => !a.isCompleted && a.progressPercentage > 0).toList();
  }

  // NEW: Get locked achievements by category
  List<Achievement> getLockedAchievementsByCategory(String categoryId) {
    final achievements = _categoryAchievements[categoryId] ?? [];
    return achievements.where((a) => !a.isCompleted && a.progressPercentage == 0).toList();
  }

  // NEW: Get category completion percentage
  double getCategoryCompletionPercentage(String categoryId) {
    final achievements = _categoryAchievements[categoryId] ?? [];
    if (achievements.isEmpty) return 0.0;
    
    final completedCount = achievements.where((a) => a.isCompleted).length;
    return (completedCount / achievements.length) * 100;
  }

  // NEW: Get category progress summary
  Map<String, dynamic> getCategoryProgressSummary(String categoryId) {
    final achievements = _categoryAchievements[categoryId] ?? [];
    final completed = achievements.where((a) => a.isCompleted).length;
    final inProgress = achievements.where((a) => !a.isCompleted && a.progressPercentage > 0).length;
    final locked = achievements.where((a) => !a.isCompleted && a.progressPercentage == 0).length;
    
    return {
      'total': achievements.length,
      'completed': completed,
      'in_progress': inProgress,
      'locked': locked,
      'completion_percentage': achievements.isNotEmpty ? (completed / achievements.length) * 100 : 0.0,
    };
  }

  // Load local data first - REMOVED hard-coded achievements
  void _loadLocalData() {
    _currentRank = UserRank.basementBopper;
    _nextRank = UserRank.selector;
    _userLevel = 1;
    _xpProgress = 0.0;
    
    // Initialize empty category achievements - will be loaded from API
    _categoryAchievements = {
      'artist': [],
      'genre': [],
      'location': [],
      'social': [],
    };
    
    // Set initial achievements as empty - will be populated from API
    _achievements = [];
    
    notifyListeners();
  }

  /// Load achievements for a specific category with caching
  Future<void> loadCategoryAchievements(String categoryId) async {
    try {
      _categoryLoadingStates[categoryId] = true;
      notifyListeners();

      // Try to load from cache first
      final cachedData = await _loadCategoryFromCache(categoryId);
      if (cachedData != null && !_shouldReloadData()) {
        _categoryAchievements[categoryId] = cachedData;
        _updateMainAchievementsList();
        notifyListeners();
        return;
      }

      // Load from API using the proper endpoint
      final achievements = await _gamificationService.getAchievementsByCategory(categoryId);
      
      if (achievements.isNotEmpty) {
        _categoryAchievements[categoryId] = achievements;
        // Save to cache
        await _saveCategoryToCache(categoryId, achievements);
        // Update the main achievements list
        _updateMainAchievementsList();
        notifyListeners();
        debugPrint('✅ Loaded ${achievements.length} achievements for category $categoryId');
      } else {
        debugPrint('⚠️ No achievements found for category $categoryId');
      }
    } catch (e) {
      debugPrint('❌ Error loading achievements for category $categoryId: $e');
      // If there's an error, try to load from cache as fallback
      final cachedData = await _loadCategoryFromCache(categoryId);
      if (cachedData != null) {
        _categoryAchievements[categoryId] = cachedData;
        _updateMainAchievementsList();
        notifyListeners();
      }
    } finally {
      _categoryLoadingStates[categoryId] = false;
      notifyListeners();
    }
  }

  Future<List<Achievement>?> _loadCategoryFromCache(String categoryId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final categoryJson = prefs.getString('${_categoryCacheKeyPrefix}$categoryId');
      if (categoryJson != null) {
        final List<dynamic> categoryData = json.decode(categoryJson);
        return categoryData.map((json) => Achievement.fromJson(json)).toList();
      }
    } catch (e) {
      debugPrint('Error loading category from cache: $e');
    }
    return null;
  }

  Future<void> _saveCategoryToCache(String categoryId, List<Achievement> achievements) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final categoryJson = json.encode(achievements.map((a) => a.toJson()).toList());
      await prefs.setString('${_categoryCacheKeyPrefix}$categoryId', categoryJson);
    } catch (e) {
      debugPrint('Error saving category to cache: $e');
    }
  }

  /// Update the main achievements list from all categories
  void _updateMainAchievementsList() {
    _achievements = [];
    
    // Combine all category achievements
    for (final achievements in _categoryAchievements.values) {
      _achievements.addAll(achievements);
    }
    
    // Update completed and in-progress lists
    _completedAchievements = _achievements.where((a) => a.isCompleted).toList();
    _inProgressAchievements = _achievements.where((a) => 
      !a.isCompleted && (a.progress?.isNotEmpty ?? false)
    ).toList();
    
    debugPrint('\n📊 Achievements Summary:');
    debugPrint('- Total: ${_achievements.length}');
    debugPrint('- Completed: ${_completedAchievements.length}');
    debugPrint('- In Progress: ${_inProgressAchievements.length}');
  }

  // Completion stats
  Map<String, dynamic> _completionStats = {
    'total_completed': 0,
    'total_xp_earned': 0,
    'completion_by_type': {},
    'completion_dates': {
      'first_completion': null,
      'latest_completion': null,
    }
  };

  // Getters for completion stats
  int get totalCompleted => _completionStats['total_completed'] as int? ?? 0;
  int get totalXpEarned => _completionStats['total_xp_earned'] as int? ?? 0;
  Map<String, dynamic> get completionByType => 
      _completionStats['completion_by_type'] as Map<String, dynamic>? ?? {};
  DateTime? get firstCompletionDate => _completionStats['completion_dates']?['first_completion'] != null
      ? DateTime.parse(_completionStats['completion_dates']['first_completion'].toString())
      : null;
  DateTime? get latestCompletionDate => _completionStats['completion_dates']?['latest_completion'] != null
      ? DateTime.parse(_completionStats['completion_dates']['latest_completion'].toString())
      : null;

  /// Load completion stats
  Future<void> loadCompletionStats() async {
    try {
      // NEW: Use the super fast completed_summary endpoint
      final summary = await _gamificationService.getCompletedSummary();
      
      _completionStats = {
        'total_completed': summary['total_completed'] ?? 0,
        'total_xp_earned': summary['total_xp'] ?? 0,
        'completion_by_type': {
          'location': summary['location']?['count'] ?? 0,
          'artist': summary['artist']?['count'] ?? 0,
          'genre': summary['genre']?['count'] ?? 0,
          'social': summary['social']?['count'] ?? 0,
        },
        'completion_dates': {
          'first_completion': null,
          'latest_completion': null,
        }
      };
      
      debugPrint('✅ Loaded completion stats from optimized endpoint');
      notifyListeners();
    } catch (e) {
      debugPrint('❌ Error loading completion stats: $e');
      _completionStats = {
        'total_completed': 0,
        'total_xp_earned': 0,
        'completion_by_type': {},
        'completion_dates': {
          'first_completion': null,
          'latest_completion': null,
        }
      };
    }
  }

  /// NEW: Load and cache completed summary for dashboard
  Future<Map<String, dynamic>> loadCompletedSummary() async {
    try {
      debugPrint('🏆 Loading completed summary for dashboard...');
      final summary = await _gamificationService.getCompletedSummary();
      
      // Update local completion stats
      _completionStats = {
        'total_completed': summary['total_completed'] ?? 0,
        'total_xp_earned': summary['total_xp'] ?? 0,
        'completion_by_type': {
          'location': summary['location']?['count'] ?? 0,
          'artist': summary['artist']?['count'] ?? 0,
          'genre': summary['genre']?['count'] ?? 0,
          'social': summary['social']?['count'] ?? 0,
        },
        'completion_dates': {
          'first_completion': null,
          'latest_completion': null,
        }
      };
      
      notifyListeners();
      return summary;
    } catch (e) {
      debugPrint('❌ Error loading completed summary: $e');
      return {
        'location': {'count': 0, 'recent': []},
        'artist': {'count': 0, 'recent': []},
        'genre': {'count': 0, 'recent': []},
        'social': {'count': 0, 'recent': []},
        'total_completed': 0,
        'total_xp': 0,
      };
    }
  }

  /// NEW: Get category completion count (fast method)
  int getCategoryCompletionCount(String categoryId) {
    final completionByType = _completionStats['completion_by_type'] as Map<String, dynamic>? ?? {};
    return completionByType[categoryId] as int? ?? 0;
  }

  /// Initialize the provider and load data
  @override
  Future<void> initialize() async {
    if (_isLoading) return;
    
    try {
      _isLoading = true;
      notifyListeners();

      // First load cached data for immediate display
      await loadCachedProgress();
      
      // Load cached achievements data
      await _loadCachedData();
      
      // Then check if we need to refresh from network
      if (_shouldReloadData()) {
        debugPrint('🔄 Fetching fresh data from network...');
      await Future.wait([
        fetchUserRankData(),
        loadAllCategoryAchievements(),
        loadCompletionStats(),
          _loadUserAchievements(), // Make sure user achievements are loaded
      ]);
      
        // Save fresh data to cache
        await Future.wait([
          _saveUserProgressToCache(),
          _saveToPersistentCache(),
        ]);
      }
      
      // Update achievement lists
      _updateMainAchievementsList();
      
    } catch (e) {
      debugPrint('❌ Error initializing provider: $e');
      _error = 'Failed to initialize: $e';
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// Load cached user progress data
  Future<void> loadCachedProgress() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // Load cached data with timestamps
      final cachedLevel = prefs.getInt(_userLevelCacheKey);
      final cachedXp = prefs.getInt(_userXpCacheKey);
      final cachedProgress = prefs.getDouble(_userProgressCacheKey);
      final lastUpdateTimeStr = prefs.getString(_lastXpUpdateTimeKey);
      
      if (lastUpdateTimeStr != null) {
        final lastUpdateTime = DateTime.parse(lastUpdateTimeStr);
        final now = DateTime.now();
        final age = now.difference(lastUpdateTime);
        
        debugPrint('\n📦 Loading cached user progress:');
        debugPrint('- Cache age: ${age.inMinutes} minutes');
        debugPrint('- Cached Level: $cachedLevel');
        debugPrint('- Cached XP: $cachedXp');
        debugPrint('- Cached Progress: $cachedProgress');
        
        // Use cached data if it's fresh or we're offline
        if (age < _xpCacheTimeout || !await _hasInternetConnection()) {
          if (cachedLevel != null) _userLevel = cachedLevel;
          if (cachedXp != null) _data['current_xp'] = cachedXp;
          if (cachedProgress != null) _xpProgress = cachedProgress;
          
          // Update ranks based on cached level
          _currentRank = UserRank.getRankForLevel(_userLevel);
          final nextRankCandidate = UserRank.allRanks
              .where((rank) => rank.requiredLevel > _userLevel)
              .firstOrNull;
          _nextRank = nextRankCandidate ?? UserRank.legend;
          
      notifyListeners();
          debugPrint('✅ Using cached user progress (age: ${age.inMinutes}m)');
        }
      }
    } catch (e) {
      debugPrint('❌ Error loading cached user progress: $e');
    }
  }

  /// Save current user progress to cache
  Future<void> _saveUserProgressToCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      await Future.wait([
        prefs.setInt(_userLevelCacheKey, _userLevel),
        prefs.setInt(_userXpCacheKey, currentXp),
        prefs.setDouble(_userProgressCacheKey, _xpProgress),
        prefs.setString(_lastXpUpdateTimeKey, DateTime.now().toIso8601String()),
      ]);
      
      debugPrint('\n💾 Saved user progress to cache:');
      debugPrint('- Level: $_userLevel');
      debugPrint('- XP: $currentXp');
      debugPrint('- Progress: $_xpProgress');
    } catch (e) {
      debugPrint('❌ Error saving user progress to cache: $e');
    }
  }
  
  /// Check if device has internet connection
  Future<bool> _hasInternetConnection() async {
    try {
      final result = await InternetAddress.lookup('google.com');
      return result.isNotEmpty && result[0].rawAddress.isNotEmpty;
    } on SocketException catch (_) {
      return false;
    }
  }
  
  /// Background refresh of user progress
  Future<void> _refreshUserProgressInBackground() async {
    try {
      // Fetch fresh data from API
      final data = await _rankService.fetchRankBadgeData();
      
      // Update local state
      _data = data;
      _userLevel = data['user_level'] as int;
      _xpProgress = (data['xp_progress'] as num).toDouble().clamp(0.0, 100.0);
      
      // Update ranks
      _currentRank = UserRank.getRankForLevel(_userLevel);
      final nextRankCandidate = UserRank.allRanks
          .where((rank) => rank.requiredLevel > _userLevel)
          .firstOrNull;
      _nextRank = nextRankCandidate ?? UserRank.legend;
      
      // Save fresh data to cache
      await _saveUserProgressToCache();
      
      notifyListeners();
      debugPrint('✅ Background refresh completed');
    } catch (e) {
      debugPrint('❌ Background refresh failed: $e');
    }
  }

  UserRank _currentRank = UserRank.basementBopper;
  UserRank _nextRank = UserRank.selector;
  int _userLevel = 1;
  double _xpProgress = 0.0;
  String? _error;
  Map<String, dynamic> _data = {};

  // Achievements state
  List<Achievement> _achievements = [];
  List<Achievement> _completedAchievements = [];
  List<Achievement> _inProgressAchievements = [];
  String? _achievementsError;

  // Pin skins state
  List<PinSkin> _pinSkins = [];
  List<PinSkin> _unlockedSkins = [];
  PinSkin? _equippedSkin;
  String? _skinsError;

  // Getters
  UserRank get currentRank => _currentRank;
  UserRank get nextRank => _nextRank;
  int get userLevel => _userLevel;
  double get xpProgress => _xpProgress;
  String? get error => _error;

  // XP getters - Access backend data directly
  int get currentXp => _data['current_xp'] as int? ?? 0;
  int get nextLevelXp {
    // If we have the data from backend, use it
    if (_data.containsKey('next_level_xp')) {
      return _data['next_level_xp'] as int;
    }
    
    // Otherwise calculate based on current level
    // Level 1 -> Level 2: 500 XP
    // Level 2 -> Level 3: 1,500 XP
    // Level 3 -> Level 4: 3,500 XP
    // Level 4 -> Level 5: 7,000 XP
    // Level 5 -> Level 6: 12,000 XP
    // Level 6 -> Level 7: 20,000 XP
    switch (_userLevel) {
      case 1: return 500;
      case 2: return 1500;
      case 3: return 3500;
      case 4: return 7000;
      case 5: return 12000;
      case 6: return 20000;
      default: return 20000; // Max level is 7
    }
  }
  int get totalXp => _data['total_xp'] as int? ?? 0;

  List<Achievement> get achievements => _achievements;
  List<Achievement> get completedAchievements => _completedAchievements;
  List<Achievement> get inProgressAchievements => _inProgressAchievements;
  String? get achievementsError => _achievementsError;

  List<PinSkin> get pinSkins => _pinSkins;
  List<PinSkin> get unlockedSkins => _unlockedSkins;
  PinSkin? get equippedSkin => _equippedSkin;
  String? get skinsError => _skinsError;

  int get totalPoints => _completedAchievements.length * 100;
  int get completedCount => _completedAchievements.length;
  int get totalAchievements => _achievements.length;
  
  // Locked achievements getter
  List<Achievement> get lockedAchievements => _achievements
    .where((a) => !a.isCompleted && (a.progress == null || a.progress!.isEmpty))
    .toList();

  /// Fetch user rank data with optimized endpoint
  Future<void> fetchUserRankData() async {
    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      // NEW: Use the optimized endpoint that gets everything in one call
      final data = await _rankService.fetchRankBadgeData();
      _data = data; // Store the raw data
      
      // Extract current rank data
      final currentRankData = data['current_rank'];
      final nextRankData = data['next_rank'];
      
      // Update user level and XP data
      _userLevel = data['user_level'] as int;
      _xpProgress = (data['xp_progress'] as num).toDouble().clamp(0.0, 100.0);
      
      // Parse current rank from API response
      _currentRank = _parseRankFromApi(currentRankData) ?? UserRank.basementBopper;
      
      // Parse next rank from API response
      _nextRank = nextRankData != null 
          ? _parseRankFromApi(nextRankData) ?? UserRank.legend
          : UserRank.legend;

      // Load additional data (achievements loaded separately via initialize())
      await Future.wait([
        loadPinSkins(),
        loadUnlockedSkins(),
      ]);
      
      debugPrint('🏆 User rank data loaded successfully');
      
    } catch (e) {
      _error = e.toString();
      debugPrint('❌ Error loading user rank data: $e');
      // Set default values in case of error
      _xpProgress = 0.0;
      _userLevel = 1;
      _currentRank = UserRank.basementBopper;
      _nextRank = UserRank.selector;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// NEW: Parse rank data from API response
  UserRank? _parseRankFromApi(Map<String, dynamic> rankData) {
    final rankId = rankData['id'] as String?;
    final level = rankData['level'] as int?;
    
    debugPrint('\n🎯 Parsing rank data from API:');
    debugPrint('- Rank ID: $rankId');
    debugPrint('- Level: $level');
    
    if (level != null) {
      final rank = UserRank.getRankForLevel(level);
      debugPrint('- Selected rank by level: ${rank.name}');
      return rank;
    }
    
    // Fallback: try to match by ID
    if (rankId != null) {
      debugPrint('- Trying to match by ID: $rankId');
      switch (rankId) {
        case 'level_1':
        case 'basement_bopper':
          return UserRank.basementBopper;
        case 'level_2':
        case 'selector':
          return UserRank.selector;
        case 'level_3':
        case 'tastemaker':
          return UserRank.tastemaker;
        case 'level_4':
        case 'trendsetter':
          return UserRank.trendsetter;
        case 'level_5':
        case 'icon':
          return UserRank.icon;
        case 'level_6':
        case 'architect':
          return UserRank.architect;
        case 'level_7':
        case 'legend':
          return UserRank.legend;
      }
    }
    
    debugPrint('❌ Could not determine rank from API data');
    return null;
  }

  /// NEW: Quick progress update for real-time progress bar
  Future<void> updateQuickProgress() async {
    try {
      final data = await _rankService.fetchQuickProgress();
      
      // Update only the progress-related data for smooth animations
      _xpProgress = (data['xp_progress'] as num).toDouble().clamp(0.0, 100.0);
      
      // Check if level changed
      final newLevel = data['user_level'] as int?;
      if (newLevel != null && newLevel != _userLevel) {
        _userLevel = newLevel;
        _currentRank = UserRank.getRankForLevel(_userLevel);
        
        // Recalculate next rank
        final nextRankCandidate = UserRank.allRanks
            .where((rank) => rank.requiredLevel > _userLevel)
            .firstOrNull;
        _nextRank = nextRankCandidate ?? UserRank.legend;
        
        // Update cache with new level data
        await _saveUserProgressToCache();
      }
      
    notifyListeners();
    } catch (e) {
      // Fail silently for quick updates to avoid disrupting UI
      debugPrint('Quick progress update failed: $e');
    }
  }

  /// NEW: Send action response after user actions (pin creation, etc.)
  Future<Map<String, dynamic>?> sendActionResponse(String action, Map<String, dynamic> data) async {
    try {
      final response = await _rankService.sendActionResponse(action, data);
      
      if (response.containsKey('xp_progress')) {
        _xpProgress = (response['xp_progress'] as num).toDouble().clamp(0.0, 100.0);
      }
      
      // Check for level changes
      if (response['level_changed'] == true) {
        final newLevelData = response['new_level'] as Map<String, dynamic>?;
        if (newLevelData != null) {
          _userLevel = newLevelData['level'] as int;
          _currentRank = UserRank.getRankForLevel(_userLevel);
          
          // Recalculate next rank
          final nextRankCandidate = UserRank.allRanks
              .where((rank) => rank.requiredLevel > _userLevel)
              .firstOrNull;
          _nextRank = nextRankCandidate ?? UserRank.legend;
        }
      }
      
      notifyListeners();
      return response;
    } catch (e) {
      debugPrint('Action response failed: $e');
      return null;
    }
  }

  /// Load all achievements
  Future<void> loadAchievements() async {
    try {
      debugPrint('🎯 Loading general achievements...');
      
      // Load general achievements from the main endpoint
      final generalAchievements = await _gamificationService.getAchievements();
      
      // Load user achievements and merge progress data
      await _loadUserAchievements();
      
      debugPrint('🎯 Loaded ${generalAchievements.length} general achievements');
      
      // Note: Category achievements are loaded separately via loadAllCategoryAchievements()
      // This method is for general achievements that don't belong to specific categories
      
    } catch (e) {
      _achievementsError = e.toString();
      debugPrint('❌ Error loading general achievements: $e');
    }
  }

  /// Load user achievements and merge progress data with achievements
  Future<void> _loadUserAchievements() async {
    try {
      final userAchievements = await _gamificationService.getUserAchievements();
      
      // Update achievements with user progress data
      for (final userAchievement in userAchievements) {
        final achievementIndex = _achievements.indexWhere(
          (a) => a.id == userAchievement.achievement.id
        );
        
        if (achievementIndex != -1) {
          _achievements[achievementIndex] = _achievements[achievementIndex].copyWith(
            progress: userAchievement.progress,
            isCompleted: userAchievement.isCompleted,
            completedAt: userAchievement.completedAt,
          );
        }
      }
      
      // Separate completed and in-progress achievements
      _completedAchievements = _achievements.where((a) => a.isCompleted).toList();
      _inProgressAchievements = _achievements.where((a) => 
        !a.isCompleted && (a.progress?.isNotEmpty ?? false)
      ).toList();
      
    } catch (e) {
      debugPrint('Error loading user achievements: $e');
    }
  }
  
  /// Calculate user level and XP progress based on completed achievements
  void _calculateUserLevelAndProgress() {
    // Simple calculation: 
    // - Each level requires level * 500 XP
    // - Each completed achievement gives xpReward
    
    int totalXP = 0;
    for (final achievement in _completedAchievements) {
      totalXP += achievement.xpReward;
    }
    
    // Calculate level based on XP (simple formula: each level requires level * 500 XP)
    int level = 1;
    int xpForNextLevel = 500;
    int remainingXP = totalXP;
    
    while (remainingXP >= xpForNextLevel) {
      remainingXP -= xpForNextLevel;
      level++;
      xpForNextLevel = level * 500; // Increase XP requirement for next level
    }
    
    // Calculate progress percentage towards next level
    final progressPercent = xpForNextLevel > 0 
        ? (remainingXP / xpForNextLevel) * 100 
        : 0.0;
    
    _userLevel = level;
    _xpProgress = progressPercent;
    
    // Update rank information
    _currentRank = UserRank.getRankForLevel(_userLevel);
    
    // Find next rank - ensure it's not nullable
    final nextRankCandidate = UserRank.allRanks
        .where((rank) => rank.requiredLevel > _userLevel)
        .firstOrNull;
    
    _nextRank = nextRankCandidate ?? UserRank.legend; // Default to highest rank if no next rank found
        
    notifyListeners();
  }

  /// Load all pin skins
  Future<void> loadPinSkins() async {
    _isLoading = true;
    _skinsError = null;
    notifyListeners();

    try {
      _pinSkins = await _gamificationService.getPinSkins();
    } catch (e) {
      _skinsError = e.toString();
      debugPrint('Error loading pin skins: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// Load unlocked skins for current user
  Future<void> loadUnlockedSkins() async {
    try {
      _unlockedSkins = await _gamificationService.getUnlockedSkins();
      
      // Find equipped skin - look for the one marked as equipped
      _equippedSkin = _unlockedSkins.firstWhere(
        (skin) => skin.isEquipped,
        orElse: () => _unlockedSkins.isNotEmpty ? _unlockedSkins.first : _getDefaultSkin(),
      );
    } catch (e) {
      debugPrint('Error loading unlocked skins: $e');
      // Fallback to default skin if there's an error
      _equippedSkin = _getDefaultSkin();
    }
  }

  /// Equip a pin skin
  Future<bool> equipSkin(PinSkin skin) async {
    try {
      final equippedSkin = await _gamificationService.equipPinSkin(skin.id);
      if (equippedSkin != null) {
        _equippedSkin = equippedSkin;
        await loadUnlockedSkins(); // Refresh the list to get updated equipped status
        notifyListeners();
        return true;
      }
    } catch (e) {
      debugPrint('Error equipping skin: $e');
    }
    return false;
  }

  /// Update progress for an achievement
  Future<bool> updateAchievementProgress(
    String achievementId, 
    Map<String, dynamic> progress
  ) async {
    try {
      final achievementIdInt = int.tryParse(achievementId);
      if (achievementIdInt == null) {
        debugPrint('Invalid achievement ID: $achievementId');
        return false;
      }

      // Get or create user achievement for progress tracking
      final userAchievement = await _gamificationService.getOrCreateUserAchievement(achievementIdInt);
      if (userAchievement == null) {
        debugPrint('Failed to get or create user achievement for ID: $achievementId');
        return false;
      }

      // Update progress using the user achievement ID
      final response = await _gamificationService.updateUserAchievementProgress(
        userAchievement.id,
        progress,
      );
      
      if (response.success) {
        await loadAchievements(); // Refresh achievements to get updated progress
        await loadUnlockedSkins(); // Refresh skins in case new ones were unlocked
        _calculateUserLevelAndProgress(); // Recalculate level and XP
        return true;
      } else {
        debugPrint('Failed to update achievement progress: ${response.message}');
      }
    } catch (e) {
      debugPrint('Error updating achievement progress: $e');
    }
    return false;
  }

  PinSkin _getDefaultSkin() {
    return PinSkin(
      id: 0,
      name: 'Default Pin',
      image: 'assets/icons/default_pin.png',
      description: 'The classic music pin',
      isPremium: false,
      createdAt: DateTime.now(),
      isEquipped: true,
      isUnlocked: true,
    );
  }

  // Featured challenge selection
  Achievement getFeaturedChallenge() {
    // If there are no achievements, return a default achievement
    if (achievements.isEmpty) {
      return Achievement.create(
        id: 0,
        name: 'No Challenges Available',
        description: 'Complete more actions to unlock challenges',
        icon: 'emoji_events',
        criteria: {},
        createdAt: DateTime.now(),
        category: 'General',
      );
    }
    
    // Try to find an incomplete achievement with some progress
    try {
      return achievements
          .where((a) => !a.isCompleted && a.progressPercentage > 0)
          .fold<Achievement>(achievements.first, (a, b) => a.progressPercentage > b.progressPercentage ? a : b);
    } catch (e) {
      // If there's any error, return the first achievement or a default one
      return achievements.isNotEmpty 
          ? achievements.first
          : Achievement.create(
              id: 0,
              name: 'Default Challenge',
              description: 'Start exploring to unlock more challenges',
              icon: 'emoji_events',
              criteria: {},
              createdAt: DateTime.now(),
              category: 'General',
            );
    }
  }

  // Daily challenge selection
  Achievement getDailyChallenge() {
    // If there are no achievements, return a default achievement
    if (achievements.isEmpty) {
      return Achievement.create(
        id: 0,
        name: 'No Daily Challenge',
        description: 'Check back later for new challenges',
        icon: 'emoji_events',
        criteria: {},
        createdAt: DateTime.now(),
        category: 'General',
      );
    }
    
    // Try to find an incomplete achievement
    final incomplete = achievements.where((a) => !a.isCompleted).toList();
    if (incomplete.isEmpty) {
      return achievements.first; // Return any achievement if all are completed
    }
    
    // Return a random incomplete achievement
    incomplete.shuffle();
    return incomplete.first;
  }

  // Category progress calculations
  double getArtistProgress() {
    if (artistAchievements.isEmpty) return 0.0;
    final completed = artistAchievements.where((a) => a.isCompleted).length;
    return completed / artistAchievements.length;
  }

  double getGenreProgress() {
    if (genreAchievements.isEmpty) return 0.0;
    final completed = genreAchievements.where((a) => a.isCompleted).length;
    return completed / genreAchievements.length;
  }

  double getLocationProgress() {
    if (locationAchievements.isEmpty) return 0.0;
    final completed = locationAchievements.where((a) => a.isCompleted).length;
    return completed / locationAchievements.length;
  }

  double getSocialProgress() {
    if (socialAchievements.isEmpty) return 0.0;
    final completed = socialAchievements.where((a) => a.isCompleted).length;
    return completed / socialAchievements.length;
  }

  void clearError() {
    _error = null;
    notifyListeners();
  }

  // Navigation helper
  void showRankPreview(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const RankPreviewScreen(),
      ),
    );
  }

  /// Load all category achievements
  Future<void> loadAllCategoryAchievements() async {
    debugPrint('📚 Loading all category achievements...');
    final categories = ['artist', 'genre', 'location', 'social'];
    
    // Load categories in parallel for better performance
    await Future.wait(
      categories.map((category) => loadCategoryAchievements(category))
    );
    
    debugPrint('📚 Finished loading all category achievements');
    debugPrint('📊 Total achievements loaded: ${_achievements.length}');
  }

  /// Convenience method for pin creation XP updates
  Future<bool> handlePinCreated(String pinId, {Map<String, dynamic>? additionalData}) async {
    final data = {
      'pin_id': pinId,
      ...?additionalData,
    };
    
    final response = await sendActionResponse('pin_created', data);
    
    if (response != null) {
      // Use the new achievement response handler
      await _handleAchievementResponse(response);
      return true;
    }
    
    return false;
  }

  /// NEW: Track social actions (votes, comments, etc.)
  Future<bool> trackSocialAction(String actionType, Map<String, dynamic> data) async {
    try {
      final response = await sendActionResponse(actionType, data);
      
      if (response != null) {
        await _handleAchievementResponse(response);
        
        // 🔄 FORCE REFRESH: Update social category achievements immediately
        try {
          await loadCategoryAchievements('social');
          debugPrint('🔄 Social achievements refreshed after $actionType');
        } catch (e) {
          debugPrint('⚠️ Failed to refresh social achievements after $actionType: $e');
        }
        
        return true;
      }
      
      return false;
    } catch (e) {
      debugPrint('Social tracking failed: $e');
      return false;
    }
  }

  /// NEW: Track vote on pin
  Future<bool> handleVoteGiven(int pinId, int voteValue) async {
    final success = await trackSocialAction('reaction_given', {
      'vote_value': voteValue,
      'pin_id': pinId,
    });
    
    // 🔄 FORCE REFRESH: Immediately update local cache after social action
    if (success) {
      try {
        await _refreshAchievementData();
        debugPrint('🔄 Achievement data refreshed after vote tracking');
      } catch (e) {
        debugPrint('⚠️ Failed to refresh achievement data after vote: $e');
      }
    }
    
    return success;
  }

  /// NEW: Track comment on pin
  Future<bool> handleCommentGiven(int pinId, String comment) async {
    final success = await trackSocialAction('comment_given', {
      'pin_id': pinId,
      'comment': comment,
    });
    
    // 🔄 FORCE REFRESH: Immediately update local cache after social action
    if (success) {
      try {
        await _refreshAchievementData();
        debugPrint('🔄 Achievement data refreshed after comment tracking');
      } catch (e) {
        debugPrint('⚠️ Failed to refresh achievement data after comment: $e');
      }
    }
    
    return success;
  }

  /// Convenience method for other user actions
  Future<bool> handleUserAction(String actionType, Map<String, dynamic> actionData) async {
    final response = await sendActionResponse(actionType, actionData);
    
    if (response != null) {
      await _handleAchievementResponse(response);
      return true;
    }
    
    return false;
  }

  /// NEW: Handle achievement response from action_response endpoint
  Future<void> _handleAchievementResponse(Map<String, dynamic> response) async {
    try {
      debugPrint('\n🎯 Achievement Response Data:');
      debugPrint('Raw response: $response');
      
      final xpGained = response['xp_gained'] as int? ?? 0;
      final achievementsCompleted = response['achievements_completed'] as List<dynamic>? ?? [];
      final levelChanged = response['level_changed'] as bool? ?? false;
      final newLevel = response['new_level'] as Map<String, dynamic>?;

      debugPrint('\n📊 Parsed Data:');
      debugPrint('- XP Gained: $xpGained');
      debugPrint('- Level Changed: $levelChanged');
      debugPrint('- New Level: $newLevel');
      debugPrint('\n🏆 Completed Achievements:');
      for (final achievement in achievementsCompleted) {
        debugPrint('  Achievement: {');
        debugPrint('    name: ${achievement['name']}');
        debugPrint('    description: ${achievement['description']}');
        debugPrint('    xp_reward: ${achievement['xp_reward'] ?? achievement['xp_earned']}');
        debugPrint('  }');
      }

      // Update local state
      if (response.containsKey('xp_progress')) {
        _xpProgress = (response['xp_progress'] as num).toDouble().clamp(0.0, 100.0);
        debugPrint('\n📈 Progress Update:');
        debugPrint('- New XP Progress: $_xpProgress%');
      }

      // NEW: Update XP values
      if (response.containsKey('current_xp')) {
        _data['current_xp'] = response['current_xp'] as int;
        debugPrint('- Current XP: ${_data['current_xp']}');
      }
      if (response.containsKey('next_level_xp')) {
        _data['next_level_xp'] = response['next_level_xp'] as int;
        debugPrint('- Next Level XP: ${_data['next_level_xp']}');
      }

      // Handle level changes
      if (levelChanged && newLevel != null) {
        _userLevel = newLevel['level'] as int;
        _currentRank = UserRank.getRankForLevel(_userLevel);
        
        final nextRankCandidate = UserRank.allRanks
            .where((rank) => rank.requiredLevel > _userLevel)
            .firstOrNull;
        _nextRank = nextRankCandidate ?? UserRank.legend;
        
        debugPrint('\n⭐ Level Up Details:');
        debugPrint('- New Level: $_userLevel');
        debugPrint('- Current Rank: ${_currentRank.name}');
        debugPrint('- Next Rank: ${_nextRank.name}');
      }

      // CRITICAL FIX: Refresh achievement data from API to update progress
      await _refreshAchievementData();

      notifyListeners();

      // NEW: Store pending completions to show on map screen instead of immediately
      if (achievementsCompleted.isNotEmpty) {
        _pendingCompletions = achievementsCompleted.cast<Map<String, dynamic>>();
        _pendingTotalXp = xpGained;
        _pendingLevelChanged = levelChanged;
        _pendingNewLevel = newLevel;
        
        debugPrint('\n🎯 Stored Pending Completions:');
        debugPrint('- Number of completions: ${_pendingCompletions.length}');
        debugPrint('- Total XP: $_pendingTotalXp');
        debugPrint('- Level Changed: $_pendingLevelChanged');
        debugPrint('- New Level: $_pendingNewLevel');
      
        // Show immediate notifications for XP/level changes without challenge completions
        if (xpGained > 0) {
          _showXPGainedNotification(xpGained);
        }

        if (levelChanged && newLevel != null) {
          _showLevelUpNotification(newLevel);
        }
      }

      // After updating local state, save to cache
      await _saveUserProgressToCache();

    } catch (e) {
      debugPrint('❌ Error handling achievement response: $e');
      debugPrint('Stack trace: ${StackTrace.current}');
    }
  }

  /// NEW: Show XP gained notification
  void _showXPGainedNotification(int xpGained) {
    if (_currentContext != null) {
      AchievementNotificationOverlay.showXPGained(_currentContext!, xpGained);
    } else {
      debugPrint('🎉 XP Gained: +$xpGained XP!');
    }
  }

  /// NEW: Show level up notification
  void _showLevelUpNotification(Map<String, dynamic> newLevel) {
    if (_currentContext != null) {
      AchievementNotificationOverlay.showLevelUp(_currentContext!, newLevel);
    } else {
      debugPrint('⭐ Level Up! Welcome to Level ${newLevel['level']}!');
    }
  }

  /// NEW: Setup WebSocket listeners for real-time updates
  void _setupWebSocketListeners() {
    // Listen for achievement completions
    _webSocketService.onAchievementCompleted.listen((achievement) {
      if (_currentContext != null) {
        AchievementNotificationOverlay.showAchievementCompleted(_currentContext!, achievement);
      }
      // Update local achievements
      _refreshAchievementData();
    });

    // Listen for level ups
    _webSocketService.onLevelUp.listen((newLevel) {
      if (_currentContext != null) {
        AchievementNotificationOverlay.showLevelUp(_currentContext!, newLevel);
      }
      // Update local rank data
      fetchUserRankData();
    });

    // Listen for XP gains
    _webSocketService.onXpGained.listen((xpGained) {
      if (_currentContext != null) {
        AchievementNotificationOverlay.showXPGained(_currentContext!, xpGained);
      }
      // Update progress immediately
      updateQuickProgress();
    });

    // Listen for progress updates
    _webSocketService.onProgressUpdate.listen((progressData) {
      // Update progress bars in real-time
      updateQuickProgress();
    });
  }

  /// NEW: Set current context for notifications
  void setContext(BuildContext context) {
    _currentContext = context;
  }

  /// NEW: Connect WebSocket
  Future<void> connectWebSocket(String authToken) async {
    try {
      await _webSocketService.connect(authToken);
      _webSocketService.startPeriodicPing();
      
      // CRITICAL: Setup listeners for real-time updates
      _setupWebSocketListeners();
      
      debugPrint('🔌 WebSocket connected for real-time achievements');
    } catch (e) {
      debugPrint('🔌 WebSocket connection failed: $e');
    }
  }

  /// NEW: Disconnect WebSocket
  Future<void> disconnectWebSocket() async {
    try {
      await _webSocketService.disconnect();
      debugPrint('🔌 WebSocket disconnected');
    } catch (e) {
      debugPrint('🔌 WebSocket disconnect error: $e');
    }
  }

  /// NEW: Refresh achievement data (called from WebSocket updates)
  Future<void> _refreshAchievementData() async {
    try {
      await loadAllCategoryAchievements();
    } catch (e) {
      debugPrint('Error refreshing achievement data: $e');
    }
  }

  /// NEW: Get WebSocket connection status
  bool get isWebSocketConnected => _webSocketService.isConnected;

  /// NEW: Show pending challenge completions on map screen
  void showPendingCompletions() {
    if (_pendingCompletions.isNotEmpty && _currentContext != null) {
      // Show as overlay notification instead of modal dialog for better UX
      AchievementNotificationOverlay.showChallengeCompletionNotification(
        _currentContext!,
        _pendingCompletions,
        _pendingTotalXp,
        levelChanged: _pendingLevelChanged,
        newLevel: _pendingNewLevel,
      );
      
      // Clear pending completions after showing
      _clearPendingCompletions();
    }
  }

  /// NEW: Clear pending completions
  void _clearPendingCompletions() {
    _pendingCompletions.clear();
    _pendingTotalXp = 0;
    _pendingLevelChanged = false;
    _pendingNewLevel = null;
  }

  /// NEW: Check if there are pending completions
  bool get hasPendingCompletions => _pendingCompletions.isNotEmpty;

  /// Dispose method to clean up resources
  @override
  void dispose() {
    _webSocketService.dispose();
    super.dispose();
  }

  /// Force reload data even if cache is fresh
  Future<void> forceReload() async {
    if (_isLoading) return;
    
    try {
      _isLoading = true;
      notifyListeners();

      debugPrint('🔄 Force reloading gamification data...');

      // Fetch fresh data from network
      await Future.wait([
        fetchUserRankData(),
        loadAllCategoryAchievements(),
        loadCompletionStats(),
      ]);
      
      // Update cache with fresh data
      await _saveUserProgressToCache();
      
      // Update timestamp
      _lastDataLoad = DateTime.now();
      await _saveToPersistentCache();
      
      debugPrint('🔄 Force reload completed successfully');
      
    } catch (e) {
      debugPrint('❌ Force reload failed: $e');
      _error = 'Failed to reload data: $e';
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// NEW: Force reload with retry logic
  Future<void> forceReloadWithRetry({int maxRetries = 3}) async {
    for (int i = 0; i < maxRetries; i++) {
      try {
        await forceReload();
        return; // Success, exit retry loop
      } catch (e) {
        if (i == maxRetries - 1) {
          // Last attempt failed, rethrow
          rethrow;
        }
        // Wait before retrying (exponential backoff)
        await Future.delayed(Duration(milliseconds: 500 * (i + 1)));
      }
    }
  }

  /// Load initial cached data synchronously
  void _loadInitialCachedData() {
    try {
      // Set default values first
      _userLevel = 1;
      _currentRank = UserRank.basementBopper;
      _nextRank = UserRank.selector;
      _xpProgress = 0.0;
      _data = {
        'current_xp': 0,
        'next_level_xp': 500,
      };
      
      // Try to load from SharedPreferences synchronously
      SharedPreferences.getInstance().then((prefs) {
        final cachedLevel = prefs.getInt(_userLevelCacheKey);
        final cachedXp = prefs.getInt(_userXpCacheKey);
        final cachedProgress = prefs.getDouble(_userProgressCacheKey);
        
        if (cachedLevel != null && cachedXp != null) {
          _userLevel = cachedLevel;
          _data['current_xp'] = cachedXp;
          if (cachedProgress != null) _xpProgress = cachedProgress;
          
          // Update ranks based on cached level
          _currentRank = UserRank.getRankForLevel(_userLevel);
          final nextRankCandidate = UserRank.allRanks
              .where((rank) => rank.requiredLevel > _userLevel)
              .firstOrNull;
          _nextRank = nextRankCandidate ?? UserRank.legend;
          
          notifyListeners();
          debugPrint('✅ Loaded initial cached data: Level $cachedLevel, XP $cachedXp');
        }
      });
    } catch (e) {
      debugPrint('❌ Error loading initial cached data: $e');
    }
  }
} 