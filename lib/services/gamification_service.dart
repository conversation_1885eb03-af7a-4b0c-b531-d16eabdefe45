import '../models/api_response.dart';
import '../models/achievement.dart';
import '../models/pin_skin.dart';
import '../models/user_achievement.dart';
import 'api_service.dart';
import 'auth_service.dart';
import 'package:flutter/foundation.dart';

class GamificationService {
  final ApiService _apiService;
  final AuthService _authService;

  GamificationService(this._apiService, this._authService);

  // Achievement Methods
  Future<List<Achievement>> getAchievements({int page = 1}) async {
    final token = await _authService.getToken();
    final response = await _apiService.get(
      '/gamification/achievements/',
      queryParams: {
        'page': page.toString(),
      },
      token: token,
    );

    print('🎵 [GamificationService] Getting achievements: ${response.data}');

    if (!response.success) return [];

    final dynamic data = response.data;
    List<dynamic> achievements;
    
    if (data is Map<String, dynamic> && data.containsKey('results')) {
      achievements = data['results'] as List<dynamic>;
    } else if (data is List<dynamic>) {
      achievements = data;
    } else {
      return [];
    }

    return achievements.map((json) => Achievement.fromJson(json)).toList();
  }

  Future<List<Achievement>> getCompletedAchievements() async {
    final token = await _authService.getToken();
    final response = await _apiService.get(
      '/gamification/achievements/completed/',
      token: token,
    );

    if (!response.success) return [];

    final List<dynamic> achievements = response.data as List<dynamic>;
    return achievements.map((json) => Achievement.fromJson(json)).toList();
  }

  Future<List<Achievement>> getInProgressAchievements() async {
    final token = await _authService.getToken();
    final response = await _apiService.get(
      '/gamification/achievements/in_progress/',
      token: token,
    );

    if (!response.success) return [];

    final List<dynamic> achievements = response.data as List<dynamic>;
    return achievements.map((json) => Achievement.fromJson(json)).toList();
  }

  Future<Achievement?> getAchievementById(int achievementId) async {
    final token = await _authService.getToken();
    final response = await _apiService.get(
      '/gamification/achievements/$achievementId/',
      token: token,
    );

    if (!response.success) return null;
    return Achievement.fromJson(response.data);
  }

  Future<ApiResponse> updateAchievementProgress(
    String achievementId,
    Map<String, dynamic> progress,
  ) async {
    final token = await _authService.getToken();
    return await _apiService.post(
      '/gamification/user-achievements/$achievementId/update_progress/',
      data: {
        'progress': progress,
      },
      token: token,
    );
  }

  // Pin Skin Methods
  Future<List<PinSkin>> getPinSkins({int page = 1}) async {
    final token = await _authService.getToken();
    final response = await _apiService.get(
      '/gamification/skins/',
      queryParams: {
        'page': page.toString(),
      },
      token: token,
    );

    if (!response.success) return [];

    final dynamic data = response.data;
    List<dynamic> skins;
    
    if (data is Map<String, dynamic> && data.containsKey('results')) {
      skins = data['results'] as List<dynamic>;
    } else if (data is List<dynamic>) {
      skins = data;
    } else {
      return [];
    }

    return skins.map((json) => PinSkin.fromJson(json)).toList();
  }

  Future<List<PinSkin>> getUnlockedSkins() async {
    final token = await _authService.getToken();
    final response = await _apiService.get(
      '/gamification/skins/unlocked/',
      token: token,
    );

    if (!response.success) return [];

    final List<dynamic> skins = response.data as List<dynamic>;
    return skins.map((json) => PinSkin.fromJson(json)).toList();
  }

  Future<List<PinSkin>> getOwnedSkins() async {
    final token = await _authService.getToken();
    final response = await _apiService.get(
      '/gamification/skins/owned/',
      token: token,
    );

    if (!response.success) return [];

    final List<dynamic> skins = response.data as List<dynamic>;
    return skins.map((json) => PinSkin.fromJson(json)).toList();
  }

  Future<PinSkin?> getPinSkinById(int skinId) async {
    final token = await _authService.getToken();
    final response = await _apiService.get(
      '/gamification/skins/$skinId/',
      token: token,
    );

    if (!response.success) return null;
    return PinSkin.fromJson(response.data);
  }

  Future<PinSkin?> equipPinSkin(int skinId) async {
    final token = await _authService.getToken();
    final response = await _apiService.post(
      '/gamification/skins/$skinId/equip/',
      token: token,
    );

    if (!response.success) return null;
    
    if (response.data is Map<String, dynamic> && response.data['success'] == true) {
      return await getPinSkinById(skinId);
    }
    
    return null;
  }

  // User Achievement Methods
  Future<List<UserAchievement>> getUserAchievements() async {
    final token = await _authService.getToken();
    final response = await _apiService.get(
      '/gamification/user-achievements/',
      token: token,
    );

    if (!response.success) return [];

    final dynamic data = response.data;
    List<dynamic> userAchievements;
    
    if (data is Map<String, dynamic> && data.containsKey('results')) {
      userAchievements = data['results'] as List<dynamic>;
    } else if (data is List<dynamic>) {
      userAchievements = data;
    } else {
      return [];
    }

    return userAchievements.map((json) => UserAchievement.fromJson(json)).toList();
  }

  Future<UserAchievement?> getUserAchievementById(int userAchievementId) async {
    final token = await _authService.getToken();
    final response = await _apiService.get(
      '/gamification/user-achievements/$userAchievementId/',
      token: token,
    );

    if (!response.success) return null;
    return UserAchievement.fromJson(response.data);
  }

  Future<ApiResponse> createUserAchievement(int achievementId) async {
    final token = await _authService.getToken();
    return await _apiService.post(
      '/gamification/user-achievements/',
      data: {
        'achievement': achievementId,
        'progress': {},
      },
      token: token,
    );
  }

  Future<ApiResponse> updateUserAchievementProgress(
    int userAchievementId,
    Map<String, dynamic> progress,
  ) async {
    final token = await _authService.getToken();
    return await _apiService.post(
      '/gamification/user-achievements/$userAchievementId/update_progress/',
      data: {
        'progress': progress,
      },
      token: token,
    );
  }

  Future<UserAchievement?> getOrCreateUserAchievement(int achievementId) async {
    try {
      final userAchievements = await getUserAchievements();
      
      UserAchievement? existingUserAchievement;
      try {
        existingUserAchievement = userAchievements
            .firstWhere((ua) => ua.achievement.id == achievementId);
      } catch (e) {
        existingUserAchievement = null;
      }
      
      if (existingUserAchievement != null) {
        return existingUserAchievement;
      }
      
      final response = await createUserAchievement(achievementId);
      if (response.success && response.data != null) {
        return UserAchievement.fromJson(response.data);
      }
      
      return null;
    } catch (e) {
      debugPrint('Error in getOrCreateUserAchievement: $e');
      return null;
    }
  }

  Future<List<Achievement>> getAchievementsByCategory(String categoryId) async {
    try {
      final token = await _authService.getToken();
      debugPrint('Loading achievements for category: $categoryId using optimized endpoints');
      
      // Use the new super fast endpoints to get both completed and in-progress
      final futures = await Future.wait([
        _apiService.get(
          '/gamification/user-achievements/completed_by_category/',
          queryParams: {'category': categoryId},
          token: token,
        ),
        _apiService.get(
          '/gamification/user-achievements/in_progress_by_category/',
          queryParams: {'category': categoryId},
          token: token,
        ),
      ]);

      final completedResponse = futures[0];
      final inProgressResponse = futures[1];

      List<Achievement> achievements = [];

      // Process completed achievements
      if (completedResponse.success) {
        final List<dynamic> completedData = completedResponse.data as List<dynamic>;
        for (final item in completedData) {
          achievements.add(Achievement.fromJson({
            'id': item['id'],
            'name': item['name'],
            'description': item['description'],
            'xp_reward': item['xp_reward'],
            'tier': item['tier'],
            'icon_name': item['icon_name'],
            'category': item['category'],
            'is_completed': true,
            'completed_at': item['completed_at'],
            'progress': {},
            'progress_percentage': 100.0,
          }));
        }
      }

      // Process in-progress achievements
      if (inProgressResponse.success) {
        final List<dynamic> inProgressData = inProgressResponse.data as List<dynamic>;
        for (final item in inProgressData) {
          achievements.add(Achievement.fromJson({
            'id': item['id'],
            'name': item['name'],
            'description': item['description'],
            'xp_reward': item['xp_reward'],
            'tier': item['tier'],
            'icon_name': item['icon_name'],
            'category': item['category'],
            'is_completed': false,
            'completed_at': null,
            'progress': item['progress'],
            'progress_percentage': item['progress']['progress_percentage'],
          }));
        }
      }

      debugPrint('✅ Loaded ${achievements.length} achievements for category $categoryId using optimized endpoints');
      return achievements;
    } catch (e) {
      debugPrint('❌ Error loading achievements for category $categoryId: $e');
      return [];
    }
  }

  Future<Map<String, dynamic>> getCompletedUserAchievements() async {
    final token = await _authService.getToken();
    final response = await _apiService.get(
      '/gamification/user-achievements/completed/',
      token: token,
    );

    if (!response.success) {
      return {
        'total_completed': 0,
        'total_xp_earned': 0,
        'completion_by_type': {},
        'completion_dates': {
          'first_completion': null,
          'latest_completion': null,
        }
      };
    }

    return response.data as Map<String, dynamic>;
  }

  /// Get completed achievements for a specific category
  Future<List<Achievement>> getCompletedAchievementsByCategory(String categoryId) async {
    try {
      final token = await _authService.getToken();
      debugPrint('Loading completed achievements for category: $categoryId using optimized endpoint');
      
      final response = await _apiService.get(
        '/gamification/user-achievements/completed_by_category/',
        queryParams: {'category': categoryId},
        token: token,
      );

      if (!response.success) {
        debugPrint('Failed to load completed achievements for category $categoryId: ${response.message}');
        return [];
      }

      final List<dynamic> completedData = response.data as List<dynamic>;
      final achievements = completedData.map((item) {
        return Achievement.fromJson({
          'id': item['id'],
          'name': item['name'],
          'description': item['description'],
          'xp_reward': item['xp_reward'],
          'tier': item['tier'],
          'icon_name': item['icon_name'],
          'category': item['category'],
          'is_completed': true,
          'completed_at': item['completed_at'],
          'progress': {},
          'progress_percentage': 100.0,
        });
      }).toList();

      debugPrint('✅ Loaded ${achievements.length} completed achievements for category $categoryId');
      return achievements;
    } catch (e) {
      debugPrint('❌ Error loading completed achievements for category $categoryId: $e');
      return [];
    }
  }

  /// Get in-progress achievements for a specific category  
  Future<List<Achievement>> getInProgressAchievementsByCategory(String categoryId) async {
    try {
      final token = await _authService.getToken();
      debugPrint('Loading in-progress achievements for category: $categoryId using optimized endpoint');
      
      final response = await _apiService.get(
        '/gamification/user-achievements/in_progress_by_category/',
        queryParams: {'category': categoryId},
        token: token,
      );

      if (!response.success) {
        debugPrint('Failed to load in-progress achievements for category $categoryId: ${response.message}');
        return [];
      }

      final List<dynamic> inProgressData = response.data as List<dynamic>;
      final achievements = inProgressData.map((item) {
        return Achievement.fromJson({
          'id': item['id'],
          'name': item['name'],
          'description': item['description'],
          'xp_reward': item['xp_reward'],
          'tier': item['tier'],
          'icon_name': item['icon_name'],
          'category': item['category'],
          'is_completed': false,
          'completed_at': null,
          'progress': item['progress'],
          'progress_percentage': item['progress']['progress_percentage'],
        });
      }).toList();

      debugPrint('✅ Loaded ${achievements.length} in-progress achievements for category $categoryId');
      return achievements;
    } catch (e) {
      debugPrint('❌ Error loading in-progress achievements for category $categoryId: $e');
      return [];
    }
  }

  /// Get a specific user achievement with full progress details
  Future<Achievement?> getUserAchievementDetails(int userAchievementId) async {
    try {
      final token = await _authService.getToken();
      
      final response = await _apiService.get(
        '/gamification/user-achievements/$userAchievementId/',
        token: token,
      );

      if (!response.success) return null;
      
      final userAchievement = UserAchievement.fromJson(response.data);
      return userAchievement.achievement.copyWith(
        progress: userAchievement.progress,
        isCompleted: userAchievement.isCompleted,
        completedAt: userAchievement.completedAt,
      );
    } catch (e) {
      debugPrint('Error loading user achievement details: $e');
      return null;
    }
  }

  /// Get completed summary for all categories (super fast dashboard endpoint)
  Future<Map<String, dynamic>> getCompletedSummary() async {
    try {
      final token = await _authService.getToken();
      debugPrint('Loading completed summary using optimized endpoint');
      
      final response = await _apiService.get(
        '/gamification/user-achievements/completed_summary/',
        token: token,
      );

      if (!response.success) {
        debugPrint('Failed to load completed summary: ${response.message}');
        return {
          'location': {'count': 0, 'recent': []},
          'artist': {'count': 0, 'recent': []},
          'genre': {'count': 0, 'recent': []},
          'social': {'count': 0, 'recent': []},
          'total_completed': 0,
          'total_xp': 0,
        };
      }

      final summary = response.data as Map<String, dynamic>;
      debugPrint('✅ Loaded completed summary: ${summary['total_completed']} total completed, ${summary['total_xp']} total XP');
      return summary;
    } catch (e) {
      debugPrint('❌ Error loading completed summary: $e');
      return {
        'location': {'count': 0, 'recent': []},
        'artist': {'count': 0, 'recent': []},
        'genre': {'count': 0, 'recent': []},
        'social': {'count': 0, 'recent': []},
        'total_completed': 0,
        'total_xp': 0,
      };
    }
  }
} 