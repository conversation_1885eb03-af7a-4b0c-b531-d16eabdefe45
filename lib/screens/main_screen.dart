import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../screens/profile/profile_screen.dart';
import '../screens/friends/friends_screen.dart';
import '../screens/map/map_screen.dart';
import '../screens/gamification/challenges_screen.dart';
import '../screens/explore/explore_screen.dart';
import '../widgets/navigation/bottom_navigation_bar.dart';
import '../utils/navigation_helper.dart';
import '../utils/app_tab.dart';
import '../widgets/music/now_playing_bar.dart';
import '../widgets/music/apple_now_playing_bar.dart';
import '../providers/spotify_provider.dart';
import '../providers/apple_music_provider.dart';
import '../providers/youtube_provider.dart';
import '../providers/auth_provider.dart';

/// Main screen that contains the bottom navigation and manages tab switching
class MainScreen extends StatefulWidget {
  const MainScreen({Key? key}) : super(key: key);

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> {
  int _currentIndex = AppTab.map.index; // Default to map as the starting screen
  final PageController _pageController = PageController(initialPage: AppTab.map.index);
  
  // List of top-level screens for each tab
  final List<Widget> _screens = [
    const ProfileScreen(showBottomNav: true),
    const ExploreScreen(showBottomNav: true),
    const MapScreen(),
    const ChallengesScreen(showBottomNav: true),
    const FriendsScreen(showBottomNav: true),
  ];

  @override
  void initState() {
    super.initState();
    // Set the AuthProvider context for OneSignal initialization
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      authProvider.setCurrentContext(context);
    });
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  void _onTabSelected(int index) {
    if (_currentIndex == index) return;
    
    setState(() {
      _currentIndex = index;
    });
    
    // Animate to the selected page
    _pageController.animateToPage(
      index,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  void _onAddPinPressed() {
    // Navigate to the add pin screen or show a modal
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.7,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: const Center(child: Text('Add Pin Form')), // Replace with your actual form
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Consumer3<SpotifyProvider, AppleMusicProvider, YouTubeProvider>(
      builder: (context, spotifyProvider, appleMusicProvider, youtubeProvider, child) {
        // Only show now playing bar for Spotify and Apple Music, NOT for YouTube
        // YouTube has its own embedded player so we don't want to show an additional bar
        final hasSpotifyActive = spotifyProvider.hasActivePlayback && spotifyProvider.currentTrack != null;
        final hasAppleMusicActive = appleMusicProvider.isPlaying && appleMusicProvider.currentTrack != null;
        final hasActiveMusic = hasSpotifyActive || hasAppleMusicActive; // Exclude YouTube
        
    final bottomPadding = hasActiveMusic ? 68.0 + kBottomNavigationBarHeight : kBottomNavigationBarHeight;

    return Scaffold(
      body: Stack(
        fit: StackFit.expand,
        children: [
          // Main content with padding for now playing bar and bottom nav
          Positioned.fill(
            bottom: bottomPadding,
            child: PageView(
              controller: _pageController,
              physics: _currentIndex == AppTab.map.index 
                  ? const NeverScrollableScrollPhysics()
                  : const PageScrollPhysics(),
              children: _screens,
              onPageChanged: (index) {
                setState(() => _currentIndex = index);
              },
            ),
          ),

              // Now Playing Bar - show appropriate bar based on active service
          if (hasActiveMusic)
            Positioned(
              left: 0,
              right: 0,
              bottom: kBottomNavigationBarHeight,
                  child: hasAppleMusicActive && !hasSpotifyActive
                      ? const AppleNowPlayingBar()
                      : const NowPlayingBar(), // Default to Spotify bar
            ),

          // Bottom Navigation Bar
          Positioned(
            left: 0,
            right: 0,
            bottom: 0,
            child: SafeArea(
              top: false,
              child: MusicPinBottomNavBar.auto(
                context: context,
                onTabSelected: _onTabSelected,
                onAddPinPressed: _onAddPinPressed,
              ),
            ),
          ),
        ],
      ),
      extendBody: true,
        );
      },
    );
  }
} 