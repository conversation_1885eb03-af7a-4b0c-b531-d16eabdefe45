import 'dart:async';
import 'package:flutter/foundation.dart';

/// Layer State Machine for reliable map layer management
/// Implements deterministic state transitions with validation and recovery
class LayerStateMachine {
  static const Duration _transitionTimeout = Duration(seconds: 10);
  static const Duration _validationInterval = Duration(seconds: 5);
  
  LayerState _currentState = LayerState.initializing;
  LayerState? _previousState;
  Timer? _transitionTimer;
  Timer? _validationTimer;
  DateTime? _lastTransitionTime;
  Map<String, dynamic> _stateContext = {};
  
  // Callbacks for state changes and errors
  void Function(LayerState oldState, LayerState newState)? onStateChanged;
  void Function(LayerState state, String error)? onStateError;
  void Function(String message)? onValidationFailed;
  
  /// Current layer state
  LayerState get currentState => _currentState;
  
  /// Previous layer state for rollback purposes
  LayerState? get previousState => _previousState;
  
  /// Whether the layers are in a stable, usable state
  bool get isStable => _currentState == LayerState.showingClusters || 
                      _currentState == LayerState.showingIndividual;
  
  /// Whether the state machine is currently transitioning
  bool get isTransitioning => _currentState == LayerState.transitioning;
  
  /// Whether there's an error state that needs recovery
  bool get needsRecovery => _currentState == LayerState.error;
  
  /// Initialize the state machine with validation monitoring
  void initialize() {
    debugPrint('📍 Layer State Machine initialized');
    _startPeriodicValidation();
  }
  
  /// Attempt to transition to a new state with validation
  Future<bool> transitionTo(LayerState newState, {Map<String, dynamic>? context}) async {
    debugPrint('📍 State transition requested: $_currentState → $newState');
    
    // If the requested state is the same as the current state, treat this as a no-op
    if (_currentState == newState) {
      debugPrint('📍 🚦 No-op transition: already in $newState');
      return true; // Consider it successful without further processing
    }
    
    // Validate the transition is allowed
    if (!_isValidTransition(_currentState, newState)) {
      debugPrint('📍 ❌ Invalid transition: $_currentState → $newState');
      return false;
    }
    
    // Store context for this transition
    if (context != null) {
      _stateContext.addAll(context);
    }
    
    // Set transitioning state for complex transitions
    if (_requiresTransitioningState(_currentState, newState)) {
      await _setStateInternal(LayerState.transitioning);
    }
    
    // Perform the actual transition with timeout
    try {
      final success = await _performTransitionWithTimeout(newState);
      if (success) {
        await _setStateInternal(newState);
        debugPrint('📍 ✅ State transition successful: $_currentState');
        return true;
      } else {
        debugPrint('📍 ❌ State transition failed, attempting recovery');
        await _handleTransitionFailure();
        return false;
      }
    } catch (e) {
      debugPrint('📍 ❌ State transition error: $e');
      await _handleTransitionFailure();
      return false;
    }
  }
  
  /// Force set state (for emergency recovery)
  Future<void> forceState(LayerState state, String reason) async {
    debugPrint('📍 🚨 Force state change: $_currentState → $state (reason: $reason)');
    await _setStateInternal(state);
  }
  
  /// Validate current state and attempt automatic correction
  Future<bool> validateAndCorrect() async {
    debugPrint('📍 🔍 Validating current state: $_currentState');
    
    try {
      final isValid = await _validateCurrentState();
      if (!isValid) {
        debugPrint('📍 ⚠️ State validation failed, attempting correction');
        onValidationFailed?.call('State validation failed: $_currentState');
        return await _attemptStateCorrection();
      }
      
      debugPrint('📍 ✅ State validation passed');
      return true;
    } catch (e) {
      debugPrint('📍 ❌ State validation error: $e');
      await _handleValidationError(e.toString());
      return false;
    }
  }
  
  /// Create a state snapshot for debugging
  Map<String, dynamic> createSnapshot() {
    return {
      'currentState': _currentState.toString(),
      'previousState': _previousState?.toString(),
      'lastTransitionTime': _lastTransitionTime?.toIso8601String(),
      'isTransitioning': isTransitioning,
      'isStable': isStable,
      'needsRecovery': needsRecovery,
      'context': Map<String, dynamic>.from(_stateContext),
    };
  }
  
  /// Restore from a state snapshot
  void restoreFromSnapshot(Map<String, dynamic> snapshot) {
    debugPrint('📍 🔄 Restoring state from snapshot');
    
    try {
      final currentStateStr = snapshot['currentState'] as String?;
      final previousStateStr = snapshot['previousState'] as String?;
      
      if (currentStateStr != null) {
        _currentState = LayerState.values.firstWhere(
          (state) => state.toString() == currentStateStr,
          orElse: () => LayerState.error,
        );
      }
      
      if (previousStateStr != null) {
        _previousState = LayerState.values.firstWhere(
          (state) => state.toString() == previousStateStr,
          orElse: () => LayerState.error,
        );
      }
      
      final contextMap = snapshot['context'] as Map<String, dynamic>?;
      if (contextMap != null) {
        _stateContext = Map<String, dynamic>.from(contextMap);
      }
      
      debugPrint('📍 ✅ State restored from snapshot: $_currentState');
    } catch (e) {
      debugPrint('📍 ❌ Failed to restore from snapshot: $e');
      _currentState = LayerState.error;
    }
  }
  
  /// Internal state setting with proper tracking
  Future<void> _setStateInternal(LayerState newState) async {
    final oldState = _currentState;
    _previousState = oldState;
    _currentState = newState;
    _lastTransitionTime = DateTime.now();
    
    // Reset transition timer
    _transitionTimer?.cancel();
    _transitionTimer = null;
    
    // Notify state change
    onStateChanged?.call(oldState, newState);
    
    debugPrint('📍 State updated: $oldState → $newState');
  }
  
  /// Check if a state transition is valid
  bool _isValidTransition(LayerState from, LayerState to) {
    // Allow any transition from error state (recovery)
    if (from == LayerState.error) return true;
    
    // Allow transition to error from any state
    if (to == LayerState.error) return true;
    
    // Allow no-op transitions to the same state
    if (from == to) return true;
    
    // Define valid transitions
    switch (from) {
      case LayerState.initializing:
        return to == LayerState.showingClusters || 
               to == LayerState.showingIndividual ||
               to == LayerState.transitioning;
      
      case LayerState.showingClusters:
        return to == LayerState.showingIndividual || 
               to == LayerState.transitioning;
      
      case LayerState.showingIndividual:
        return to == LayerState.showingClusters || 
               to == LayerState.transitioning;
      
      case LayerState.transitioning:
        return to == LayerState.showingClusters || 
               to == LayerState.showingIndividual;
      
      case LayerState.error:
        return true; // Error state can transition to any state
    }
  }
  
  /// Check if a transition requires the transitioning state
  bool _requiresTransitioningState(LayerState from, LayerState to) {
    // Complex transitions that require intermediate state
    return (from == LayerState.showingClusters && to == LayerState.showingIndividual) ||
           (from == LayerState.showingIndividual && to == LayerState.showingClusters) ||
           (from == LayerState.initializing && (to == LayerState.showingClusters || to == LayerState.showingIndividual));
  }
  
  /// Perform transition with timeout protection
  Future<bool> _performTransitionWithTimeout(LayerState newState) async {
    final completer = Completer<bool>();
    
    // Set up timeout timer
    _transitionTimer = Timer(_transitionTimeout, () {
      if (!completer.isCompleted) {
        debugPrint('📍 ⏱️ State transition timeout');
        completer.complete(false);
      }
    });
    
    // Simulate transition logic (to be implemented by consumer)
    // In real implementation, this would trigger actual layer operations
    Timer(Duration(milliseconds: 100), () {
      if (!completer.isCompleted) {
        completer.complete(true);
      }
    });
    
    return completer.future;
  }
  
  /// Handle transition failure with recovery
  Future<void> _handleTransitionFailure() async {
    await _setStateInternal(LayerState.error);
    onStateError?.call(_currentState, 'State transition failed');
    
    // Attempt automatic recovery after a delay
    Timer(Duration(seconds: 2), () {
      _attemptAutomaticRecovery();
    });
  }
  
  /// Validate the current state
  Future<bool> _validateCurrentState() async {
    // State-specific validation logic
    switch (_currentState) {
      case LayerState.initializing:
        // Should not stay in initializing state for too long
        if (_lastTransitionTime != null) {
          final elapsed = DateTime.now().difference(_lastTransitionTime!);
          return elapsed.inSeconds < 30;
        }
        return true;
      
      case LayerState.showingClusters:
      case LayerState.showingIndividual:
        // These are stable states - always valid
        return true;
      
      case LayerState.transitioning:
        // Should not stay in transitioning state for too long
        if (_lastTransitionTime != null) {
          final elapsed = DateTime.now().difference(_lastTransitionTime!);
          return elapsed.inSeconds < 15;
        }
        return false;
      
      case LayerState.error:
        // Error state is always invalid and needs recovery
        return false;
    }
  }
  
  /// Attempt to correct invalid state
  Future<bool> _attemptStateCorrection() async {
    debugPrint('📍 🔧 Attempting state correction');
    
    // Recovery strategy based on current state
    switch (_currentState) {
      case LayerState.initializing:
        // Try to transition to a stable state
        return await transitionTo(LayerState.showingClusters);
      
      case LayerState.transitioning:
        // Rollback to previous stable state if available
        if (_previousState != null && _previousState != LayerState.transitioning) {
          return await transitionTo(_previousState!);
        }
        return await transitionTo(LayerState.showingClusters);
      
      case LayerState.error:
        return await _attemptAutomaticRecovery();
      
      default:
        return true; // Stable states don't need correction
    }
  }
  
  /// Handle validation errors
  Future<void> _handleValidationError(String error) async {
    await _setStateInternal(LayerState.error);
    onStateError?.call(_currentState, 'Validation error: $error');
  }
  
  /// Attempt automatic recovery from error state
  Future<bool> _attemptAutomaticRecovery() async {
    debugPrint('📍 🚑 Attempting automatic recovery');
    
    // Try to recover to the most likely stable state
    final targetState = _previousState == LayerState.showingIndividual 
        ? LayerState.showingIndividual 
        : LayerState.showingClusters;
    
    return await transitionTo(targetState, context: {'recovery': true});
  }
  
  /// Start periodic state validation
  void _startPeriodicValidation() {
    _validationTimer?.cancel();
    _validationTimer = Timer.periodic(_validationInterval, (_) {
      validateAndCorrect();
    });
  }
  
  /// Dispose of resources
  void dispose() {
    _transitionTimer?.cancel();
    _validationTimer?.cancel();
    _transitionTimer = null;
    _validationTimer = null;
    onStateChanged = null;
    onStateError = null;
    onValidationFailed = null;
    debugPrint('📍 Layer State Machine disposed');
  }
}

/// Possible states for map layers
enum LayerState {
  /// Initial state when layers are being set up
  initializing,
  
  /// Cluster layer is visible, individual layer is hidden
  showingClusters,
  
  /// Individual pin layer is visible, cluster layer is hidden
  showingIndividual,
  
  /// Currently switching between layer states
  transitioning,
  
  /// Error state requiring recovery
  error,
}
