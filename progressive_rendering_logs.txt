flutter: 📍 🔍 Validating current state: LayerState.showingClusters
flutter: 📍 ✅ State validation passed
flutter: 📍 🔍 Validating current state: LayerState.showingClusters
flutter: 📍 ✅ State validation passed
flutter: 📍 🔍 Validating current state: LayerState.showingClusters
flutter: 📍 ✅ State validation passed
flutter: 📍 🔍 Validating current state: LayerState.showingClusters
flutter: 📍 ✅ State validation passed
flutter: 📍 🔍 Validating current state: LayerState.showingClusters
flutter: 📍 ✅ State validation passed
flutter: 🎨 Changing map style from MapStyle.cyberpunk to MapStyle.neon with complete rebuild
flutter: 💾 Saved map style: MapStyle.neon
flutter: 🔄 Stopping all animations before screen rebuild...
flutter: ✅ All animations stopped successfully
flutter: Map Screen: didPop - screen popped, animations paused
flutter: 🗺️ Navigating to map screen
flutter: 🔐 Auth state: token=true, user=true, isAuthenticated=true
flutter: 👤 Current user: mnm967 (<EMAIL>)
flutter: 🎯 Display name computed: mnm967
flutter: 🔑 Token starts with: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************._xof7K5R7dipGFWkrN4smtHyPorC9_XmpEHDGkLmfyY
flutter: 🔐 Auth check for map route - isAuthenticated: true
flutter: 🚀 MapScreen: Time to init: 0ms
flutter: 🚀 MapScreen: Requesting location permission
flutter: 🗺️ LocationManager: Requesting location permission
flutter: 🗺️ LocationManager: Status changed from LocationStatus.available to LocationStatus.determining
flutter: 🎵 [MapScreen] Build - currentlyPlayingPinId: null
flutter: 🎵 [MapScreen] Build - hasCurrentlyPlayingPin: false
flutter: 🎵 [MapScreen] _buildCaptionPopup called
flutter: 🎵 [MapScreen] - currentlyPlayingPinId: null
flutter: 🎵 [MapScreen] - currentlyPlayingPinData: null
flutter: 🎵 [MapScreen] ❌ No currently playing pin or pin data
flutter: NavigationHelper: Getting current tab...
flutter: NavigationHelper: Checking route - name: /map, looking for: /profile, match: false
flutter: NavigationHelper: Checking route - name: /map, looking for: /explore, match: false
flutter: NavigationHelper: Checking route - name: /map, looking for: /friends, match: false
flutter: NavigationHelper: Checking route - name: /map, looking for: /challenges, match: false
flutter: NavigationHelper: Checking route - name: /map, looking for: /map, match: true
flutter: NavigationHelper: Current tab is MAP (explicit)
flutter: 🌧️ Setting weather type to: rain
flutter: 🌧️ Weather visual effects initialized
flutter: 🌧️ Checking location and updating weather...
flutter: 🌍 Location monitoring started
flutter: 📍 Layer State Machine initialized
flutter: 🟡 [MAP-INIT] === REGISTERING CALLBACKS ===
flutter: 🟡 [MAP-INIT] MapProvider via Provider.of: Instance of 'MapProvider'
flutter: 🟡 [MAP-INIT] MapProvider hashCode: 969213914
flutter: 🟡 [MAP-INIT] Local _mapProvider: null
flutter: 🟡 [MAP-INIT] Local _mapProvider hashCode: null
flutter: MapProvider: Force refresh callback registered
flutter: MapProvider: Optimistic pin callback registered
flutter: 🟡 [MAP-INIT] ✅ Callbacks registered on Provider.of instance!
flutter: 🟡 [MAP-INIT] === CALLBACK REGISTRATION COMPLETE ===
flutter: 📍 Pin cache cleared (force: true, size was: 0)
flutter: 📍 Skipping pin bounce animation during initial load
flutter: 🌧️ Setting weather type to: rain
flutter: Map Screen: didPush - screen is now visible
flutter: 🌧️ Initial screen size update: 390.0x844.0
flutter: ✅ MapScreen: User profile already loaded
flutter: 📍 MapProvider update ignored - map not ready
flutter: 🎨 Loaded saved map style: MapStyle.neon
flutter: 🎭 Loaded saved effects state - weather: false
flutter: 🔐 Auth state: token=true, user=true, isAuthenticated=true
flutter: 👤 Current user: mnm967 (<EMAIL>)
flutter: 🎯 Display name computed: mnm967
flutter: 🔑 Token starts with: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************._xof7K5R7dipGFWkrN4smtHyPorC9_XmpEHDGkLmfyY
flutter: 🤖 MapScreen: Initializing Global AI Provider for authenticated user...
flutter: 🤖 Global AI Provider already initialized or initializing
flutter: ✅ MapScreen: Global AI Provider initialized successfully
flutter: 📍 SmartFetch: Location permission granted - will refresh on next location update
flutter: 🌧️ Found last known position: 36.14582461040516, -86.80003388880716 (1.0 minutes old)
flutter: 🌧️ Using recent last known position
flutter: 🌧️ Got position: 36.1458, -86.8000
flutter: 🌧️ Updating weather for location: 36.1458,-86.8000
flutter: 🌧️ Fetching weather data for: 36.1458,-86.8000
flutter: 🌧️ Making weather API request to: http://api.weatherapi.com/v1/current.json?key=41051434ead448cdba5191912252206&q=36.1458,-86.8000&aqi=no
flutter: 🎵 [MapScreen] Build - currentlyPlayingPinId: null
flutter: 🎵 [MapScreen] Build - hasCurrentlyPlayingPin: false
flutter: 🎵 [MapScreen] _buildCaptionPopup called
flutter: 🎵 [MapScreen] - currentlyPlayingPinId: null
flutter: 🎵 [MapScreen] - currentlyPlayingPinData: null
flutter: 🎵 [MapScreen] ❌ No currently playing pin or pin data
flutter: NavigationHelper: Getting current tab...
flutter: NavigationHelper: Checking route - name: /map, looking for: /profile, match: false
flutter: NavigationHelper: Checking route - name: /map, looking for: /explore, match: false
flutter: NavigationHelper: Checking route - name: /map, looking for: /friends, match: false
flutter: NavigationHelper: Checking route - name: /map, looking for: /challenges, match: false
flutter: NavigationHelper: Checking route - name: /map, looking for: /map, match: true
flutter: NavigationHelper: Current tab is MAP (explicit)
flutter: 🎵 [MapScreen] Build - currentlyPlayingPinId: null
flutter: 🎵 [MapScreen] Build - hasCurrentlyPlayingPin: false
flutter: 🎵 [MapScreen] _buildCaptionPopup called
flutter: 🎵 [MapScreen] - currentlyPlayingPinId: null
flutter: 🎵 [MapScreen] - currentlyPlayingPinData: null
flutter: 🎵 [MapScreen] ❌ No currently playing pin or pin data
flutter: NavigationHelper: Getting current tab...
flutter: NavigationHelper: Checking route - name: /map, looking for: /profile, match: false
flutter: NavigationHelper: Checking route - name: /map, looking for: /explore, match: false
flutter: NavigationHelper: Checking route - name: /map, looking for: /friends, match: false
flutter: NavigationHelper: Checking route - name: /map, looking for: /challenges, match: false
flutter: NavigationHelper: Checking route - name: /map, looking for: /map, match: true
flutter: NavigationHelper: Current tab is MAP (explicit)
NSPredicate: Use of 'mgl_does:have:' as an NSExpression function is forbidden.
flutter: 🗺️ Map created successfully
flutter: Map style loaded successfully
flutter: 🧹 Cleaned up all style-specific layers and sources
NSPredicate: Use of 'mgl_does:have:' as an NSExpression function is forbidden.
flutter: 🗺️ Map created successfully
flutter: Map style loaded successfully
flutter: 🧹 Cleaned up all style-specific layers and sources
flutter: 🗺️ LocationManager: Permission request result: LocationPermission.whileInUse
flutter: 🗺️ LocationManager: Status changed from LocationStatus.determining to LocationStatus.available
flutter: 📍 MapProvider update ignored - map not ready
flutter: 🗺️ LocationManager: Requesting current location (request #5)
flutter: 🗺️ LocationManager: Status changed from LocationStatus.available to LocationStatus.determining
flutter: 🗺️ LocationManager: Location request started at T+88839ms since app startup
flutter: 📍 MapProvider update ignored - map not ready
flutter: 📍 MapProvider update ignored - map not ready
flutter: 📍 MapProvider update ignored - map not ready
flutter: 🎵 [MapScreen] Build - currentlyPlayingPinId: null
flutter: 🎵 [MapScreen] Build - hasCurrentlyPlayingPin: false
flutter: 🎵 [MapScreen] _buildCaptionPopup called
flutter: 🎵 [MapScreen] - currentlyPlayingPinId: null
flutter: 🎵 [MapScreen] - currentlyPlayingPinData: null
flutter: 🎵 [MapScreen] ❌ No currently playing pin or pin data
flutter: NavigationHelper: Getting current tab...
flutter: NavigationHelper: Checking route - name: /map, looking for: /profile, match: false
flutter: NavigationHelper: Checking route - name: /map, looking for: /explore, match: false
flutter: NavigationHelper: Checking route - name: /map, looking for: /friends, match: false
flutter: NavigationHelper: Checking route - name: /map, looking for: /challenges, match: false
flutter: NavigationHelper: Checking route - name: /map, looking for: /map, match: true
flutter: NavigationHelper: Current tab is MAP (explicit)
flutter: 🎵 [MapScreen] Build - currentlyPlayingPinId: null
flutter: 🎵 [MapScreen] Build - hasCurrentlyPlayingPin: false
flutter: 🎵 [MapScreen] _buildCaptionPopup called
flutter: 🎵 [MapScreen] - currentlyPlayingPinId: null
flutter: 🎵 [MapScreen] - currentlyPlayingPinData: null
flutter: 🎵 [MapScreen] ❌ No currently playing pin or pin data
flutter: NavigationHelper: Getting current tab...
flutter: NavigationHelper: Checking route - name: /map, looking for: /profile, match: false
flutter: NavigationHelper: Checking route - name: /map, looking for: /explore, match: false
flutter: NavigationHelper: Checking route - name: /map, looking for: /friends, match: false
flutter: NavigationHelper: Checking route - name: /map, looking for: /challenges, match: false
flutter: NavigationHelper: Checking route - name: /map, looking for: /map, match: true
flutter: NavigationHelper: Current tab is MAP (explicit)
flutter: ✅ Openmaptiles source already exists
flutter: ✅ Openmaptiles source already exists
flutter: Customizing map style for Snapchat look...
flutter: 🛣️ Added realistic road surfaces
flutter: 🔄 [STYLE-CHANGE] Re-registering callbacks after style change...
flutter: MapProvider: Optimistic pin callback registered
flutter: MapProvider: Force refresh callback registered
flutter: 🔄 [STYLE-CHANGE] ✅ Callbacks re-registered successfully!
flutter: 🔄 [STYLE-CHANGE] MapProvider hashCode: 969213914
flutter: 📍 Pin cache cleared (force: true, size was: 0)
flutter: ✅ Map fully initialized and ready
NSPredicate: Use of 'MLN_IF' as an NSExpression function is forbidden.
flutter: No existing park layer to remove
flutter: Successfully added custom parks layer
flutter: Successfully added grass layer
flutter: Successfully added forest layer
flutter: Successfully added custom water layer
flutter: Successfully added water outline
NSPredicate: Use of 'mgl_does:have:' as an NSExpression function is forbidden.
NSPredicate: Use of 'mgl_does:have:' as an NSExpression function is forbidden.
flutter: 🗺️ Symbol settings configured
flutter: 🗺️ Symbol tap listener registered
flutter: 🗺️ Camera position listener added
flutter: 🗺️ Initial zoom set to: 16.5
flutter: 📍 🏗️ Initializing pin layers during map creation...
flutter: Successfully added Snapchat-style tree icon
flutter: 🎨 Enhanced user avatar added successfully
flutter: 🎨 Enhanced user avatar added successfully
flutter: ✅ Created new user avatar symbol
flutter: 📍 User avatar image already added, skipping
flutter: MapProvider: Force refresh callback cleared
flutter: MapProvider: Optimistic pin callback cleared
flutter: 🛑 Cluster isolate disposed
flutter: 🛑 Parallel pin feature isolate disposed
flutter: 📍 Pin cache cleared (force: true, size was: 0)
flutter: 🌧️ Weather visual effects manager disposed
flutter: 📍 SmartFetch: Manager disposed
flutter: 🎯 Enhanced gesture manager disposed
flutter: 📍 Layer State Machine disposed
flutter: 🌧️ Weather API response received successfully
flutter: 🌧️ Weather API Response: {location: {name: Nashville, region: Tennessee, country: United States of America, lat: 36.166, lon: -86.784, tz_id: America/Chicago, localtime_epoch: **********, localtime: 2025-07-30 10:40}, current: {last_updated_epoch: **********, last_updated: 2025-07-30 10:30, temp_c: 31.1, temp_f: 88.0, is_day: 1, condition: {text: Partly cloudy, icon: //cdn.weatherapi.com/weather/64x64/day/116.png, code: 1003}, wind_mph: 3.6, wind_kph: 5.8, wind_degree: 75, wind_dir: ENE, pressure_mb: 1018.0, pressure_in: 30.05, precip_mm: 0.0, precip_in: 0.0, humidity: 63, cloud: 50, feelslike_c: 35.2, feelslike_f: 95.4, windchill_c: 32.2, windchill_f: 89.9, heatindex_c: 38.0, heatindex_f: 100.4, dewpoint_c: 22.8, dewpoint_f: 73.0, vis_km: 16.0, vis_miles: 9.0, uv: 5.7, gust_mph: 4.1, gust_kph: 6.6}}
flutter: 🌧️ Weather details: text="partly cloudy", code=1003, precip=0.0mm, isDay=true
flutter: 🌧️ Wind: 5.8kph at 75° -> offset(3.0023009231892406, 11.204739584953192)
flutter: 🌧️ ═══ WEATHER DETAILS ═══
flutter: 🌧️ Location: Nashville, Tennessee, United States of America
flutter: 🌧️ Temperature: 31.1°C (88.0°F)
flutter: 🌧️ Condition: Partly cloudy (code: 1003)
flutter: 🌧️ Precipitation: 0.0mm
flutter: 🌧️ Wind: 5.8kph at 75° (ENE)
flutter: 🌧️ Humidity: 63%
flutter: 🌧️ Cloud Cover: 50%
flutter: 🌧️ Visibility: 16.0km
flutter: 🌧️ UV Index: 5.7
flutter: 🌧️ Is Day: Yes
flutter: 🌧️ ═══════════════════════
flutter: 🌧️ Parsed Weather Type: clouds
flutter: 🌧️ Wind Vector: (3.0, 11.2)
flutter: 🌧️ Setting weather type to: clouds
flutter: 📍 _updateMapPins called - updating from cache only (no API calls)
flutter: 📍 No cached pins available for display
flutter: Successfully added tree symbols to parks
flutter: Successfully added tree symbols to landcover
flutter: 📍 ✅ Created empty individual pins layer
flutter: 📍 ✅ Created empty cluster layer
flutter: 📍 ✅ Initialized empty glow sources
flutter: 📍 ✅ Pin layers initialized successfully during map creation
flutter: 🎨 Applying neon effects...
flutter: 🏢 Applying neon building colors...
flutter: 🏢 Removed all building layers to prevent z-fighting
flutter: 🎨 Neon effects applied successfully
NSPredicate: Use of 'MLN_IF' as an NSExpression function is forbidden.
NSPredicate: Use of 'MLN_IF' as an NSExpression function is forbidden.
NSPredicate: Use of 'MLN_IF' as an NSExpression function is forbidden.
flutter: 🏢 Successfully added neon 3D buildings
flutter: 🏢 Auto-tilting map to reveal 3D buildings (zoom: 16.5)
flutter: 🏷️ Adding all labels on top...
flutter: 🏷️ All labels added on top
NSPredicate: Use of 'MLN_IF' as an NSExpression function is forbidden.
NSPredicate: Use of 'mgl_does:have:' as an NSExpression function is forbidden.
NSPredicate: Use of 'mgl_does:have:' as an NSExpression function is forbidden.
NSPredicate: Use of 'mgl_attributed:' as an NSExpression function is forbidden.
NSPredicate: Use of 'mgl_does:have:' as an NSExpression function is forbidden.
NSPredicate: Use of 'mgl_does:have:' as an NSExpression function is forbidden.
NSPredicate: Use of 'mgl_does:have:' as an NSExpression function is forbidden.
NSPredicate: Use of 'mgl_does:have:' as an NSExpression function is forbidden.
flutter: 🏢 Added building icons
flutter: 🏷️ Added enhanced building labels
flutter: ✅ Added POI icon: poi-restaurant
flutter: ✅ Added POI icon: poi-cafe
flutter: ✅ Added POI icon: poi-hotel
flutter: ✅ Added POI icon: poi-hospital
flutter: ✅ Added POI icon: poi-school
flutter: ✅ Added POI icon: poi-bank
flutter: ✅ Added POI icon: poi-shop
flutter: ✅ Added POI icon: poi-museum
flutter: ✅ Added POI icon: poi-library
flutter: ✅ Added POI icon: poi-park
flutter: ✅ Added POI icon: poi-default
flutter: 🎨 No cached pins, applying filter
flutter: 📍 User avatar image already added, skipping
flutter: ✅ Created new user avatar symbol
flutter: 🔝 User avatar repositioned on top of all layers
flutter: 🧭 Compass stream initialized
NSPredicate: Use of 'MLN_IF' as an NSExpression function is forbidden.
NSPredicate: Use of 'mgl_does:have:' as an NSExpression function is forbidden.
flutter: 🎯 Added POI labels with icons
flutter: 📍 Cleared 0 pins and 0 clusters
flutter: 📍 Filter change - using smart fetch manager
flutter: 📍 SmartFetch: Updated filter to PinFilterType.all
flutter: 📍 SmartFetch: Force refresh requested
flutter: 📍 SmartFetch: Started fetching pins
flutter: 🔍 Starting scanning animation
flutter: 📍 SmartFetch: Starting API call for filter PinFilterType.all
flutter: 🔍 Fetching nearby pins at lat: 36.145829333758876, lng: -86.80002463884857, radius: 5000.0
flutter: 🔍 Added scanning layers on top of all other layers
flutter: 🗺️ LocationManager: Received foreground position update: lat: 36.1458306792511, lng: -86.8000220035813, accuracy: 8.01027344943496m, speed: 0.0m/s
flutter: 🗺️ LocationManager: GPS noise detected - distance: 0.3m, speed: 0.0m/s, accuracy: 8.0m
flutter: 🗺️ LocationManager: Filtered GPS noise (total filtered: 3)
flutter: 🗺️ LocationManager: Location acquired in 3232ms: lat: 36.1458306792511, lng: -86.8000220035813, accuracy: 8.01027344943496m, speed: 0.0m/s, altitude: 165.57015001169722m
flutter: 🗺️ LocationManager: Status changed from LocationStatus.determining to LocationStatus.available
flutter: 🗺️ LocationManager: Location success rate: 100.0% (5/5)
flutter: 🚀 MapScreen: Location permission requested
flutter: 🚀 MapScreen: Pin refreshing disabled - handled by SmartLocationFetchManager
flutter: 🚀 MapScreen: Full initialization completed in: 3293ms
flutter: MapProvider: Position update received - pin refresh handled by SmartLocationFetchManager
flutter: 📍 User avatar image already added, skipping
flutter: 📍 SmartFetch: Handling location update at 36.1458306792511, -86.8000220035813
flutter: 📍 SmartFetch: Fetch already in progress, ignoring location update
flutter: 🎵 [MapScreen] Build - currentlyPlayingPinId: null
flutter: 🎵 [MapScreen] Build - hasCurrentlyPlayingPin: false
flutter: 🎵 [MapScreen] _buildCaptionPopup called
flutter: 🎵 [MapScreen] - currentlyPlayingPinId: null
flutter: 🎵 [MapScreen] - currentlyPlayingPinData: null
flutter: 🎵 [MapScreen] ❌ No currently playing pin or pin data
flutter: NavigationHelper: Getting current tab...
flutter: NavigationHelper: Checking route - name: /map, looking for: /profile, match: false
flutter: NavigationHelper: Checking route - name: /map, looking for: /explore, match: false
flutter: NavigationHelper: Checking route - name: /map, looking for: /friends, match: false
flutter: NavigationHelper: Checking route - name: /map, looking for: /challenges, match: false
flutter: NavigationHelper: Checking route - name: /map, looking for: /map, match: true
flutter: NavigationHelper: Current tab is MAP (explicit)
flutter: 📍 Found 27 all pins (list)
flutter: 📍 SmartFetch: Successfully fetched 27 pins
flutter: 📍 SmartFetch: Received 27 pins from API
flutter: 📍 SmartFetch: Updating cache with 27 pins
flutter: 📍 SmartFetch: Cache before clear: 0 pins
flutter: 📍 SmartFetch: Cache after update: 27 pins
flutter: 📍 Updating map pins from cache (27 pins)
flutter: 📍 Using progressive rendering for 27 cached pins (0 already rendered)
flutter: 📍 🚀 Starting progressive pin rendering (context: RenderingContext.initialLoad)...
flutter: 📍 🏗️ Initializing empty layers for progressive rendering...
flutter: 📍 Cleared 0 pins and 0 clusters
flutter: 📍 🏗️ Initializing pin layers during map creation...
flutter: 📍 SmartFetch: Completed fetching pins
flutter: 📍 Waiting for smart fetch manager to complete...
flutter: 📍 ✅ Created empty individual pins layer
flutter: 📍 ✅ Created empty cluster layer
flutter: 🎯 User is within aura of 1 unique pins
flutter: 📍 ✅ Initialized empty glow sources
flutter: 📍 ✅ Pin layers initialized successfully during map creation
flutter: 📍 🏗️ Setting initial layer visibility: currentZoom=16.0, shouldCluster=false, threshold=14.0
flutter: 📍 ✅ Empty layers ready for progressive updates (showing individual pins)
flutter: 📍 🏗️ Layers initialized during progressive rendering (fallback)
flutter: 📍 _getCachedOrFetchPins called - returning cached pins only
flutter: 📍 Returning 27 cached pins
flutter: 📍 🔄 Cleared rendered pins for fresh initialLoad rendering
flutter: 📍 🎯 Sorting 27 pins by distance from user at (36.1458306792511, -86.8000220035813)
flutter: 📍 🎯 Sorted pins by distance - nearest: 28.5m, farthest: 3472.8m
flutter: 📍 🎯 First 8 pins for progressive rendering:
flutter: 📍 🎯   1. Pin 2239 at 29m
flutter: 📍 🎯   2. Pin 2232 at 147m
flutter: 📍 🎯   3. Pin 2240 at 378m
flutter: 📍 🎯   4. Pin 2251 at 563m
flutter: 📍 🎯   5. Pin 2312 at 719m
flutter: 📍 🎯   6. Pin 2277 at 745m
flutter: 📍 🎯   7. Pin 2252 at 836m
flutter: 📍 🎯   8. Pin 2250 at 991m
flutter: 📍 🚀 Added 27 pins to progressive rendering queue (sorted by distance)
flutter: 📍 🚀 Pin IDs: [2239, 2232, 2240, 2251, 2312, 2277, 2252, 2250, 2234, 2235, 2253, 2245, 2249, 2248, 2236, 2259, 2238, 2256, 2257, 2255, 2243, 2254, 2246, 2237, 2247, 2244, 2233]
flutter: 📍 🔄 Starting progressive rendering timer with 27 pending pins...
flutter: 📍 🔄 Current state: _isPinFetchInProgress=false
flutter: 📍 ⚡ Processing first batch immediately for instant feedback...
flutter: 📍 ⚡ Processing batch of 5 NEW pins (0 already rendered, 22 remaining)
flutter: 📍 ✅ Marked pin 2239 as rendered (1 total)
flutter: 📍 ✅ Marked pin 2232 as rendered (2 total)
flutter: 📍 ✅ Marked pin 2240 as rendered (3 total)
flutter: 📍 ✅ Marked pin 2251 as rendered (4 total)
flutter: 📍 ✅ Marked pin 2312 as rendered (5 total)
flutter: 📍 🔄 Building batch of 5 pins to BOTH layers in parallel
flutter: 📍 🔧 Computing pin features for 5 pins in parallel isolate
flutter: 🚀 Parallel pin feature isolate initialized successfully
flutter: 📊 Parallel processing: 5 pins in 0ms
flutter: 📊 Average processing time: 0ms per batch
flutter: 📊 Total pins processed: 5
flutter: 📍 🔧 Parallel isolate returned 5 features
flutter: 📍 🔧 Processing feature: 2239
flutter: 📍 ⏱️ Processing pin batch (22 pending, 5 rendered)
flutter: 📍 ⚡ Processing batch of 5 NEW pins (0 already rendered, 17 remaining)
flutter: 📍 ✅ Marked pin 2277 as rendered (6 total)
flutter: 📍 ✅ Marked pin 2252 as rendered (7 total)
flutter: 📍 ✅ Marked pin 2250 as rendered (8 total)
flutter: 📍 ✅ Marked pin 2234 as rendered (9 total)
flutter: 📍 ✅ Marked pin 2235 as rendered (10 total)
flutter: 📍 🔄 Building batch of 5 pins to BOTH layers in parallel
flutter: 📍 🔧 Computing pin features for 5 pins in parallel isolate
flutter: 📊 Parallel processing: 5 pins in 1ms
flutter: 📊 Average processing time: 0ms per batch
flutter: 📊 Total pins processed: 10
flutter: 📍 🔧 Parallel isolate returned 5 features
flutter: 📍 🔧 Processing feature: 2277
flutter: 📍 ⏱️ Processing pin batch (17 pending, 10 rendered)
flutter: 📍 ⚡ Processing batch of 5 NEW pins (0 already rendered, 12 remaining)
flutter: 📍 ✅ Marked pin 2253 as rendered (11 total)
flutter: 📍 ✅ Marked pin 2245 as rendered (12 total)
flutter: 📍 ✅ Marked pin 2249 as rendered (13 total)
flutter: 📍 ✅ Marked pin 2248 as rendered (14 total)
flutter: 📍 ✅ Marked pin 2236 as rendered (15 total)
flutter: 📍 🔄 Building batch of 5 pins to BOTH layers in parallel
flutter: 📍 🔧 Computing pin features for 5 pins in parallel isolate
flutter: 📊 Parallel processing: 5 pins in 0ms
flutter: 📊 Average processing time: 0ms per batch
flutter: 📊 Total pins processed: 15
flutter: 📍 🔧 Parallel isolate returned 5 features
flutter: 📍 🔧 Processing feature: 2253
flutter: 📍 ⏱️ Processing pin batch (12 pending, 15 rendered)
flutter: 📍 ⚡ Processing batch of 5 NEW pins (0 already rendered, 7 remaining)
flutter: 📍 ✅ Marked pin 2259 as rendered (16 total)
flutter: 📍 ✅ Marked pin 2238 as rendered (17 total)
flutter: 📍 ✅ Marked pin 2256 as rendered (18 total)
flutter: 📍 ✅ Marked pin 2257 as rendered (19 total)
flutter: 📍 ✅ Marked pin 2255 as rendered (20 total)
flutter: 📍 🔄 Building batch of 5 pins to BOTH layers in parallel
flutter: 📍 🔧 Computing pin features for 5 pins in parallel isolate
flutter: 📊 Parallel processing: 5 pins in 0ms
flutter: 📊 Average processing time: 0ms per batch
flutter: 📊 Total pins processed: 20
flutter: 📍 🔧 Parallel isolate returned 5 features
flutter: 📍 🔧 Processing feature: 2259
flutter: 📍 ⏱️ Processing pin batch (7 pending, 20 rendered)
flutter: 📍 ⚡ Processing batch of 5 NEW pins (0 already rendered, 2 remaining)
flutter: 📍 ✅ Marked pin 2243 as rendered (21 total)
flutter: 📍 ✅ Marked pin 2254 as rendered (22 total)
flutter: 📍 ✅ Marked pin 2246 as rendered (23 total)
flutter: 📍 ✅ Marked pin 2237 as rendered (24 total)
flutter: 📍 ✅ Marked pin 2247 as rendered (25 total)
flutter: 📍 🔄 Building batch of 5 pins to BOTH layers in parallel
flutter: 📍 🔧 Computing pin features for 5 pins in parallel isolate
flutter: 📊 Parallel processing: 5 pins in 0ms
flutter: 📊 Average processing time: 0ms per batch
flutter: 📊 Total pins processed: 25
flutter: 📍 🔧 Parallel isolate returned 5 features
flutter: 📍 🔧 Processing feature: 2243
flutter: 📍 ⏱️ Processing pin batch (2 pending, 25 rendered)
flutter: 📍 ⚡ Processing batch of 2 NEW pins (0 already rendered, 0 remaining)
flutter: 📍 ✅ Marked pin 2244 as rendered (26 total)
flutter: 📍 ✅ Marked pin 2233 as rendered (27 total)
flutter: 📍 🔄 Building batch of 2 pins to BOTH layers in parallel
flutter: 📍 🔧 Computing pin features for 2 pins in parallel isolate
flutter: 📊 Parallel processing: 2 pins in 0ms
flutter: 📊 Average processing time: 0ms per batch
flutter: 📊 Total pins processed: 27
flutter: 📍 🔧 Parallel isolate returned 2 features
flutter: 📍 🔧 Processing feature: 2244
flutter: 📍 ✅ Progressive rendering timer completed, all pins processed
flutter: 📍 🎉 Progressive rendering complete! Rendered 27 pins
flutter: 📍 🎉 Rendered pin IDs: [2239, 2232, 2240, 2251, 2312, 2277, 2252, 2250, 2234, 2235, 2253, 2245, 2249, 2248, 2236, 2259, 2238, 2256, 2257, 2255, 2243, 2254, 2246, 2237, 2247, 2244, 2233]
flutter: 📍 🎉 Cache size: 27, Real pin data size: 27
flutter: 🔍 Stopping scanning animation
flutter: 🔍 Removed scanning layers
flutter: 📍 🔧 Building cluster layer with all 27 rendered pins
flutter: 🚀 Cluster isolate initialized successfully
flutter: 📍 🔍 About to update cluster layer with 27 items
flutter: 📍 🔍 - Clusters: 9
flutter: 📍 🔍 - Individual pins: 18
flutter: 🎯 Added 1 new cluster icons: {}
flutter: 📍 🎯 Updating cluster layer: 27 items, 0 existing pins, 0 existing clusters
flutter: 📍 🎯 Adding/updating cluster feature: cluster_36.14634616693229_-86.79971528426131 with 4 pins
flutter: 📍 🎯 Adding NEW individual pin feature in cluster view: 2240
flutter: 📊 Parallel processing: 1 pins in 0ms
flutter: 📊 Average processing time: 0ms per batch
flutter: 📊 Total pins processed: 28
flutter: 📍 🎯 Adding NEW individual pin feature in cluster view: 2240
flutter: 📊 Parallel processing: 1 pins in 0ms
flutter: 📊 Average processing time: 0ms per batch
flutter: 📊 Total pins processed: 29
flutter: 📍 🎯 Adding NEW individual pin feature in cluster view: 2251
flutter: 📊 Parallel processing: 1 pins in 0ms
flutter: 📊 Average processing time: 0ms per batch
flutter: 📊 Total pins processed: 30
flutter: 📍 🎯 Adding NEW individual pin feature in cluster view: 2251
flutter: 📊 Parallel processing: 1 pins in 0ms
flutter: 📊 Average processing time: 0ms per batch
flutter: 📊 Total pins processed: 31
flutter: 📍 🎯 Adding/updating cluster feature: cluster_36.14674000710197_-86.80808779011457 with 4 pins
flutter: 📍 🎯 Adding NEW individual pin feature in cluster view: 2252
flutter: 📊 Parallel processing: 1 pins in 0ms
flutter: 📊 Average processing time: 0ms per batch
flutter: 📊 Total pins processed: 32
flutter: 📍 🎯 Adding NEW individual pin feature in cluster view: 2252
flutter: 📊 Parallel processing: 1 pins in 0ms
flutter: 📊 Average processing time: 0ms per batch
flutter: 📊 Total pins processed: 33
flutter: 📍 🎯 Adding/updating cluster feature: cluster_36.14539194670232_-86.81152344455455 with 4 pins
flutter: 📍 🎯 Adding NEW individual pin feature in cluster view: 2234
flutter: 📊 Parallel processing: 1 pins in 0ms
flutter: 📊 Average processing time: 0ms per batch
flutter: 📊 Total pins processed: 34
flutter: 📍 🎯 Adding NEW individual pin feature in cluster view: 2234
flutter: 📊 Parallel processing: 1 pins in 0ms
flutter: 📊 Average processing time: 0ms per batch
flutter: 📊 Total pins processed: 35
flutter: 📍 🎯 Adding/updating cluster feature: cluster_36.15198825029742_-86.78880170385645 with 4 pins
flutter: 📍 🎯 Adding/updating cluster feature: cluster_36.14928063459031_-86.8130905819607 with 4 pins
flutter: 📍 🎯 Adding/updating cluster feature: cluster_36.132290310667166_-86.79581988821178 with 4 pins
flutter: 📍 🎯 Adding NEW individual pin feature in cluster view: 2238
flutter: 📊 Parallel processing: 1 pins in 0ms
flutter: 📊 Average processing time: 0ms per batch
flutter: 📊 Total pins processed: 36
flutter: 📍 🎯 Adding NEW individual pin feature in cluster view: 2238
flutter: 📊 Parallel processing: 1 pins in 0ms
flutter: 📊 Average processing time: 0ms per batch
flutter: 📊 Total pins processed: 37
flutter: 📍 🎯 Adding NEW individual pin feature in cluster view: 2256
flutter: 📊 Parallel processing: 1 pins in 0ms
flutter: 📊 Average processing time: 0ms per batch
flutter: 📊 Total pins processed: 38
flutter: 📍 🎯 Adding NEW individual pin feature in cluster view: 2256
flutter: 📊 Parallel processing: 1 pins in 0ms
flutter: 📊 Average processing time: 0ms per batch
flutter: 📊 Total pins processed: 39
flutter: 📍 🎯 Adding NEW individual pin feature in cluster view: 2257
flutter: 📊 Parallel processing: 1 pins in 0ms
flutter: 📊 Average processing time: 0ms per batch
flutter: 📊 Total pins processed: 40
flutter: 📍 🎯 Adding NEW individual pin feature in cluster view: 2257
flutter: 📊 Parallel processing: 1 pins in 0ms
flutter: 📊 Average processing time: 0ms per batch
flutter: 📊 Total pins processed: 41
flutter: 📍 🎯 Adding/updating cluster feature: cluster_36.162515142452534_-86.78295829992003 with 4 pins
flutter: 📍 🎯 Adding NEW individual pin feature in cluster view: 2243
flutter: 📊 Parallel processing: 1 pins in 0ms
flutter: 📊 Average processing time: 0ms per batch
flutter: 📊 Total pins processed: 42
flutter: 📍 🎯 Adding NEW individual pin feature in cluster view: 2243
flutter: 📊 Parallel processing: 1 pins in 0ms
flutter: 📊 Average processing time: 0ms per batch
flutter: 📊 Total pins processed: 43
flutter: 📍 🎯 Adding/updating cluster feature: cluster_36.16150712322758_-86.77869490277848 with 4 pins
flutter: 📍 🎯 Adding/updating cluster feature: cluster_36.17022012488388_-86.78788971665182 with 4 pins
flutter: 📍 🎯 Adding NEW individual pin feature in cluster view: 2233
flutter: 📊 Parallel processing: 1 pins in 0ms
flutter: 📊 Average processing time: 0ms per batch
flutter: 📊 Total pins processed: 44
flutter: 📍 🎯 Adding NEW individual pin feature in cluster view: 2233
flutter: 📊 Parallel processing: 1 pins in 0ms
flutter: 📊 Average processing time: 0ms per batch
flutter: 📊 Total pins processed: 45
flutter: 📍 🔧 Added 18/27 new features to cluster-pins-source (total: 18 unique)
flutter: 📍 🎯 Incremental cluster layer update: 27 new features
flutter: 📍 ✅ Cluster layer updated with 18 total features (27 new)
flutter: 📍 🎯 Updating cluster symbols: 0 existing → 9 new
flutter: 📍 🎯 Updated cluster symbols: 9 symbols for tap handling
flutter: 📍 ✅ Updated cluster layer with 27 new pins (total pins: 54, clusters: 9)
flutter: 📍 ✅ Cluster layer built with 27 pins
flutter: 📍 🔧 Final layer visibility update: shouldCluster=false, renderedPins=27
flutter: 📍 ✅ Final layer visibility set successfully
flutter: 📍 Skipping glow animation during initial load
flutter: 📍 Skipping pin bounce animation during initial load
flutter: 📍 Initial pin load completed with progressive rendering
flutter: 📍 Pin state change: Pins: 0→0, Clusters: 0→9
flutter: 📍 ✅ Pin count validation passed: 27 pins rendered
flutter: 📍 Progressive rendering completed in 256ms
flutter: 📍 🎯 Progressive rendering completion state: shouldCluster=false, currentlyShowingClusters=false
flutter: 📍 🎯 Current zoom from controller: 16.0, max zoom for clustering: 14.0
flutter: 📍 🎯 Rendered pins: 27, cache size: 27
flutter: 📍 🔄 Progressive rendering complete - ensuring proper layer visibility
flutter: 📍 🔄 Current zoom: 16.0, should cluster: false, currently showing clusters: false
flutter: 📍 ✅ Progressive rendering complete - layers already built, just switching visibility
flutter: 📍 Enhanced layer switching: showClusters=false
flutter: 📍 Current zoom: 16.0, clustering threshold: 14.0
flutter: 📍 Current layer state: LayerState.initializing
flutter: 📍 Progressive rendering check: inProgress=false, enabled=true, pending=0
flutter: 📍 State transition requested: LayerState.initializing → LayerState.showingIndividual
flutter: 📍 Layer state changed: LayerState.initializing → LayerState.transitioning
flutter: 📍 State updated: LayerState.initializing → LayerState.transitioning
NSPredicate: Use of 'mgl_does:have:' as an NSExpression function is forbidden.
NSPredicate: Use of 'mgl_does:have:' as an NSExpression function is forbidden.
NSPredicate: Use of 'mgl_does:have:' as an NSExpression function is forbidden.
NSPredicate: Use of 'mgl_does:have:' as an NSExpression function is forbidden.
flutter: 📍 Layer state changed: LayerState.transitioning → LayerState.showingIndividual
flutter: 📍 State updated: LayerState.transitioning → LayerState.showingIndividual
flutter: 📍 ✅ State transition successful: LayerState.showingIndividual
flutter: 📍 ✅ Showing individual layers, hiding cluster layers
flutter: 📍 ⚡ Layer switch completed in 3ms (parallelized)
flutter: 📍 🔍 Validating layer visibility: individual=true, cluster=false
flutter: 📍 ✅ Layer visibility validated and corrected if needed
flutter: 📍 ✅ Progressive rendering layer visibility set: clusters=false
flutter: 📍 Found 27 pins immediately from cache
flutter: 📍 Cleared layer-based approach
flutter: 📍 Cleared 0 pins and 9 clusters
flutter: 🔄 ===== SYNCING FILTERED PINS TO CACHE =====
flutter: 🔄 Syncing 27 filtered pins to cache
flutter: 🔄 Pin 2239 already has coordinates: lat=36.145775257195865, lng=-86.80033198630963
flutter: 🔄 ✅ Cached filtered pin: 2239 (lat=36.145775257195865, lng=-86.80033198630963)
flutter: 🔄 Pin 2232 already has coordinates: lat=36.14691707666871, lng=-86.79909858221296
flutter: 🔄 ✅ Cached filtered pin: 2232 (lat=36.14691707666871, lng=-86.79909858221296)
flutter: 🔄 Pin 2240 already has coordinates: lat=36.1431523311605, lng=-86.802599235324
flutter: 🔄 ✅ Cached filtered pin: 2240 (lat=36.1431523311605, lng=-86.802599235324)
flutter: 🔄 Pin 2251 already has coordinates: lat=36.15059702869148, lng=-86.80211380314252
flutter: 🔄 ✅ Cached filtered pin: 2251 (lat=36.15059702869148, lng=-86.80211380314252)
flutter: 🔄 Pin 2312 already has coordinates: lat=36.146753416629714, lng=-86.80793968449623
flutter: 🔄 ✅ Cached filtered pin: 2312 (lat=36.146753416629714, lng=-86.80793968449623)
flutter: 🔄 Pin 2277 already has coordinates: lat=36.146726597574215, lng=-86.80823589573289
flutter: 🔄 ✅ Cached filtered pin: 2277 (lat=36.146726597574215, lng=-86.80823589573289)
flutter: 🔄 Pin 2252 already has coordinates: lat=36.152118501372144, lng=-86.79494571613948
flutter: 🔄 ✅ Cached filtered pin: 2252 (lat=36.152118501372144, lng=-86.79494571613948)
flutter: 🔄 Pin 2250 already has coordinates: lat=36.1448614611531, lng=-86.81098073677607
flutter: 🔄 ✅ Cached filtered pin: 2250 (lat=36.1448614611531, lng=-86.81098073677607)
flutter: 🔄 Pin 2234 already has coordinates: lat=36.13630787405521, lng=-86.80067629406697
flutter: 🔄 ✅ Cached filtered pin: 2234 (lat=36.13630787405521, lng=-86.80067629406697)
flutter: 🔄 Pin 2235 already has coordinates: lat=36.14592243225154, lng=-86.81206615233302
flutter: 🔄 ✅ Cached filtered pin: 2235 (lat=36.14592243225154, lng=-86.81206615233302)
flutter: 🔄 Pin 2253 already has coordinates: lat=36.15131638804406, lng=-86.78894239678924
flutter: 🔄 ✅ Cached filtered pin: 2253 (lat=36.15131638804406, lng=-86.78894239678924)
flutter: 🔄 Pin 2245 already has coordinates: lat=36.14888960995274, lng=-86.81275678529597
flutter: 🔄 ✅ Cached filtered pin: 2245 (lat=36.14888960995274, lng=-86.81275678529597)
flutter: 🔄 Pin 2249 already has coordinates: lat=36.15266011255079, lng=-86.78866101092366
flutter: 🔄 ✅ Cached filtered pin: 2249 (lat=36.15266011255079, lng=-86.78866101092366)
flutter: 🔄 Pin 2248 already has coordinates: lat=36.14967165922789, lng=-86.81342437862543
flutter: 🔄 ✅ Cached filtered pin: 2248 (lat=36.14967165922789, lng=-86.81342437862543)
flutter: 🔄 Pin 2236 already has coordinates: lat=36.13230298457076, lng=-86.7957955347335
flutter: 🔄 ✅ Cached filtered pin: 2236 (lat=36.13230298457076, lng=-86.7957955347335)
flutter: 🔄 Pin 2259 already has coordinates: lat=36.132277636763575, lng=-86.79584424169008
flutter: 🔄 ✅ Cached filtered pin: 2259 (lat=36.132277636763575, lng=-86.79584424169008)
flutter: 🔄 Pin 2238 already has coordinates: lat=36.156119091070565, lng=-86.78750137549608
flutter: 🔄 ✅ Cached filtered pin: 2238 (lat=36.156119091070565, lng=-86.78750137549608)
flutter: 🔄 Pin 2256 already has coordinates: lat=36.15730982457368, lng=-86.78479404305152
flutter: 🔄 ✅ Cached filtered pin: 2256 (lat=36.15730982457368, lng=-86.78479404305152)
flutter: 🔄 Pin 2257 already has coordinates: lat=36.14648635275807, lng=-86.77551453760935
flutter: 🔄 ✅ Cached filtered pin: 2257 (lat=36.14648635275807, lng=-86.77551453760935)
flutter: 🔄 Pin 2255 already has coordinates: lat=36.16206183354287, lng=-86.78296421742428
flutter: 🔄 ✅ Cached filtered pin: 2255 (lat=36.16206183354287, lng=-86.78296421742428)
flutter: 🔄 Pin 2243 already has coordinates: lat=36.1671494340818, lng=-86.8033868457519
flutter: 🔄 ✅ Cached filtered pin: 2243 (lat=36.1671494340818, lng=-86.8033868457519)
flutter: 🔄 Pin 2254 already has coordinates: lat=36.16296845136219, lng=-86.78295238241579
flutter: 🔄 ✅ Cached filtered pin: 2254 (lat=36.16296845136219, lng=-86.78295238241579)
flutter: 🔄 Pin 2246 already has coordinates: lat=36.161170796687266, lng=-86.77835643551977
flutter: 🔄 ✅ Cached filtered pin: 2246 (lat=36.161170796687266, lng=-86.77835643551977)
flutter: 🔄 Pin 2237 already has coordinates: lat=36.1618434497679, lng=-86.77903337003718
flutter: 🔄 ✅ Cached filtered pin: 2237 (lat=36.1618434497679, lng=-86.77903337003718)
flutter: 🔄 Pin 2247 already has coordinates: lat=36.169538259950706, lng=-86.78825775587553
flutter: 🔄 ✅ Cached filtered pin: 2247 (lat=36.169538259950706, lng=-86.78825775587553)
flutter: 🔄 Pin 2244 already has coordinates: lat=36.17090198981707, lng=-86.78752167742809
flutter: 🔄 ✅ Cached filtered pin: 2244 (lat=36.17090198981707, lng=-86.78752167742809)
flutter: 🔄 Pin 2233 already has coordinates: lat=36.17542840930612, lng=-86.78781044398772
flutter: 🔄 ✅ Cached filtered pin: 2233 (lat=36.17542840930612, lng=-86.78781044398772)
flutter: 🔄 Final cache size: 27
flutter: 🔄 ===== FILTER SYNC COMPLETE =====
flutter: 📍 Using progressive rendering for filter with 27 pins
flutter: 📍 🏗️ Initializing empty layers for progressive rendering...
flutter: 📍 Cleared 0 pins and 0 clusters
flutter: 📍 🏗️ Initializing pin layers during map creation...
flutter: 📍 ✅ Created empty individual pins layer
flutter: 📍 ✅ Created empty cluster layer
flutter: 📍 ✅ Initialized empty glow sources
flutter: 📍 ✅ Pin layers initialized successfully during map creation
flutter: 📍 🏗️ Setting initial layer visibility: currentZoom=16.0, shouldCluster=false, threshold=14.0
flutter: 📍 ✅ Empty layers ready for progressive updates (showing individual pins)
flutter: 📍 🔄 Filter using same 27 pins that are already rendered, skipping progressive rendering
flutter: 📍 🔧 Added custom skin for pin 2259: pin-skin-2259
flutter: 📍 🔧 Updated real pin data for: 2259
flutter: 📍 🔧 Processing feature: 2238
flutter: 📍 🔧 Added custom skin for pin 2243: pin-skin-2243
flutter: 📍 🔧 Updated real pin data for: 2243
flutter: 📍 🔧 Processing feature: 2254
flutter: 📍 🔍 Validating layer visibility: individual=true, cluster=false
flutter: 📍 ✅ Layer visibility validated and corrected if needed
flutter: 📍 🔧 Added custom skin for pin 2239: pin-skin-2239
flutter: 📍 🔧 Updated real pin data for: 2239
flutter: 📍 🔧 Processing feature: 2232
flutter: 📍 🔧 Added custom skin for pin 2244: pin-skin-2244
flutter: 📍 🔧 Updated real pin data for: 2244
flutter: 📍 🔧 Processing feature: 2233
flutter: 📍 🔧 Added custom skin for pin 2277: pin-skin-2277
flutter: 📍 🔧 Updated real pin data for: 2277
flutter: 📍 🔧 Processing feature: 2252
flutter: 📍 🔧 Added custom skin for pin 2253: pin-skin-2253
flutter: 📍 🔧 Updated real pin data for: 2253
flutter: 📍 🔧 Processing feature: 2245
flutter: 📍 🔧 Added custom skin for pin 2238: pin-skin-2238
flutter: 📍 🔧 Updated real pin data for: 2238
flutter: 📍 🔧 Processing feature: 2256
flutter: 📍 🔧 Added custom skin for pin 2254: pin-skin-2254
flutter: 📍 🔧 Updated real pin data for: 2254
flutter: 📍 🔧 Processing feature: 2246
flutter: 📍 🔍 Validating current state: LayerState.showingIndividual
flutter: 📍 Pin state change: Pins: 0→0, Clusters: 9→0
flutter: 📍 ⚠️ WARNING: Have cached data (27 pins) but no pins visible!
flutter: 📍 ⚠️ This suggests pins were cleared but not restored properly
flutter: 📍 ✅ State validation passed
flutter: 📍 🔧 Added custom skin for pin 2252: pin-skin-2252
flutter: 📍 🔧 Updated real pin data for: 2252
flutter: 📍 🔧 Processing feature: 2250
flutter: 📍 🔧 Added custom skin for pin 2232: pin-skin-2232
flutter: 📍 🔧 Updated real pin data for: 2232
flutter: 📍 🔧 Processing feature: 2240
flutter: 📍 🔧 Added custom skin for pin 2245: pin-skin-2245
flutter: 📍 🔧 Updated real pin data for: 2245
flutter: 📍 🔧 Processing feature: 2249
flutter: 📍 🔧 Added custom skin for pin 2256: pin-skin-2256
flutter: 📍 🔧 Updated real pin data for: 2256
flutter: 📍 🔧 Processing feature: 2257
flutter: Error adding skin image: ClientException with SocketException: Failed host lookup: 'via.placeholder.com' (OS Error: nodename nor servname provided, or not known, errno = 8), uri=https://via.placeholder.com/256x256/orange/ffffff?text=Warm+Orange+House
flutter: 📍 🔧 Updated real pin data for: 2257
flutter: 📍 🔧 Processing feature: 2255
flutter: 📍 🔧 Added custom skin for pin 2246: pin-skin-2246
flutter: 📍 🔧 Updated real pin data for: 2246
flutter: 📍 🔧 Processing feature: 2237
flutter: 📍 🔧 Added custom skin for pin 2233: pin-skin-2233
flutter: 📍 🔧 Updated real pin data for: 2233
flutter: 📍 🔧 Added 2/2 new features to individual-pins-source (total: 2 unique)
flutter: 📍 ✅ Individual pins layer made visible for batch of 2 pins
flutter: 📍 ✅ Added 2 pins to individual layer (total: 2)
flutter: 📍 ✅ Processed pin IDs: [2244, 2233]
flutter: 📍 ✅ Layer refreshed for immediate pin visibility
flutter: 📍 ✨ Adding incremental glow effects for batch of 2 pins
flutter: 🌟 Added 4 features to glow-polygons-source memory (total: 4)
flutter: 🌟 Added 2 features to border-polygons-source memory (total: 2)
flutter: 🌟 Added 2 features to pulse-polygons-source memory (total: 2)
flutter: 🌟 Updated existing glow sources with 4 glow features (accumulated across batches)
flutter: 🌟 Added incremental glow effects for 2 pins
flutter: 📍 ✨ Incremental glow effects added for batch
flutter: 📍 ✅ Progressive: Individual layer updated for batch of 2 pins
flutter: 📍 ✨ Incremental display: Refreshed layer visibility after batch (showing individual pins)
flutter: 📍 ⚡ Batch processed - updating layer visibility immediately: shouldCluster=false, current zoom=16.0 (2 pins added, 27 total)
flutter: 📍 ✅ Layer visibility updated successfully for batch
flutter: 📍 🔧 Added custom skin for pin 2250: pin-skin-2250
flutter: 📍 🔧 Updated real pin data for: 2250
flutter: 📍 🔧 Processing feature: 2234
flutter: 📍 🔧 Added custom skin for pin 2240: pin-skin-2240
flutter: 📍 🔧 Updated real pin data for: 2240
flutter: 📍 🔧 Processing feature: 2251
flutter: 📍 🔧 Added custom skin for pin 2249: pin-skin-2249
flutter: 📍 🔧 Updated real pin data for: 2249
flutter: 📍 🔧 Processing feature: 2248
flutter: 📍 🔧 Added custom skin for pin 2255: pin-skin-2255
flutter: 📍 🔧 Updated real pin data for: 2255
flutter: 📍 🔧 Added 5/5 new features to individual-pins-source (total: 7 unique)
flutter: 📍 ✅ Individual pins layer made visible for batch of 5 pins
flutter: 📍 ✅ Added 5 pins to individual layer (total: 7)
flutter: 📍 ✅ Processed pin IDs: [2259, 2238, 2256, 2257, 2255]
flutter: 📍 ✅ Layer refreshed for immediate pin visibility
flutter: 📍 ✨ Adding incremental glow effects for batch of 5 pins
flutter: 🌟 Added 10 features to glow-polygons-source memory (total: 14)
flutter: 🌟 Added 5 features to border-polygons-source memory (total: 7)
flutter: 🌟 Added 5 features to pulse-polygons-source memory (total: 7)
flutter: 📍 🔧 Added custom skin for pin 2237: pin-skin-2237
flutter: 📍 🔧 Updated real pin data for: 2237
flutter: 📍 🔧 Processing feature: 2247
flutter: 🌟 Updated existing glow sources with 10 glow features (accumulated across batches)
flutter: 🌟 Added incremental glow effects for 5 pins
flutter: 📍 ✨ Incremental glow effects added for batch
flutter: 📍 ✅ Progressive: Individual layer updated for batch of 5 pins
flutter: 📍 ✨ Incremental display: Refreshed layer visibility after batch (showing individual pins)
flutter: 📍 ⚡ Batch processed - updating layer visibility immediately: shouldCluster=false, current zoom=16.0 (5 pins added, 27 total)
flutter: 📍 ✅ Layer visibility updated successfully for batch
flutter: 📍 🔧 Added custom skin for pin 2234: pin-skin-2234
flutter: 📍 🔧 Updated real pin data for: 2234
flutter: 📍 🔧 Processing feature: 2235
flutter: 📍 🔧 Added custom skin for pin 2248: pin-skin-2248
flutter: 📍 🔧 Updated real pin data for: 2248
flutter: 📍 🔧 Processing feature: 2236
flutter: 📍 🔧 Added custom skin for pin 2251: pin-skin-2251
flutter: 📍 🔧 Updated real pin data for: 2251
flutter: 📍 🔧 Processing feature: 2312
flutter: 📍 🔧 Added custom skin for pin 2247: pin-skin-2247
flutter: 📍 🔧 Updated real pin data for: 2247
flutter: 📍 🔧 Added 5/5 new features to individual-pins-source (total: 12 unique)
flutter: 📍 ✅ Individual pins layer made visible for batch of 5 pins
flutter: 📍 ✅ Added 5 pins to individual layer (total: 12)
flutter: 📍 ✅ Processed pin IDs: [2243, 2254, 2246, 2237, 2247]
flutter: 📍 ✅ Layer refreshed for immediate pin visibility
flutter: 📍 ✨ Adding incremental glow effects for batch of 5 pins
flutter: 🌟 Added 10 features to glow-polygons-source memory (total: 24)
flutter: 🌟 Added 5 features to border-polygons-source memory (total: 12)
flutter: 🌟 Added 5 features to pulse-polygons-source memory (total: 12)
flutter: 🌟 Updated existing glow sources with 10 glow features (accumulated across batches)
flutter: 🌟 Added incremental glow effects for 5 pins
flutter: 📍 ✨ Incremental glow effects added for batch
flutter: 📍 ✅ Progressive: Individual layer updated for batch of 5 pins
flutter: 📍 ✨ Incremental display: Refreshed layer visibility after batch (showing individual pins)
flutter: 📍 ⚡ Batch processed - updating layer visibility immediately: shouldCluster=false, current zoom=16.0 (5 pins added, 27 total)
flutter: 📍 ✅ Layer visibility updated successfully for batch
flutter: 📍 🔧 Added custom skin for pin 2235: pin-skin-2235
flutter: 📍 🔧 Updated real pin data for: 2235
flutter: 📍 🔧 Added 5/5 new features to individual-pins-source (total: 17 unique)
flutter: 📍 ✅ Individual pins layer made visible for batch of 5 pins
flutter: 📍 ✅ Added 5 pins to individual layer (total: 17)
flutter: 📍 ✅ Processed pin IDs: [2277, 2252, 2250, 2234, 2235]
flutter: 📍 ✅ Layer refreshed for immediate pin visibility
flutter: 📍 ✨ Adding incremental glow effects for batch of 5 pins
flutter: 🌟 Added 10 features to glow-polygons-source memory (total: 34)
flutter: 🌟 Added 5 features to border-polygons-source memory (total: 17)
flutter: 🌟 Added 5 features to pulse-polygons-source memory (total: 17)
flutter: 🌟 Updated existing glow sources with 10 glow features (accumulated across batches)
flutter: 🌟 Added incremental glow effects for 5 pins
flutter: 📍 ✨ Incremental glow effects added for batch
flutter: 📍 ✅ Progressive: Individual layer updated for batch of 5 pins
flutter: 📍 ✨ Incremental display: Refreshed layer visibility after batch (showing individual pins)
flutter: 📍 ⚡ Batch processed - updating layer visibility immediately: shouldCluster=false, current zoom=16.0 (5 pins added, 27 total)
flutter: 📍 ✅ Layer visibility updated successfully for batch
flutter: 📍 🔧 Added custom skin for pin 2312: pin-skin-2312
flutter: 📍 🔧 Updated real pin data for: 2312
flutter: 📍 🔧 Added 5/5 new features to individual-pins-source (total: 22 unique)
flutter: 📍 ✅ Individual pins layer made visible for batch of 5 pins
flutter: 📍 ✅ Added 5 pins to individual layer (total: 22)
flutter: 📍 ✅ Processed pin IDs: [2239, 2232, 2240, 2251, 2312]
flutter: 📍 ✅ Layer refreshed for immediate pin visibility
flutter: 📍 ✨ Adding incremental glow effects for batch of 5 pins
flutter: 🌟 Added 10 features to glow-polygons-source memory (total: 44)
flutter: 🌟 Added 5 features to border-polygons-source memory (total: 22)
flutter: 🌟 Added 5 features to pulse-polygons-source memory (total: 22)
flutter: 🌟 Updated existing glow sources with 10 glow features (accumulated across batches)
flutter: 🌟 Added incremental glow effects for 5 pins
flutter: 📍 ✨ Incremental glow effects added for batch
flutter: 📍 ✅ Progressive: Individual layer updated for batch of 5 pins
flutter: 📍 ✨ Incremental display: Refreshed layer visibility after batch (showing individual pins)
flutter: 📍 ⚡ Batch processed - updating layer visibility immediately: shouldCluster=false, current zoom=16.0 (5 pins added, 27 total)
flutter: 📍 ✅ Layer visibility updated successfully for batch
flutter: 📍 🔧 Added custom skin for pin 2236: pin-skin-2236
flutter: 📍 🔧 Updated real pin data for: 2236
flutter: 📍 🔧 Added 5/5 new features to individual-pins-source (total: 27 unique)
